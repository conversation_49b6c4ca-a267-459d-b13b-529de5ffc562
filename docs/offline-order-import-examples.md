# 线下渠道订单Excel导入示例

## Excel文件示例

### 示例数据

| 试剂盒编号 | 商品名称 | 商品规格 |
|-----------|---------|---------|
| KIT001    | 宠物基因检测套餐A | 毛色检测 |
| KIT002    | 宠物基因检测套餐A | 毛色检测，疾病筛查 |
| KIT003    | 宠物基因检测套餐B | 品种鉴定，疾病筛查，性格分析 |
| KIT004    | 宠物健康检测套餐 | 基础检测 |

## 导入结果示例

### 成功导入的响应
```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "successCount": 4,
    "failCount": 0,
    "errorMessages": []
  }
}
```

### 部分失败的响应
```json
{
  "code": 200,
  "message": "导入完成，但存在部分错误",
  "data": {
    "successCount": 2,
    "failCount": 2,
    "errorMessages": [
      "第3行：试剂盒编号[KIT003]已存在",
      "第4行：商品[不存在的商品]不存在"
    ]
  }
}
```

## 常见错误示例

### 1. Excel内部试剂盒编号重复
**Excel数据：**
| 试剂盒编号 | 商品名称 | 商品规格 |
|-----------|---------|---------|
| KIT001    | 商品A   | 规格1   |
| KIT001    | 商品B   | 规格2   |

**错误信息：**
```
第2行：试剂盒编号[KIT001]在Excel中重复
```

### 2. 数据库中试剂盒编号已存在
**Excel数据：**
| 试剂盒编号 | 商品名称 | 商品规格 |
|-----------|---------|---------|
| KIT001    | 商品A   | 规格1   |

**错误信息：**（假设KIT001在数据库中已存在）
```
第1行：试剂盒编号[KIT001]在数据库中已存在
```

### 3. 商品不存在
**Excel数据：**
| 试剂盒编号 | 商品名称 | 商品规格 |
|-----------|---------|---------|
| KIT001    | 不存在的商品 | 规格1   |

**错误信息：**
```
第1行：商品[不存在的商品]不存在
```

### 4. 商品规格不存在
**Excel数据：**
| 试剂盒编号 | 商品名称 | 商品规格 |
|-----------|---------|---------|
| KIT001    | 商品A   | 不存在的规格   |

**错误信息：**
```
第1行：商品[商品A]的规格[不存在的规格]不存在
```

### 4. 多规格中部分不存在
**Excel数据：**
| 试剂盒编号 | 商品名称 | 商品规格 |
|-----------|---------|---------|
| KIT001    | 商品A   | 规格1，不存在的规格，规格3   |

**错误信息：**
```
第1行：商品[商品A]的规格[不存在的规格]不存在
```

### 5. 必填字段为空
**Excel数据：**
| 试剂盒编号 | 商品名称 | 商品规格 |
|-----------|---------|---------|
|           | 商品A   | 规格1   |
| KIT002    |         | 规格2   |
| KIT003    | 商品C   |         |

**错误信息：**
```
第1行：试剂盒编号不能为空
第2行：商品名称不能为空
第3行：商品规格不能为空
```

## 数据库存储示例

### 单个规格的存储
**Excel输入：**
```
试剂盒编号: KIT001
商品名称: 宠物基因检测套餐
商品规格: 毛色检测
```

**数据库存储：**
```sql
INSERT INTO app_offline_order (
    kits_no, 
    goods_id, 
    goods_specs, 
    goods_specs_name, 
    status
) VALUES (
    'KIT001', 
    1, 
    '5', 
    '毛色检测', 
    0
);
```

### 多个规格的存储
**Excel输入：**
```
试剂盒编号: KIT002
商品名称: 宠物基因检测套餐
商品规格: 毛色检测，疾病筛查，品种鉴定
```

**数据库存储：**
```sql
INSERT INTO app_offline_order (
    kits_no, 
    goods_id, 
    goods_specs, 
    goods_specs_name, 
    status
) VALUES (
    'KIT002', 
    1, 
    '5,6,7', 
    '毛色检测，疾病筛查，品种鉴定', 
    0
);
```

## API调用示例

### 使用curl上传Excel文件
```bash
curl -X POST \
  http://localhost:8080/api/admin/appOfflineOrder/import \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@offline_orders.xlsx'
```

### 下载模板文件
```bash
curl -X GET \
  http://localhost:8080/api/admin/appOfflineOrder/template \
  -o template.xlsx
```

## 最佳实践

### 1. 数据准备
- 在导入前，确保所有商品和规格都已在系统中创建
- 使用模板文件确保格式正确
- 检查试剂盒编号的唯一性

### 2. 批量导入
- 建议每次导入不超过1000条记录
- 大批量数据可以分多个文件导入
- 导入前备份现有数据

### 3. 错误处理
- 仔细查看错误信息，根据行号定位问题
- 修正错误后可以重新导入失败的记录
- 保留导入日志以便问题追踪

### 4. 规格格式
- 多个规格必须使用中文逗号（，）分割
- 规格名称前后的空格会被自动去除
- 确保所有规格都属于对应的商品
