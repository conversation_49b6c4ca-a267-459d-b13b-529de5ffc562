# 线下渠道订单Excel导入功能

## 功能概述

本功能实现了线下渠道订单表的Excel批量导入，支持通过Excel文件批量创建线下渠道订单记录。

## 功能特性

1. **数据校验**：
   - 试剂盒编号唯一性校验（Excel内部 + 数据库）
   - 商品名称存在性校验
   - 商品规格存在性校验
   - 必填字段校验

2. **批量处理**：
   - 支持大批量数据导入
   - 批量保存提高性能
   - 详细的导入结果反馈

3. **错误处理**：
   - 详细的错误信息提示
   - 行号定位错误位置
   - 部分成功部分失败的处理

## API接口

### 1. 导入Excel文件

**接口地址**：`POST /api/admin/appOfflineOrder/import`

**请求参数**：
- `file`：MultipartFile类型，Excel文件（支持.xlsx和.xls格式）

**响应格式**：
```json
{
  "code": 200,
  "message": "导入成功",
  "data": {
    "successCount": 10,
    "failCount": 2,
    "errorMessages": [
      "第3行：试剂盒编号[KIT003]已存在",
      "第5行：商品[不存在的商品]不存在"
    ]
  }
}
```

### 2. 下载导入模板

**接口地址**：`GET /api/admin/appOfflineOrder/template`

**响应**：Excel文件下载

## Excel格式要求

### 表头格式（第一行）
| 试剂盒编号 | 商品名称 | 商品规格 |
|-----------|---------|---------|
| KIT001    | 商品A   | 规格1   |
| KIT002    | 商品B   | 规格1，规格2，规格3   |

### 字段说明

1. **试剂盒编号**（必填）
   - 数据库中必须唯一
   - Excel文件内部必须唯一
   - 不能为空

2. **商品名称**（必填）
   - 必须在app_goods表中存在
   - 状态必须为启用（status=1）

3. **商品规格**（必填）
   - 必须在app_goods_specs表中存在
   - 必须属于对应的商品
   - 状态必须为启用（status=1）
   - **支持多个规格**：使用中文逗号（，）分割多个规格名称
   - 示例：`规格1，规格2，规格3`

## 使用步骤

1. **下载模板**
   - 调用模板下载接口获取Excel模板
   - 参考模板格式填写数据

2. **准备数据**
   - 确保商品名称在系统中存在
   - 确保商品规格属于对应商品且存在
   - 确保试剂盒编号唯一

3. **上传导入**
   - 调用导入接口上传Excel文件
   - 查看导入结果和错误信息

4. **处理错误**
   - 根据错误信息修正数据
   - 重新导入失败的记录

## 多规格处理说明

### 支持格式
- **单个规格**：直接填写规格名称，如：`规格A`
- **多个规格**：使用中文逗号分割，如：`规格A，规格B，规格C`

### 处理逻辑
1. 系统会自动按中文逗号（，）分割规格名称
2. 每个规格名称都会在数据库中进行校验
3. 所有规格必须都存在且属于对应商品，否则整行导入失败
4. 规格ID会用英文逗号拼接存储在goods_specs字段
5. 规格名称会用中文逗号拼接存储在goods_specs_name字段

### 示例
```
试剂盒编号: KIT001
商品名称: 宠物基因检测套餐
商品规格: 毛色检测，疾病筛查，品种鉴定

存储结果:
- goods_specs: "1,2,3" (规格ID)
- goods_specs_name: "毛色检测，疾病筛查，品种鉴定"
```

## 注意事项

1. **数据准备**
   - 导入前请确保相关商品和规格已在系统中创建
   - 试剂盒编号必须全局唯一
   - 多个规格时，每个规格都必须存在

2. **规格分割符**
   - 必须使用中文逗号（，）分割多个规格
   - 英文逗号（,）不会被识别为分割符
   - 规格名称前后的空格会被自动去除

3. **性能考虑**
   - 建议单次导入不超过1000条记录
   - 大批量数据建议分批导入

4. **错误处理**
   - 导入过程中如有错误，会继续处理其他正确的记录
   - 错误信息会详细说明具体问题和行号
   - 多规格中任一规格不存在，整行都会导入失败

## 技术实现

### 核心组件

1. **OfflineOrderImportDto**：Excel数据映射DTO
2. **OfflineOrderImportListener**：EasyExcel读取监听器
3. **ImportResultResponse**：导入结果响应类
4. **AppOfflineOrderController**：控制器接口

### 数据流程

1. 上传Excel文件
2. EasyExcel解析文件内容
3. 监听器逐行处理数据
4. 数据校验和转换
5. 批量保存到数据库
6. 返回导入结果

### 校验规则

1. **基础校验**：非空校验、格式校验
2. **业务校验**：唯一性校验、关联性校验
3. **数据转换**：Excel数据转换为实体对象

## 扩展说明

如需扩展功能，可以：

1. **增加字段**：在DTO中添加新字段，更新Excel模板
2. **增加校验**：在监听器中添加新的校验规则
3. **优化性能**：调整批量处理大小，优化数据库操作

## 错误码说明

- `200`：导入成功
- `400`：请求参数错误（文件为空、格式不正确等）
- `500`：服务器内部错误（数据库异常等）
