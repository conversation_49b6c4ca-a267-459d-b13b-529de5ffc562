package com.boot.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.dto.ActivityParticipantResponse;
import com.boot.server.dto.AdminIntegralActivityResponse;
import com.boot.server.dto.request.ActivityParticipantRequest;
import com.boot.server.dto.request.AdminIntegralActivityRequest;
import com.boot.server.dto.request.CreateIntegralActivityRequest;
import com.boot.server.dto.request.UpdateIntegralActivityRequest;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 积分活动服务测试类
 *
 * <AUTHOR> 2025/7/23 13:45
 */
@SpringBootTest
@ActiveProfiles("dev")
public class AppIntegralActivityServiceTest {

    @Resource
    private AppIntegralActivityService appIntegralActivityService;

    @Test
    public void testCreateIntegralActivity() {
        // 创建积分活动请求
        CreateIntegralActivityRequest request = new CreateIntegralActivityRequest();
        request.setActivityNo("TEST123");
        request.setIntegral(100);
        request.setExprTime(LocalDateTime.now().plusDays(7));
        request.setLimitNum(50);
        request.setRemark("测试积分活动");

        // 执行创建
        Boolean result = appIntegralActivityService.create(request);

        // 验证结果
        assertTrue(result, "创建积分活动应该成功");
    }

    @Test
    public void testSelectAdminPage() {
        // 创建查询请求
        AdminIntegralActivityRequest request = new AdminIntegralActivityRequest();
        request.setPageNum(1);
        request.setPageSize(10);

        // 执行查询
        Page<AdminIntegralActivityResponse> result = appIntegralActivityService.selectAdminPage(request);
        
        // 验证结果
        assertNotNull(result, "查询结果不应该为空");
        assertTrue(result.getSize() > 0, "分页大小应该大于0");
    }

    @Test
    public void testUpdateIntegralActivity() {
        // 先创建一个积分活动
        CreateIntegralActivityRequest createRequest = new CreateIntegralActivityRequest();
        createRequest.setActivityNo("TEST001");
        createRequest.setIntegral(100);
        createRequest.setExprTime(LocalDateTime.now().plusDays(7));
        createRequest.setLimitNum(50);
        createRequest.setRemark("测试积分活动");

        Boolean createResult = appIntegralActivityService.create(createRequest);
        assertTrue(createResult, "创建积分活动应该成功");

        // 查询刚创建的活动
        AdminIntegralActivityRequest queryRequest = new AdminIntegralActivityRequest();
        queryRequest.setPageNum(1);
        queryRequest.setPageSize(1);
        queryRequest.setRemark("测试积分活动");

        Page<AdminIntegralActivityResponse> queryResult = appIntegralActivityService.selectAdminPage(queryRequest);
        assertFalse(queryResult.getRecords().isEmpty(), "应该能查询到刚创建的活动");

        AdminIntegralActivityResponse activity = queryResult.getRecords().get(0);

        // 更新积分活动
        UpdateIntegralActivityRequest updateRequest = new UpdateIntegralActivityRequest();
        updateRequest.setId(activity.getId());
        updateRequest.setActivityNo("TEST002");
        updateRequest.setIntegral(200);
        updateRequest.setExprTime(LocalDateTime.now().plusDays(14));
        updateRequest.setLimitNum(100);
        updateRequest.setRemark("更新后的测试积分活动");

        // 执行更新
        Boolean updateResult = appIntegralActivityService.update(updateRequest);

        // 验证结果
        assertTrue(updateResult, "更新积分活动应该成功");

        // 验证更新后的数据
        AdminIntegralActivityResponse updatedActivity = appIntegralActivityService.getById(activity.getId());
        assertEquals(200, updatedActivity.getIntegral(), "积分应该已更新");
        assertEquals("更新后的测试积分活动", updatedActivity.getRemark(), "备注应该已更新");
    }

    @Test
    public void testSelectActivityParticipants() {
        // 创建一个测试活动
        CreateIntegralActivityRequest createRequest = new CreateIntegralActivityRequest();
        createRequest.setActivityNo("TESTPART001");
        createRequest.setIntegral(50);
        createRequest.setExprTime(LocalDateTime.now().plusDays(7));
        createRequest.setLimitNum(100);
        createRequest.setRemark("测试参加人员查询活动");

        Boolean createResult = appIntegralActivityService.create(createRequest);
        assertTrue(createResult, "创建积分活动应该成功");

        // 查询刚创建的活动
        AdminIntegralActivityRequest queryRequest = new AdminIntegralActivityRequest();
        queryRequest.setPageNum(1);
        queryRequest.setPageSize(1);
        queryRequest.setActivityNo("TESTPART001");

        Page<AdminIntegralActivityResponse> queryResult = appIntegralActivityService.selectAdminPage(queryRequest);
        assertFalse(queryResult.getRecords().isEmpty(), "应该能查询到刚创建的活动");

        AdminIntegralActivityResponse activity = queryResult.getRecords().get(0);

        // 查询活动参加人员列表
        ActivityParticipantRequest participantRequest = new ActivityParticipantRequest();
        participantRequest.setActivityId(activity.getId());
        participantRequest.setPageNum(1);
        participantRequest.setPageSize(10);

        Page<ActivityParticipantResponse> participantResult = appIntegralActivityService.selectActivityParticipants(participantRequest);

        // 验证结果
        assertNotNull(participantResult, "查询结果不应该为空");
        assertTrue(participantResult.getSize() > 0, "分页大小应该大于0");
        // 由于是新创建的活动，参加人员应该为空
        assertTrue(participantResult.getRecords().isEmpty() || participantResult.getRecords().size() >= 0, "参加人员列表应该是有效的");
    }
}
