package com.boot.server;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.boot.server.common.express.ExpressOrderService;
import com.boot.server.common.express.OAuth2Service;
import com.boot.server.common.express.dto.ExpAddress;
import com.boot.server.common.express.dto.PreOrderRequest;
import com.boot.server.common.express.dto.RoutesRequest;
import com.boot.server.common.express.utils.ExpressUtil;
import com.boot.server.config.ExpressConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;

@Slf4j
@SpringBootTest
class PetServerApplicationTests {
    @Resource
    private OAuth2Service oAuth2Service;
    @Resource
    private ExpressConfig expressConfig;
    @Resource
    private ExpressOrderService expressOrderService;

    @Test
    void contextLoads() throws InterruptedException {
        String accessToken = oAuth2Service.getAccessToken();
        System.out.println(accessToken);
        //Thread.sleep(Duration.ofSeconds(20).toMillis());
        //System.out.println(oAuth2Service.getAccessToken());

    }


    @Test
    void create_order_test() {
//        CreateOrderRequest createOrderRequest = new CreateOrderRequest();
//        createOrderRequest.setCargoDetails(CreateOrderRequest.getCargoDetails());
//        createOrderRequest.setOrderId(IdWorker.getTimeId());
//        createOrderRequest.setMonthlyCard(expressConfig.getMonthlyCard());
//        ArrayList<ExpAddress> expAddresses = new ArrayList<>();
//        ExpAddress expAddress = new ExpAddress();
//        expAddress.setContactType(1);
//        expAddress.setContact("刘昊栋");
//        expAddress.setTel("18736342050");
//        expAddress.setAddress("河南省郑州市中原区祥瑞苑 18 号楼 2 单元 1703");
//        expAddresses.add(expAddress);
//        ExpAddress expAddress2 = new ExpAddress();
//        expAddress2.setContactType(2);
//        expAddress2.setContact("刘三");
//        expAddress2.setTel("17630046703");
//        expAddress2.setAddress("河南省洛阳市洛宁县底张乡底张街");
//        expAddresses.add(expAddress2);
//
//        createOrderRequest.setContactInfoList(expAddresses);
//        JSONObject order = expressOrderService.createOrder(createOrderRequest);
//        String waybillNo = ExpressUtil.getWaybillNo(order);
//
//        System.out.println(waybillNo);
//        System.out.println(order.toJSONString());
        RoutesRequest routesRequest = new RoutesRequest();
        routesRequest.setTrackingNumber(Collections.singletonList("SF0267951464481"));
        JSONObject jsonObject = expressOrderService.queryRoutes(routesRequest);
        System.out.println(JSONObject.toJSONString(ExpressUtil.getWaybillRouters(jsonObject)));
    }

    @Test
    void pre_order_test() {
        PreOrderRequest preOrderRequest = new PreOrderRequest();
        preOrderRequest.setOrderId(IdWorker.getTimeId());
        preOrderRequest.setMonthlyCard(expressConfig.getMonthlyCard());
        ArrayList<ExpAddress> expAddresses = new ArrayList<>();
        ExpAddress expAddress = new ExpAddress();
        expAddress.setContactType(1);
        expAddress.setContact("刘昊栋");
        expAddress.setTel("18736342050");
        expAddress.setProvince("河南省");
        expAddress.setCity("郑州市");
        expAddress.setAddress("河南省郑州市中原区祥瑞苑 18 号楼 2 单元 1703");
        expAddresses.add(expAddress);
        ExpAddress expAddress2 = new ExpAddress();
        expAddress2.setContactType(2);
        expAddress2.setContact("刘三");
        expAddress2.setTel("17630046703");
        expAddress.setProvince("河南省");
        expAddress.setCity("洛阳市");
        expAddress2.setAddress("河南省洛阳市洛宁县底张乡底张街");
        expAddresses.add(expAddress2);

        preOrderRequest.setContactInfoList(expAddresses);
        JSONObject order = expressOrderService.preOrder(preOrderRequest);
        JSONObject apiResultData = ExpressUtil.getApiResultData(order);
        log.info(JSONObject.toJSONString(order));
        log.info(JSONObject.toJSONString(apiResultData.toJSONString()));
    }

}
