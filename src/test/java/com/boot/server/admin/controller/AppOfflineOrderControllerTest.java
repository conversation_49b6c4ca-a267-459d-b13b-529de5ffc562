package com.boot.server.admin.controller;

import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppGoodsSpecsEntity;
import com.boot.server.entity.AppOfflineOrderEntity;
import com.boot.server.repository.AppGoodsRepository;
import com.boot.server.repository.AppGoodsSpecsRepository;
import com.boot.server.repository.AppOfflineOrderRepository;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 线下渠道订单控制器测试
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@SpringBootTest
@ActiveProfiles("test")
public class AppOfflineOrderControllerTest {

    @MockBean
    private AppOfflineOrderRepository appOfflineOrderRepository;

    @MockBean
    private AppGoodsRepository appGoodsRepository;

    @MockBean
    private AppGoodsSpecsRepository appGoodsSpecsRepository;

    @Test
    public void testImportLogic() {
        // 模拟商品数据
        AppGoodsEntity goods = new AppGoodsEntity();
        goods.setId(1L);
        goods.setTitle("测试商品");
        goods.setStatus(1);

        // 模拟规格数据
        AppGoodsSpecsEntity specs = new AppGoodsSpecsEntity();
        specs.setId(1L);
        specs.setGoodsId(1);
        specs.setTitle("测试规格");
        specs.setStatus(1);

        // 设置Mock行为
        when(appGoodsRepository.getByTitle("测试商品")).thenReturn(goods);
        when(appGoodsSpecsRepository.getByGoodsIdAndTitle(1, "测试规格")).thenReturn(specs);
        when(appOfflineOrderRepository.getByKitsNo("KIT001")).thenReturn(null);
        when(appOfflineOrderRepository.saveBatch(any())).thenReturn(true);

        // 这里可以添加更多的测试逻辑
        System.out.println("测试准备完成，可以进行Excel导入功能测试");
    }
}
