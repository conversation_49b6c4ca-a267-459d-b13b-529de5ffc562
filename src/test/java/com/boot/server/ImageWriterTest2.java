package com.boot.server;

import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.boot.server.util.ReportUtil;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class ImageWriterTest2 {
    private static final String dir = System.getProperty("user.dir");
    private static final String INPUT_TWO_IMAGE = dir + File.separator + "report" + File.separator + "template" + File.separator + "index.jpg";

    private static final String OUTPUT_DIR = dir + File.separator + "uploads" + File.separator + "report-image" + File.separator;

    static {
        if (!FileUtil.exist(OUTPUT_DIR)) {
            System.out.println(new File(OUTPUT_DIR).mkdirs());
        }
    }

    @Test
    public void testDrawTableWithBorder() {
        TableInfo tableInfo = getTableInfo();
        // 创建文字位置列表
        List<TextPosition> textPositions = new ArrayList<>();
        // 添加多组文字位置
        textPositions.add(ItemEnum.ORG.buildPosition("小狗"));
        textPositions.add(ItemEnum.PET_NAME.buildPosition("宠物名称"));
        textPositions.add(ItemEnum.PET_CLASSIFY.buildPosition("品种"));
        textPositions.add(ItemEnum.PET_GENDER.buildPosition("雌性"));
        textPositions.add(ItemEnum.PET_NO.buildPosition(String.format("SZ%05d", 2)));
        ReportUtil.drawTableWithBorder(
                INPUT_TWO_IMAGE,
                OUTPUT_DIR + textPositions.get(1).getText() + ".png",
                tableInfo,
                textPositions);
    }

    @NotNull
    private static TableInfo getTableInfo() {
        List<List<String>> table = Arrays.asList(
//
                Collections.singletonList("CAT:疾病"),
                Arrays.asList("检测项目", "位点 - 基因型", "结果解读", "检测日期"),
                Arrays.asList("自身免疫性淋巴细胞增生综合症", "c.413_414insA - N/N", "不致病", "2025-05-09"),
                Arrays.asList("预期寿命较短的先天性少毛症", "Taᴹ/Taᵇ - N/N", "不致病", "2025-05-09"),

//                Collections.singletonList("CAT:疾病"),
//                Arrays.asList("检测项目", "位点", "基因型", "结果解读"),
//                Arrays.asList("自身免疫性淋巴细胞增生综合症", "c.413_414insA", "N/N", "不致病"),
//                Arrays.asList("预期寿命较短的先天性少毛症", "c.1030_1033delCTGT", "N/N", "不致病")

//                Collections.singletonList("CAT:疾病"),
//                Arrays.asList("TITLE:解读结果"),
//                Arrays.asList("CONTENT:极端阳光基因")
//
//                Collections.singletonList("CAT:毛长"),
//                Arrays.asList("TITLE:解读结果"),
//                Arrays.asList("CONTENT:极端阳光基因"),
//
                Collections.singletonList("CAT:毛色"),
                Arrays.asList("检测项目", "基因型", "结果解读", "检测日期"),
                Arrays.asList("斯芬克斯无毛基因", "Aᵖᵇ/a", "橘色", "2025-05-09"),
                Arrays.asList("德文卷毛基因", "Wb+/wbSIB", "橘色", "2025-05-09"),
                Arrays.asList("炭色基因", "Aᵖᵇ/a", "正常毛色", "2025-05-09"),
                Arrays.asList("金色阳光基因", "Wb+/wbSIB", "正常毛色", "2025-05-09"),
                Arrays.asList("炭色基因", "Aᵖᵇ/a", "正常毛色", "2025-05-09"),
                Arrays.asList("金色阳光基因", "Wb+/wbSIB", "正常毛色", "2025-05-09"),
                Arrays.asList("炭色基因", "Wb+/wbSIB", "正常毛色", "2025-05-09"),
                Arrays.asList("NOTE:1. 携带一份金色阳光色等位基因，会将其传给一半的后代"),
                Arrays.asList("NOTE:2. 携带两份炭色基因，会讲该基因传给所有后代；且携带一份纯色基因，会将该基因传给一半后代"),
                Arrays.asList("NOTE:3. 携带两份炭色基因，会讲该基因传给所有后代；且携带一份纯色基因，会将该基因传给一半后代"),
                Arrays.asList("NOTE:4. 携带两份炭色基因，会讲该基因传给所有后代；且携带一份纯色基因，会将该基因传给一半后代"),
                Arrays.asList("NOTE:5. 携带两份炭色基因，会讲该基因传给所有后代；且携带一份纯色基因，会将该基因传给一半后代"),
                Arrays.asList("NOTE:6. 携带两份炭色基因，会讲该基因传给所有后代；且携带一份纯色基因，会将该基因传给一半后代"),
                Arrays.asList("NOTE:7. 携带两份炭色基因，会讲该基因传给所有后代；且携带一份纯色基因，会将该基因传给一半后代")
        );

        return new TableInfo(
                230, // 表格左上角x坐标（可根据实际图片调整）
                620, // 表格左上角y坐标（可根据实际图片调整）
                275, // 单元格宽度
                50, // 单元格高度
                table);
    }
}
