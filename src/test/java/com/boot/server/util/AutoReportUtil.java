package com.boot.server.util;

import com.boot.server.TableInfo;
import com.boot.server.TextPosition;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.List;

/**
 * ReportUtil
 *
 * <AUTHOR> 2025/5/16 00:50
 */
public class AutoReportUtil {

    // 默认字体设置
    private static final String DEFAULT_FONT_NAME = "思源雅黑";
    private static final int DEFAULT_FONT_SIZE = 35;
    private static final Color DEFAULT_COLOR = Color.BLACK;

    private static final String CATEGORY_PREFIX = "CAT:";
    private static final String TITLE_PREFIX = "TITLE:";
    private static final String NOTE_PREFIX = "NOTE:";
    private static final String CONTENT_PREFIX = "CONTENT:";
    private static final String INTERPRETATION_PREFIX = "INTERPRETATION:";

    /**
     * 在图片上绘制表格内容和横线，支持分页、自动合并单元格和动态行高。
     *
     * @param imagePath     原始图片路径
     * @param outputPath    输出图片路径（会自动加上分页后缀，如_page1）
     * @param tableInfo     表格信息（包含所有页的数据）
     * @param textPositions 固定的文本位置
     * @return 所有生成图片的路径列表
     */
    public static List<String> drawTableWithBorder(
            String imagePath,
            String outputPath,
            TableInfo tableInfo,
            List<TextPosition> textPositions) {
        List<String> generatedFiles = new ArrayList<>();
        try {
            // 预加载字体和计算工具
            Font headerFont = getDefaultFont(0);
            Font normalFont = getDefaultFont(1);
            Font categoryFont = new Font(DEFAULT_FONT_NAME, Font.BOLD, 30); // 分类标题使用更大的字体
            BufferedImage tempImage = new BufferedImage(1, 1, BufferedImage.TYPE_INT_ARGB);
            Graphics2D tempG2d = tempImage.createGraphics();
            FontMetrics headerMetrics = tempG2d.getFontMetrics(headerFont);
            FontMetrics normalMetrics = tempG2d.getFontMetrics(normalFont);
            FontMetrics categoryMetrics = tempG2d.getFontMetrics(categoryFont);
            tempG2d.dispose();

            final int MAX_VISUAL_ROWS_PER_PAGE = 18;

            // 1. 计算每条数据实际占用的视觉行数
            List<List<String>> allDataRows = tableInfo.getContent();
            int[] visualRowCounts = new int[allDataRows.size()];
            int maxCols = 0; // 全局最大列数，用于计算说明行的宽度
            for (List<String> rowData : allDataRows) {
                if (rowData.size() > maxCols) {
                    maxCols = rowData.size();
                }
            }
            int tableWidth = tableInfo.getCellWidth() * maxCols;

            for (int i = 0; i < allDataRows.size(); i++) {
                List<String> rowData = allDataRows.get(i);
                FontMetrics metrics;
                int lineCount = 1;

                if (rowData.size() == 1) {
                    String cellText = rowData.get(0);
                    if (cellText.startsWith(CATEGORY_PREFIX)) {
                        metrics = categoryMetrics;
                        cellText = cellText.substring(CATEGORY_PREFIX.length());
                    } else if (cellText.startsWith(TITLE_PREFIX)) {
                        metrics = headerMetrics;
                        cellText = cellText.substring(TITLE_PREFIX.length());
                    } else if (cellText.startsWith(NOTE_PREFIX)) {
                        metrics = headerMetrics;
                        cellText = cellText.substring(NOTE_PREFIX.length());
                    } else if (cellText.startsWith(CONTENT_PREFIX)) {
                        metrics = normalMetrics;
                        cellText = cellText.substring(CONTENT_PREFIX.length());
                    } else if (cellText.startsWith(INTERPRETATION_PREFIX)) {
                        metrics = headerMetrics;
                        cellText = cellText.substring(INTERPRETATION_PREFIX.length());
                    } else {
                        metrics = categoryMetrics; // Default for single-column rows
                    }
                    // 解读内容使用双倍宽度进行换行计算
                    int wrapWidth = tableWidth - 20;
                    if (cellText.startsWith(INTERPRETATION_PREFIX) || 
                        cellText.startsWith(CONTENT_PREFIX)) {
                        wrapWidth = tableInfo.getCellWidth() * 2 - 20; // 双倍宽度
                    }
                    List<String> lines = wrapTextByWidth(cellText, metrics, wrapWidth);
                    lineCount = Math.max(1, lines.size());
                    
                    // 解读内容需要额外的行高来跳一行
                    if (cellText.startsWith(INTERPRETATION_PREFIX) || 
                        cellText.startsWith(CONTENT_PREFIX)) {
                        lineCount += 1; // 增加一行高度
                    }
                } else if (rowData.size() > 2) { // "结果解读" 列
                    metrics = normalMetrics;
                    String cellText = rowData.get(2);
                    List<String> lines = wrapTextByWidth(cellText, metrics, tableInfo.getCellWidth() - 2 * 30);
                    lineCount = Math.max(1, lines.size());
                }
                visualRowCounts[i] = lineCount;
            }

            // 2. 根据视觉行数将数据拆分成多个页面，并处理孤行和重复标题
            List<List<List<String>>> pagesData = new ArrayList<>();
            List<List<String>> currentPageData = new ArrayList<>();
            List<Integer> currentPageVisualCounts = new ArrayList<>(); // 并行列表，用于精确追踪视觉行数
            int currentVisualRowCount = 0;

            List<List<String>> lastCategoryHeaderInfo = null; // [ [category_title], [table_header] ]
            int lastCatHeadVisualCount = 0;
            int lastTabHeadVisualCount = 0;

            for (int i = 0; i < allDataRows.size(); i++) {
                List<String> currentRow = allDataRows.get(i);
                int currentRowVisualCount = visualRowCounts[i];

                // 探测并存储最新的分类及其表头信息（排除NOTE、CONTENT、INTERPRETATION）
                if (currentRow.size() == 1 && 
                    !currentRow.get(0).startsWith("NOTE:") &&
                    !currentRow.get(0).startsWith("CONTENT:") &&
                    !currentRow.get(0).startsWith("INTERPRETATION:")) {
                    lastCategoryHeaderInfo = new ArrayList<>();
                    lastCategoryHeaderInfo.add(currentRow);
                    lastCatHeadVisualCount = currentRowVisualCount;

                    if (i + 1 < allDataRows.size() && allDataRows.get(i + 1).size() > 1) {
                        lastCategoryHeaderInfo.add(allDataRows.get(i + 1));
                        lastTabHeadVisualCount = visualRowCounts[i + 1];
                    } else {
                        lastCategoryHeaderInfo.add(null); // 占位，保持size为2
                        lastTabHeadVisualCount = 0;
                    }
                }

                // 在添加新行之前，检查是否需要分页
                if (!currentPageData.isEmpty()
                        && (currentVisualRowCount + currentRowVisualCount) > MAX_VISUAL_ROWS_PER_PAGE) {
                    int rowsOnPage = currentPageData.size();

                    // 定义什么是分类标题（不能是NOTE、CONTENT、INTERPRETATION）
                    boolean lastIsCategory = rowsOnPage >= 1 && currentPageData.get(rowsOnPage - 1).size() == 1
                            && !currentPageData.get(rowsOnPage - 1).get(0).startsWith("NOTE:")
                            && !currentPageData.get(rowsOnPage - 1).get(0).startsWith("CONTENT:")
                            && !currentPageData.get(rowsOnPage - 1).get(0).startsWith("INTERPRETATION:");
                    boolean lastTwoAreCategoryPair = rowsOnPage >= 2 && currentPageData.get(rowsOnPage - 2).size() == 1
                            && !currentPageData.get(rowsOnPage - 2).get(0).startsWith("NOTE:")
                            && !currentPageData.get(rowsOnPage - 2).get(0).startsWith("CONTENT:")
                            && !currentPageData.get(rowsOnPage - 2).get(0).startsWith("INTERPRETATION:");

                    // 情况一：页末是"分类+表头"，移动它们
                    if (lastTwoAreCategoryPair) {
                        List<String> catRow = currentPageData.remove(rowsOnPage - 2);
                        List<String> tableRow = currentPageData.remove(rowsOnPage - 2);
                        int catCount = currentPageVisualCounts.remove(rowsOnPage - 2);
                        int tableCount = currentPageVisualCounts.remove(rowsOnPage - 2);
                        if (!currentPageData.isEmpty())
                            pagesData.add(currentPageData);

                        currentPageData = new ArrayList<>();
                        currentPageData.add(catRow);
                        currentPageData.add(tableRow);
                        currentPageVisualCounts = new ArrayList<>();
                        currentPageVisualCounts.add(catCount);
                        currentPageVisualCounts.add(tableCount);
                        currentVisualRowCount = catCount + tableCount;
                    }
                    // 情况二：页末仅是"分类"，移动它
                    else if (lastIsCategory) {
                        List<String> catRow = currentPageData.remove(rowsOnPage - 1);
                        int catCount = currentPageVisualCounts.remove(rowsOnPage - 1);
                        if (!currentPageData.isEmpty())
                            pagesData.add(currentPageData);

                        currentPageData = new ArrayList<>();
                        currentPageData.add(catRow);
                        currentPageVisualCounts = new ArrayList<>();
                        currentPageVisualCounts.add(catCount);
                        currentVisualRowCount = catCount;
                    }
                    // 情况三：正常分页，需判断是否重复标题
                    else {
                        pagesData.add(currentPageData);
                        currentPageData = new ArrayList<>();
                        currentPageVisualCounts = new ArrayList<>();
                        currentVisualRowCount = 0;

                        // 如果新页不是新分类的开始，则重复上一个分类的标题
                        if (lastCategoryHeaderInfo != null && currentRow.size() != 1 &&
                            !currentRow.get(0).startsWith("NOTE:") &&
                            !currentRow.get(0).startsWith("CONTENT:") &&
                            !currentRow.get(0).startsWith("INTERPRETATION:")) {
                            currentPageData.add(lastCategoryHeaderInfo.get(0));
                            currentPageVisualCounts.add(lastCatHeadVisualCount);
                            currentVisualRowCount += lastCatHeadVisualCount;
                            if (lastCategoryHeaderInfo.get(1) != null) {
                                currentPageData.add(lastCategoryHeaderInfo.get(1));
                                currentPageVisualCounts.add(lastTabHeadVisualCount);
                                currentVisualRowCount += lastTabHeadVisualCount;
                            }
                        }
                    }
                }

                // 将当前行添加到正在构建的页面
                currentPageData.add(currentRow);
                currentPageVisualCounts.add(currentRowVisualCount);
                currentVisualRowCount += currentRowVisualCount;
            }

            // 添加最后一页
            if (!currentPageData.isEmpty()) {
                pagesData.add(currentPageData);
            }

            // 3. 循环绘制每一个页面
            for (int pageIndex = 0; pageIndex < pagesData.size(); pageIndex++) {
                // 为每一页生成独立的输出路径
                String currentPageOutputPath;
                int dotIndex = outputPath.lastIndexOf('.');
                if (dotIndex != -1) {
                    currentPageOutputPath = outputPath.substring(0, dotIndex) + "_page" + (pageIndex + 1)
                            + outputPath.substring(dotIndex);
                } else {
                    currentPageOutputPath = outputPath + "_page" + (pageIndex + 1);
                }

                TableInfo pageTableInfo = new TableInfo(
                        tableInfo.getStartX(),
                        tableInfo.getStartY(),
                        tableInfo.getCellWidth(),
                        tableInfo.getCellHeight(),
                        pagesData.get(pageIndex));

                // 调用私有方法绘制单页
                drawSinglePage(imagePath, currentPageOutputPath, pageTableInfo, textPositions, headerFont, normalFont,
                        categoryFont, maxCols);
                generatedFiles.add(currentPageOutputPath);
            }

        } catch (IOException e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
        return generatedFiles;
    }

    /**
     * 绘制单个页面的所有内容
     */
    private static void drawSinglePage(
            String imagePath, String outputPath, TableInfo tableInfo, List<TextPosition> textPositions,
            Font headerFont, Font normalFont, Font categoryFont, int maxCols) throws IOException {

        BufferedImage image = ImageIO.read(new File(imagePath));
        Graphics2D g2d = image.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

        // 绘制固定的文本
        Font titleFont = new Font(DEFAULT_FONT_NAME, Font.PLAIN, DEFAULT_FONT_SIZE);
        g2d.setFont(titleFont);
        g2d.setColor(DEFAULT_COLOR);
        for (TextPosition position : textPositions) {
            g2d.drawString(position.getText(), position.getX(), position.getY());
        }

        List<List<String>> pageContent = tableInfo.getContent();
        if (pageContent == null || pageContent.isEmpty()) {
            ImageIO.write(image, "png", new File(outputPath));
            g2d.dispose();
            return;
        }

        // --- 开始单页绘制核心逻辑 ---
        int rows = pageContent.size();
        final int cols = maxCols;

        // 1. 合并单元格计算
        Map<Integer, Integer> mergeStartRow = new java.util.HashMap<>();
        boolean[] isMergedRow = new boolean[rows];
        int i = 0;
        while (i < rows) {
            if (pageContent.get(i).isEmpty()) {
                i++;
                continue;
            }
            String val = pageContent.get(i).get(0);
            int j = i + 1;
            while (j < rows && !pageContent.get(j).isEmpty() && val != null && val.equals(pageContent.get(j).get(0))) {
                j++;
            }
            int mergeLen = j - i;
            if (mergeLen > 1) {
                mergeStartRow.put(i, mergeLen);
                for (int r = i + 1; r < i + mergeLen; r++) {
                    isMergedRow[r] = true;
                }
            }
            i = j;
        }

        // Define pairs for special merging. More pairs can be added here in the future.
        List<Set<String>> specialMergePairs = new ArrayList<>();
        
        specialMergePairs.add(new HashSet<>(Arrays.asList("斯芬克斯无毛基因", "德文卷毛基因")));
        specialMergePairs.add(new HashSet<>(Arrays.asList("巧克力色基因", "肉桂色基因")));
        specialMergePairs.add(new HashSet<>(Arrays.asList("全白基因", "白斑毛色")));
        specialMergePairs.add(new HashSet<>(Arrays.asList("极端阳光基因", "金色阳光基因")));

        // Special merge for last column for specific items
        Map<Integer, Integer> mergeLastColStartRow = new java.util.HashMap<>();
        boolean[] isLastColMergedRow = new boolean[rows];

        for (i = 0; i < rows - 1; i++) {
            List<String> currentRow = pageContent.get(i);
            List<String> nextRow = pageContent.get(i + 1);

            if (!currentRow.isEmpty() && currentRow.size() > 2 &&
                    !nextRow.isEmpty() && nextRow.size() > 2) {

                Set<String> itemsToTest = new HashSet<>(Arrays.asList(currentRow.get(0), nextRow.get(0)));

                for (Set<String> pair : specialMergePairs) {
                    if (pair.equals(itemsToTest)) {
                        // Assuming the content in the last column is identical and should be merged.
                        mergeLastColStartRow.put(i, 2);
                        isLastColMergedRow[i + 1] = true;
                        i++; // Skip next row as it's already processed
                        break; // Found a pair, continue to the next 'i'
                    }
                }
            }
        }

        // 1.1 标记合并行的横线不绘制
        boolean[] skipLine = new boolean[rows + 1];
        for (Map.Entry<Integer, Integer> entry : mergeStartRow.entrySet()) {
            int start = entry.getKey();
            int len = entry.getValue();
            for (int l = start + 1; l < start + len; l++) {
                skipLine[l] = true;
            }
        }

        // 2. 动态行高计算
        FontMetrics headerMetrics = g2d.getFontMetrics(headerFont);
        FontMetrics normalMetrics = g2d.getFontMetrics(normalFont);
        FontMetrics categoryMetrics = g2d.getFontMetrics(categoryFont);
        int paddingCol2 = 30;
        int lineSpacing = 6;
        int[] rowHeights = new int[rows];
        for (int row = 0; row < rows; row++) {
            List<String> rowData = pageContent.get(row);
            FontMetrics metrics;
            int lineCount = 1;
            int totalTableWidth = tableInfo.getCellWidth() * cols;

            if (rowData.size() == 1) { // 所有单列行
                String cellText = rowData.get(0);
                if (cellText.startsWith(CATEGORY_PREFIX)) {
                    metrics = categoryMetrics;
                    cellText = cellText.substring(CATEGORY_PREFIX.length());
                } else if (cellText.startsWith(TITLE_PREFIX)) {
                    metrics = headerMetrics;
                    cellText = cellText.substring(TITLE_PREFIX.length());
                } else if (cellText.startsWith(NOTE_PREFIX)) {
                    metrics = headerMetrics;
                    cellText = cellText.substring(NOTE_PREFIX.length());
                } else if (cellText.startsWith(CONTENT_PREFIX)) {
                    metrics = normalMetrics;
                    cellText = cellText.substring(CONTENT_PREFIX.length());
                } else if (cellText.startsWith(INTERPRETATION_PREFIX)) {
                    metrics = headerMetrics;
                    cellText = cellText.substring(INTERPRETATION_PREFIX.length());
                } else {
                    metrics = categoryMetrics;
                }
                // 解读内容使用双倍宽度进行换行计算
                int wrapWidth = totalTableWidth - (2 * 10);
                if (cellText.startsWith(INTERPRETATION_PREFIX) || 
                    cellText.startsWith(CONTENT_PREFIX)) {
                    wrapWidth = tableInfo.getCellWidth() * 2 - (2 * 10); // 双倍宽度
                }
                List<String> lines = wrapTextByWidth(cellText, metrics, wrapWidth);
                lineCount = Math.max(1, lines.size());
                
                // 解读内容需要额外的行高来跳一行
                if (cellText.startsWith(INTERPRETATION_PREFIX) || 
                    cellText.startsWith(CONTENT_PREFIX)) {
                    lineCount += 1; // 增加一行高度
                }

            } else { // 其他多列行
                boolean isTitleRow = (row > 0 && pageContent.get(row - 1).size() == 1);
                boolean isHeaderRow = isTableHeader(rowData); // 检查是否为表头行
                metrics = (isTitleRow || isHeaderRow) ? headerMetrics : normalMetrics;

                // 计算所有列的最大行数
                int maxLineCount = 1;
                for (int col = 0; col < rowData.size(); col++) {
                    String cellText = rowData.get(col);
                    List<String> lines;
                    
                    if (col == 2) { // 第2列（结果解读列）
                        lines = wrapTextByWidth(cellText, metrics, tableInfo.getCellWidth() - 2 * paddingCol2);
                    } else { // 其他列
                        lines = wrapTextByWidth(cellText, metrics, tableInfo.getCellWidth() - 2 * 10);
                    }
                    
                    maxLineCount = Math.max(maxLineCount, lines.size());
                }
                lineCount = maxLineCount;
            }

            rowHeights[row] = Math.max(tableInfo.getCellHeight(), lineCount * (metrics.getHeight() + lineSpacing));
        }
        int[] rowStartY = new int[rows + 1];
        rowStartY[0] = tableInfo.getStartY();
        for (int r = 1; r <= rows; r++) {
            rowStartY[r] = rowStartY[r - 1] + rowHeights[r - 1];
        }

        // 3. 写入表格内容
        for (int row = 0; row < rows; row++) {
            int currentCols = pageContent.get(row).size();
            int cellY = rowStartY[row];
            int cellHeight = rowHeights[row];
            g2d.setColor(DEFAULT_COLOR);

            if (currentCols == 1) { // 绘制所有单列行
                String cellText = pageContent.get(row).get(0);
                FontMetrics currentMetrics;
                Font currentFont;

                if (cellText.startsWith(CATEGORY_PREFIX)) {
                    currentFont = categoryFont;
                    currentMetrics = categoryMetrics;
                    cellText = cellText.substring(CATEGORY_PREFIX.length());
                } else if (cellText.startsWith(TITLE_PREFIX)) {
                    currentFont = headerFont;
                    currentMetrics = headerMetrics;
                    cellText = cellText.substring(TITLE_PREFIX.length());
                } else if (cellText.startsWith(NOTE_PREFIX)) {
                    currentFont = headerFont;
                    currentMetrics = headerMetrics;
                    cellText = cellText.substring(NOTE_PREFIX.length());
                } else if (cellText.startsWith(CONTENT_PREFIX)) {
                    currentFont = normalFont;
                    currentMetrics = normalMetrics;
                    cellText = cellText.substring(CONTENT_PREFIX.length());
                } else if (cellText.startsWith(INTERPRETATION_PREFIX)) {
                    currentFont = headerFont;
                    currentMetrics = headerMetrics;
                    cellText = cellText.substring(INTERPRETATION_PREFIX.length());
                } else {
                    currentFont = categoryFont;
                    currentMetrics = categoryMetrics;
                }
                g2d.setFont(currentFont);

                // 计算单列行的显示位置
                int singleColWidth = tableInfo.getCellWidth();
                int centerOffset = (cols * tableInfo.getCellWidth() - singleColWidth) / 2;
                int totalTableWidth = singleColWidth;
                int padding = 10;
                
                // 解读内容使用双倍宽度
                if (pageContent.get(row).get(0).startsWith(INTERPRETATION_PREFIX) || 
                    pageContent.get(row).get(0).startsWith(CONTENT_PREFIX)) {
                    totalTableWidth = tableInfo.getCellWidth() * 2; // 双倍宽度
                    centerOffset = 0; // 不使用居中偏移
                }
                
                List<String> lines = wrapTextByWidth(cellText, currentMetrics, totalTableWidth - (2 * padding));
                int lineHeight = currentMetrics.getHeight() + lineSpacing;
                int totalHeight = lineHeight * (lines.size() > 0 ? lines.size() : 1);
                int startY = cellY + (cellHeight - totalHeight) / 2 + currentMetrics.getAscent();

                for (int iLine = 0; iLine < lines.size(); iLine++) {
                    String lineText = lines.get(iLine).trim();
                    if (lineText.isEmpty())
                        continue;
                    // 一行一列内容左对齐显示
                    int x;
                    int y;
                    if (pageContent.get(row).get(0).startsWith(INTERPRETATION_PREFIX) || 
                        pageContent.get(row).get(0).startsWith(CONTENT_PREFIX)) {
                        x = tableInfo.getStartX() + padding; // 解读内容左对齐
                        // 解读内容跳一行从第二行开始绘制
                        y = startY + (iLine + 1) * lineHeight;
                    } else {
                        x = tableInfo.getStartX() + padding; // 一行一列内容左对齐
                        y = startY + iLine * lineHeight;
                    }
                    g2d.drawString(lineText, x, y);
                }
            } else {
                boolean isHeaderRow = row > 0 && pageContent.get(row - 1).size() == 1;
                boolean isTableHeaderRow = isTableHeader(pageContent.get(row)); // 检查是否为表头行
                Font currentFont = (isHeaderRow || isTableHeaderRow) ? headerFont : normalFont;
                FontMetrics metrics = (isHeaderRow || isTableHeaderRow) ? headerMetrics : normalMetrics;
                g2d.setFont(currentFont);

                // 计算表格实际宽度和居中偏移
                int actualTableWidth = currentCols * tableInfo.getCellWidth();
                int centerOffset = (cols * tableInfo.getCellWidth() - actualTableWidth) / 2;

                for (int col = 0; col < cols; col++) {
                    if (col >= currentCols)
                        continue;
                    int cellX = tableInfo.getStartX() + centerOffset + col * tableInfo.getCellWidth();
                    String cellText = pageContent.get(row).get(col);

                    if (col == 0) { // 第0列（考虑合并）
                        if (mergeStartRow.containsKey(row)) {
                            int mergeRows = mergeStartRow.get(row);
                            int mergeHeight = 0;
                            for (int r = 0; r < mergeRows; r++)
                                mergeHeight += rowHeights[row + r];
                            
                            // 第0列自动换行显示
                            List<String> lines = wrapTextByWidth(cellText, metrics,
                                    tableInfo.getCellWidth() - 2 * 10);
                            int lineHeight = metrics.getHeight() + lineSpacing;
                            int totalHeight = lineHeight * (lines.size() > 0 ? lines.size() : 1);
                            int startY = cellY + (mergeHeight - totalHeight) / 2 + metrics.getAscent();

                            for (int iLine = 0; iLine < lines.size(); iLine++) {
                                String lineText = lines.get(iLine).trim();
                                if (lineText.isEmpty())
                                    continue;
                                // 居中显示
                                int textWidth = metrics.stringWidth(lineText);
                                int x = cellX + (tableInfo.getCellWidth() - textWidth) / 2;
                                int y = startY + iLine * lineHeight;
                                g2d.drawString(lineText, x, y);
                            }
                        } else if (isMergedRow[row]) {
                            continue;
                        } else {
                            // 第0列自动换行显示
                            List<String> lines = wrapTextByWidth(cellText, metrics,
                                    tableInfo.getCellWidth() - 2 * 10);
                            int lineHeight = metrics.getHeight() + lineSpacing;
                            int totalHeight = lineHeight * (lines.size() > 0 ? lines.size() : 1);
                            int startY = cellY + (cellHeight - totalHeight) / 2 + metrics.getAscent();

                            for (int iLine = 0; iLine < lines.size(); iLine++) {
                                String lineText = lines.get(iLine).trim();
                                if (lineText.isEmpty())
                                    continue;
                                // 居中显示
                                int textWidth = metrics.stringWidth(lineText);
                                int x = cellX + (tableInfo.getCellWidth() - textWidth) / 2;
                                int y = startY + iLine * lineHeight;
                                g2d.drawString(lineText, x, y);
                            }
                        }
                    } else if (col == 2) { // 第2列（结果解读，自动换行）
                        if (mergeLastColStartRow.containsKey(row)) { // It's a start of a last-column-merge
                            int mergeRows = mergeLastColStartRow.get(row);
                            int mergeHeight = 0;
                            for (int r = 0; r < mergeRows; r++)
                                mergeHeight += rowHeights[row + r];

                            List<String> lines = wrapTextByWidth(cellText, metrics,
                                    tableInfo.getCellWidth() - 2 * paddingCol2);
                            int lineHeight = metrics.getHeight() + lineSpacing;
                            int totalHeight = lineHeight * (lines.size() > 0 ? lines.size() : 1);
                            int startY = cellY + (mergeHeight - totalHeight) / 2 + metrics.getAscent();

                            for (int iLine = 0; iLine < lines.size(); iLine++) {
                                String lineText = lines.get(iLine).trim();
                                if (lineText.isEmpty())
                                    continue;
                                // 左对齐
                                int x = cellX + paddingCol2;
                                int y = startY + iLine * lineHeight;
                                g2d.drawString(lineText, x, y);
                            }
                        } else if (isLastColMergedRow[row]) { // It's a merged row for the last column
                            continue; // Don't draw anything
                        } else { // Not part of a last-column-merge
                            List<String> lines = wrapTextByWidth(cellText, metrics,
                                    tableInfo.getCellWidth() - 2 * paddingCol2);
                            int lineHeight = metrics.getHeight() + lineSpacing;
                            int totalHeight = lineHeight * (lines.size() > 0 ? lines.size() : 1);
                            int startY = cellY + (cellHeight - totalHeight) / 2 + metrics.getAscent();
                            for (int iLine = 0; iLine < lines.size(); iLine++) {
                                String lineText = lines.get(iLine).trim();
                                if (lineText.isEmpty())
                                    continue;
                                // 左对齐
                                int x = cellX + paddingCol2;
                                int y = startY + iLine * lineHeight;
                                g2d.drawString(lineText, x, y);
                            }
                        }
                    } else { // 其他列（第1列等）
                        // 其他列自动换行显示
                        List<String> lines = wrapTextByWidth(cellText, metrics,
                                tableInfo.getCellWidth() - 2 * 10);
                        int lineHeight = metrics.getHeight() + lineSpacing;
                        int totalHeight = lineHeight * (lines.size() > 0 ? lines.size() : 1);
                        int startY = cellY + (cellHeight - totalHeight) / 2 + metrics.getAscent();

                        for (int iLine = 0; iLine < lines.size(); iLine++) {
                            String lineText = lines.get(iLine).trim();
                            if (lineText.isEmpty())
                                continue;
                            // 居中显示
                            int textWidth = metrics.stringWidth(lineText);
                            int x = cellX + (tableInfo.getCellWidth() - textWidth) / 2;
                            int y = startY + iLine * lineHeight;
                            g2d.drawString(lineText, x, y);
                        }
                    }
                }
            }
        }

        // 4. 画横线 - 确保每行数据底部都有横线
        g2d.setColor(Color.BLACK);
        for (int line = 0; line <= rows; line++) {
            if (skipLine[line])
                continue;

            // 跳过解读相关行的横线
            if (line > 0 && line < rows) {
                List<String> prevRowData = pageContent.get(line - 1);
                List<String> currentRowData = pageContent.get(line);

                // 跳过连续NOTE行之间的横线
                if (prevRowData.size() == 1 && prevRowData.get(0).startsWith(NOTE_PREFIX) &&
                        currentRowData.size() == 1 && currentRowData.get(0).startsWith(NOTE_PREFIX)) {
                    continue;
                }
                
                // 跳过解读内容行的横线
                if (currentRowData.size() == 1 && 
                    (currentRowData.get(0).startsWith(INTERPRETATION_PREFIX) || 
                     currentRowData.get(0).startsWith(CONTENT_PREFIX))) {
                    continue;
                }
            }
            
            // 跳过解读内容行后面的横线
            if (line > 0 && line <= rows) {
                List<String> prevRowData = pageContent.get(line - 1);
                if (prevRowData.size() == 1 && 
                    (prevRowData.get(0).startsWith(INTERPRETATION_PREFIX) || 
                     prevRowData.get(0).startsWith(CONTENT_PREFIX))) {
                    continue;
                }
            }

            int y = rowStartY[line];
            g2d.setStroke(new BasicStroke(2));

            boolean isSpecialMergeLine = false;
            if (line > 0 && line < rows) {
                if (mergeLastColStartRow.containsKey(line - 1)) {
                    isSpecialMergeLine = true;
                }
            }
            
            // 检查是否是多列内容的最后一行
            boolean isLastMultiColumnRow = false;
            if (line > 0 && line < rows) {
                List<String> currentRowData = pageContent.get(line);
                if (currentRowData.size() > 1) {
                    // 检查后面是否还有多列内容
                    boolean hasMoreMultiColumnRows = false;
                    for (int checkRowIndex = line; checkRowIndex < rows; checkRowIndex++) {
                        List<String> checkRow = pageContent.get(checkRowIndex);
                        if (checkRow.size() > 1) {
                            hasMoreMultiColumnRows = true;
                            break;
                        }
                    }
                    // 如果后面没有多列内容，说明这是最后一行
                    if (!hasMoreMultiColumnRows) {
                        isLastMultiColumnRow = true;
                    }
                }
            }

            // 计算当前行的实际列数和居中偏移
            int currentRowCols = 0;
            if (line < rows) {
                currentRowCols = pageContent.get(line).size();
            } else if (line > 0) {
                currentRowCols = pageContent.get(line - 1).size();
            }
            
            // 对于单列行，使用单列宽度；对于多列行，使用实际列数
            int actualTableWidth = currentRowCols * tableInfo.getCellWidth();
            int centerOffset = (cols * tableInfo.getCellWidth() - actualTableWidth) / 2;
            
            // 解读内容行使用双倍宽度（虽然我们跳过了横线，但保持逻辑一致）
            if (line > 0 && line <= rows) {
                List<String> prevRowData = pageContent.get(line - 1);
                if (prevRowData.size() == 1 && 
                    (prevRowData.get(0).startsWith(INTERPRETATION_PREFIX) || 
                     prevRowData.get(0).startsWith(CONTENT_PREFIX))) {
                    actualTableWidth = tableInfo.getCellWidth() * 2;
                    centerOffset = 0;
                }
            }

            if (isSpecialMergeLine) {
                // Draw line with a gap for the last column
                g2d.drawLine(tableInfo.getStartX() + centerOffset, y, 
                           tableInfo.getStartX() + centerOffset + (currentRowCols - 1) * tableInfo.getCellWidth(), y);
            } else {
                g2d.drawLine(tableInfo.getStartX() + centerOffset, y, 
                           tableInfo.getStartX() + centerOffset + currentRowCols * tableInfo.getCellWidth(), y);
            }
        }

        g2d.dispose();
        ImageIO.write(image, "png", new File(outputPath));
    }

    /**
     * 获取表格默认字体，表头加粗。
     *
     * @param row 行号
     * @return Font
     */
    /**
     * 判断是否为表头行
     * @param rowData 行数据
     * @return 是否为表头行
     */
    private static boolean isTableHeader(List<String> rowData) {
        if (rowData.size() != 2) {
            return false;
        }
        String firstCol = rowData.get(0).trim();
        String secondCol = rowData.get(1).trim();
        
        // 检查是否为表头（检测项目、检测结果）
        return "检测项目".equals(firstCol) && "检测结果".equals(secondCol);
    }

    private static Font getDefaultFont(int row) {
        return Objects.equals(row, 0) ? new Font(DEFAULT_FONT_NAME, Font.BOLD, 27)
                : new Font(DEFAULT_FONT_NAME, Font.PLAIN, 27);
    }

    /**
     * 新增：按像素宽度自动换行
     */
    private static List<String> wrapTextByWidth(String text, FontMetrics metrics, int maxWidth) {
        List<String> lines = new ArrayList<>();
        if (text == null || text.isEmpty() || maxWidth <= 0) {
            if (text != null && !text.isEmpty())
                lines.add(text);
            return lines;
        }

        String[] paragraphs = text.replace("\\n", "\n").replace("\r\n", "\n").replace("\r", "\n").split("\n");
        for (String paragraph : paragraphs) {
            if (paragraph.isEmpty()) {
                lines.add("");
                continue;
            }
            StringBuilder line = new StringBuilder();
            for (char c : paragraph.toCharArray()) {
                if (metrics.stringWidth(line.toString() + c) > maxWidth && line.length() > 0) {
                    lines.add(line.toString());
                    line = new StringBuilder();
                }
                line.append(c);
            }
            if (line.length() > 0) {
                lines.add(line.toString());
            }
        }
        return lines;
    }
}
