package com.boot.server;

import lombok.Data;
import java.util.List;

@Data
public class TableInfo {
    private int startX; // 表格左上角x
    private int startY; // 表格左上角y
    private int cellWidth; // 单元格宽度
    private int cellHeight; // 单元格高度
    private List<List<String>> content; // 表格内容

    public TableInfo(int startX, int startY, int cellWidth, int cellHeight, List<List<String>> content) {
        this.startX = startX;
        this.startY = startY;
        this.cellWidth = cellWidth;
        this.cellHeight = cellHeight;
        this.content = content;
    }
} 