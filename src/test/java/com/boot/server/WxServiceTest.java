package com.boot.server;

import com.boot.server.service.WxService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

/**
 * WxServiceTest
 *
 * <AUTHOR> 2025/7/23 12:13
 */
@ActiveProfiles("dev")
@SpringBootTest(classes = PetServerApplication.class)
public class WxServiceTest {
    @Resource
    private WxService wxService;

    @Test
    public void test() {
//        String generatedQrCode = wxService.generateQrCode();
//        System.out.println(generatedQrCode);
    }
}
