package com.boot.server.auto;

import cn.hutool.core.io.FileUtil;
import com.boot.server.TableInfo;
import com.boot.server.TextPosition;
import com.boot.server.util.AutoReportUtil;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class AutoImageWriterTest {
    private static final String dir = System.getProperty("user.dir");
    private static final String INPUT_TWO_IMAGE = dir + "/report/template/index.jpg";
    private static final String AUTO_INPUT_TWO_IMAGE = dir + "/report/template/auto_index.jpg";

    private static final String OUTPUT_DIR = dir + "/uploads/report-image/";

    static {
        if (!FileUtil.exist(OUTPUT_DIR)) {
            System.out.println(new File(OUTPUT_DIR).mkdirs());
        }
    }

    @Test
    public void testDrawTableWithBorder() {
        TableInfo tableInfo = getInterpretationTableInfo();
        // 创建文字位置列表
        List<TextPosition> textPositions = new ArrayList<>();
        // 添加多组文字位置
        textPositions.add(TitlePosition.ORG.buildPosition("小猫来了"));
        textPositions.add(TitlePosition.REPORT_NO.buildPosition("B99382"));
        textPositions.add(TitlePosition.PET_NO.buildPosition("CDFS-998DF"));
        textPositions.add(TitlePosition.PET_CLASSIFY.buildPosition("蓝猫"));
        textPositions.add(TitlePosition.SEX.buildPosition("雌性"));
        textPositions.add(TitlePosition.PROJECT.buildPosition("炭色基因"));
        AutoReportUtil.drawTableWithBorder(
                AUTO_INPUT_TWO_IMAGE,
                OUTPUT_DIR + textPositions.get(1).getText() + ".png",
                tableInfo,
                textPositions);
    }

    @NotNull
    private static TableInfo getInterpretationTableInfo() {
        List<List<String>> table = new ArrayList<List<String>>(){{
            this.add(Arrays.asList("检测项目", "检测结果"));
            this.add(Arrays.asList("炭色基因", "正常花纹，不携带亚洲豹猫纯色基因，也不会将该基因传给下一代"));
            this.add(Arrays.asList("", ""));
            this.add(Arrays.asList("INTERPRETATION:解读:"));
            this.add(Arrays.asList("CONTENT:1. 正常花纹，不携带亚洲豹猫纯色基因，也不会将该基因传给下一代。"));
        }};

        return new TableInfo(
                350, // 表格左上角x坐标（可根据实际图片调整）
                700, // 表格左上角y坐标（可根据实际图片调整）
                420, // 单元格宽度
                50, // 单元格高度
                table);
    }
}
