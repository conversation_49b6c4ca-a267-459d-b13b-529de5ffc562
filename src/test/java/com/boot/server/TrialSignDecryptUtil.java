package com.boot.server;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 宠知因接口解密算法
 * 对应 TrialSignUtil 的解密工具类
 *
 * <AUTHOR>
 * @date 2023-12-23
 **/
@Slf4j
public class TrialSignDecryptUtil {

    /**
     * 验证签名是否正确
     *
     * @param data     原始数据
     * @param sign     签名
     * @param signkey  签名密钥
     * @param signTime 签名时间
     * @return 签名是否有效
     */
    public static boolean verifySign(Map<String, Object> data, String sign, String signkey, long signTime) {
        try {
            String calculatedSign = TrialSignUtil.signData(data, signkey, signTime);
            return calculatedSign.equals(sign);
        } catch (Exception e) {
            log.error("验证签名失败", e);
            return false;
        }
    }

    /**
     * 从签名中提取原始数据（不包含签名本身）
     *
     * @param signedData 包含签名的完整数据
     * @return 原始数据（不包含签名）
     */
    public static Map<String, Object> extractOriginalData(Map<String, Object> signedData) {
        Map<String, Object> originalData = new HashMap<>(signedData);
        // 移除签名相关字段
        originalData.remove("sign");
        originalData.remove("signtime");
        return originalData;
    }

    /**
     * 解密Base64编码的数据
     *
     * @param base64Data Base64编码的数据
     * @return 解密后的字符串
     */
    public static String decryptBase64Data(String base64Data) {
        try {
            byte[] decodedBytes = Base64.decode(base64Data);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Base64解密失败", e);
            return null;
        }
    }

    /**
     * URL解码
     *
     * @param encodedData URL编码的数据
     * @return 解码后的字符串
     */
    public static String urlDecode(String encodedData) {
        try {
            return URLDecoder.decode(encodedData, "utf-8");
        } catch (UnsupportedEncodingException e) {
            log.error("URL解码失败", e);
            return null;
        }
    }

    /**
     * 恢复被替换的特殊字符
     *
     * @param processedData 处理过的数据
     * @return 恢复特殊字符后的数据
     */
    public static String restoreSpecialCharacters(String processedData) {
        if (processedData == null) {
            return null;
        }
        
        // 恢复被替换的特殊字符
        String restored = processedData;
        String[] specialChars = {"-", "_", ".", "!", "~", "*", "'", "(", ")"};
        
        for (String specialChar : specialChars) {
            // 将占位符 'a' 替换回原始特殊字符
            // 注意：这里需要根据实际的替换逻辑来恢复
            // 由于原代码中是将特殊字符替换为 'a'，这里需要更精确的恢复逻辑
            if (specialChar.equals("-")) {
                restored = restored.replace("a", "-");
            } else if (specialChar.equals("_")) {
                restored = restored.replace("a", "_");
            } else if (specialChar.equals(".")) {
                restored = restored.replace("a", ".");
            } else if (specialChar.equals("!")) {
                restored = restored.replace("a", "!");
            } else if (specialChar.equals("~")) {
                restored = restored.replace("a", "~");
            } else if (specialChar.equals("*")) {
                restored = restored.replace("a", "*");
            } else if (specialChar.equals("'")) {
                restored = restored.replace("a", "'");
            } else if (specialChar.equals("(")) {
                restored = restored.replace("a", "(");
            } else if (specialChar.equals(")")) {
                restored = restored.replace("a", ")");
            }
        }
        
        return restored;
    }

    /**
     * 深度格式化对象，处理嵌套的Map、List等复杂对象
     * 对应 TrialSignUtil.objFormat 的反向处理
     *
     * @param data 需要格式化的数据
     * @return 格式化后的数据
     */
    public static Object deepFormatObject(Object data) {
        if (data == null) {
            return null;
        }
        
        if (data instanceof Map<?, ?>) {
            Map<String, Object> result = new HashMap<>();
            for (Map.Entry<?, ?> entry : ((Map<?, ?>) data).entrySet()) {
                String key = String.valueOf(entry.getKey());
                Object value = deepFormatObject(entry.getValue());
                result.put(key, value);
            }
            return result;
        } else if (data instanceof List<?>) {
            List<Object> result = new ArrayList<>();
            for (Object item : (List<?>) data) {
                result.add(deepFormatObject(item));
            }
            return result;
        } else if (data instanceof String) {
            String str = (String) data;
            // 处理日期格式的字符串
            if (str.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    return sdf.parse(str);
                } catch (ParseException e) {
                    // 如果解析失败，返回原始字符串
                    return str;
                }
            }
            return str;
        } else {
            return data;
        }
    }

    /**
     * 将JSON字符串解析为复杂对象（支持嵌套Map、List等）
     *
     * @param jsonString JSON字符串
     * @return 解析后的复杂对象
     */
    public static Object parseJsonToComplexObject(String jsonString) {
        try {
            Object parsed = JSON.parse(jsonString);
            return deepFormatObject(parsed);
        } catch (Exception e) {
            log.error("JSON解析为复杂对象失败", e);
            return null;
        }
    }

    /**
     * 解析JSON字符串为Map
     *
     * @param jsonString JSON字符串
     * @return 解析后的Map
     */
    public static Map<String, Object> parseJsonToMap(String jsonString) {
        try {
            return JSON.parseObject(jsonString, Map.class);
        } catch (Exception e) {
            log.error("JSON解析失败", e);
            return null;
        }
    }

    /**
     * 完整的数据解密流程
     *
     * @param signedData 包含签名的完整数据
     * @param signkey    签名密钥
     * @return 解密后的原始数据，如果验证失败返回null
     */
    public static Map<String, Object> decryptData(Map<String, Object> signedData, String signkey) {
        try {
            // 1. 提取签名和时间
            String sign = (String) signedData.get("sign");
            Long signTime = (Long) signedData.get("signtime");
            
            if (sign == null || signTime == null) {
                log.error("缺少签名或签名时间");
                return null;
            }

            // 2. 提取原始数据
            Map<String, Object> originalData = extractOriginalData(signedData);

            // 3. 验证签名
            if (!verifySign(originalData, sign, signkey, signTime)) {
                log.error("签名验证失败");
                return null;
            }

            // 4. 返回原始数据
            return originalData;
        } catch (Exception e) {
            log.error("数据解密失败", e);
            return null;
        }
    }

    /**
     * 从加密字符串中解密数据（反向加密流程）
     * 注意：由于MD5是单向加密，无法直接解密，这里提供的是验证和提取原始数据的方法
     *
     * @param encryptedString 加密后的字符串
     * @param signkey         签名密钥
     * @return 原始数据
     */
    public static String decryptEncryptedString(String encryptedString, String signkey) {
        try {
            // 1. Base64解码
            String base64Decoded = decryptBase64Data(encryptedString);
            if (base64Decoded == null) {
                return null;
            }

            // 2. URL解码
            String urlDecoded = urlDecode(base64Decoded);
            if (urlDecoded == null) {
                return null;
            }

            // 3. 恢复特殊字符
            String restored = restoreSpecialCharacters(urlDecoded);
            
            return restored;
        } catch (Exception e) {
            log.error("字符串解密失败", e);
            return null;
        }
    }

    /**
     * 验证时间戳是否在有效期内
     *
     * @param signTime    签名时间
     * @param validPeriod 有效期（毫秒），默认5分钟
     * @return 是否在有效期内
     */
    public static boolean isSignTimeValid(long signTime, long validPeriod) {
        long currentTime = System.currentTimeMillis();
        long timeDiff = Math.abs(currentTime - signTime);
        return timeDiff <= validPeriod;
    }

    /**
     * 验证时间戳是否在有效期内（默认5分钟）
     *
     * @param signTime 签名时间
     * @return 是否在有效期内
     */
    public static boolean isSignTimeValid(long signTime) {
        return isSignTimeValid(signTime, 5 * 60 * 1000); // 5分钟
    }

    /**
     * 格式化时间戳为可读格式
     *
     * @param timestamp 时间戳
     * @param pattern   格式模式
     * @return 格式化后的时间字符串
     */
    public static String formatTimestamp(long timestamp, String pattern) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            return sdf.format(new Date(timestamp));
        } catch (Exception e) {
            log.error("时间格式化失败", e);
            return null;
        }
    }

    /**
     * 完整的数据验证和解密流程
     *
     * @param signedData   包含签名的完整数据
     * @param signkey      签名密钥
     * @param validPeriod  有效期（毫秒）
     * @return 验证和解密结果
     */
    public static DecryptResult validateAndDecrypt(Map<String, Object> signedData, String signkey, long validPeriod) {
        DecryptResult result = new DecryptResult();
        
        try {
            // 1. 提取签名和时间
            String sign = (String) signedData.get("sign");
            Long signTime = (Long) signedData.get("signtime");
            
            if (sign == null || signTime == null) {
                result.setSuccess(false);
                result.setMessage("缺少签名或签名时间");
                return result;
            }

            // 2. 验证时间戳
            if (!isSignTimeValid(signTime, validPeriod)) {
                result.setSuccess(false);
                result.setMessage("签名时间已过期");
                return result;
            }

            // 3. 提取原始数据
            Map<String, Object> originalData = extractOriginalData(signedData);

            // 4. 验证签名
            if (!verifySign(originalData, sign, signkey, signTime)) {
                result.setSuccess(false);
                result.setMessage("签名验证失败");
                return result;
            }

            // 5. 设置成功结果
            result.setSuccess(true);
            result.setData(originalData);
            result.setSignTime(signTime);
            result.setMessage("验证成功");
            
        } catch (Exception e) {
            log.error("数据验证和解密失败", e);
            result.setSuccess(false);
            result.setMessage("处理失败: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 解密结果类
     */
    public static class DecryptResult {
        private boolean success;
        private String message;
        private Map<String, Object> data;
        private Long signTime;

        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }

        public void setSuccess(boolean success) {
            this.success = success;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Map<String, Object> getData() {
            return data;
        }

        public void setData(Map<String, Object> data) {
            this.data = data;
        }

        public Long getSignTime() {
            return signTime;
        }

        public void setSignTime(Long signTime) {
            this.signTime = signTime;
        }

        @Override
        public String toString() {
            return "DecryptResult{" +
                    "success=" + success +
                    ", message='" + message + '\'' +
                    ", data=" + data +
                    ", signTime=" + signTime +
                    '}';
        }
    }
} 