package com.boot.server;

import cn.hutool.core.io.FileUtil;
import com.boot.server.util.ReportUtil;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ImageWriterTest {
    // 取决与在那个目录运行的 jar 这个环境变量获取到的就是当前目录
    private static final String dir = System.getProperty("user.dir");
    private static final String INPUT_IMAGE_MULTIPLE = dir + "/report/template/index.jpg";
    private static final String OUTPUT_DIR = dir + "/report/";

    static {
        if (!FileUtil.exist(OUTPUT_DIR)) {
            System.out.println(new File(OUTPUT_DIR).mkdirs());
        }

    }

    private static TemplateInfo initData() {
        TemplateInfo pptTemplateInfo = new TemplateInfo();
        pptTemplateInfo.setFfs("小猫来了")
                .setKitoNo("XMLL0009")
                .setPetName("Amy")
                .setPetRuNm("小七")
                .setPetCategory("布偶猫")
                .setSex("雌性")
                .setTaocan("基础健康套餐");

        List<TemplateDetail> items = new ArrayList<>();
        pptTemplateInfo.setItems(items);
        TemplateDetail item = new TemplateDetail();
        item.setProjectName("肥厚性心肌病");
        item.setPosition("c.95-108G>A");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("肥厚性心肌病");
        item.setPosition("c.5647G>A");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("肥厚性心肌病");
        item.setPosition("p.A31P");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("肥厚性心肌病");
        item.setPosition("p.R820W");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("肥厚性心肌病");
        item.setPosition("c.7384G>C");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("丙酮酸激酶缺乏症");
        item.setPosition("c.707-53G>A");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("渐进性视网膜萎缩");
        item.setPosition("c.1282delCT");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("渐进性视网膜萎缩");
        item.setPosition("c.1000G>A");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("多囊肾病");
        item.setPosition("c.9882C>A");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("折耳和软骨发育不良");
        item.setPosition("c.1024G>T");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("先天性肌无力");
        item.setPosition("c.1190G>A");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("头部缺陷");
        item.setPosition("c.497_508del");
        item.setRes("不致病");
        items.add(item);
        item = new TemplateDetail();
        item.setProjectName("第二页");
        item.setPosition("c.497_508del");
        item.setRes("致病");
        items.add(item);
        return pptTemplateInfo;
    }

    @Test
    public void testDrawTableWithBorder() {
        TemplateInfo templateInfo = initData();
        List<TemplateDetail> items = templateInfo.getItems();
        int totalItems = items.size();
        int pageSize = 12;
        int totalPages = (int) Math.ceil((double) totalItems / pageSize);

        for (int pageNum = 1; pageNum <= totalPages; pageNum++) {
            int fromIndex = (pageNum - 1) * pageSize;
            int toIndex = Math.min(fromIndex + pageSize, totalItems);
            List<TemplateDetail> pageItems = items.subList(fromIndex, toIndex);

            // 创建当前页的TemplateInfo
            TemplateInfo pageTemplateInfo = new TemplateInfo();
            pageTemplateInfo.setFfs(templateInfo.getFfs())
                    .setKitoNo(templateInfo.getKitoNo())
                    .setPetName(templateInfo.getPetName())
                    .setPetRuNm(templateInfo.getPetRuNm())
                    .setPetCategory(templateInfo.getPetCategory())
                    .setSex(templateInfo.getSex())
                    .setTaocan(templateInfo.getTaocan());
            pageTemplateInfo.setItems(pageItems);

            // 创建表格信息
            TableInfo tableInfo = getTableInfo(pageTemplateInfo);

            // 创建文字位置列表
            List<TextPosition> textPositions = new ArrayList<>();
            // 添加多组文字位置
            textPositions.add(ItemEnum.ORG.buildPosition(pageTemplateInfo.getFfs()));
            textPositions.add(ItemEnum.PET_NAME.buildPosition(pageTemplateInfo.getPetName()));
            textPositions.add(ItemEnum.PET_CLASSIFY.buildPosition(pageTemplateInfo.getPetCategory()));
            textPositions.add(ItemEnum.PET_GENDER.buildPosition(pageTemplateInfo.getSex()));

            // 生成输出文件名
            String outputFileName = totalPages == 1 ?
                    pageTemplateInfo.getKitoNo() + ".png" :
                    pageTemplateInfo.getKitoNo() + "_" + pageNum + ".png";

            // 渲染图片
            ReportUtil.drawTableWithBorder(
                    INPUT_IMAGE_MULTIPLE,
                    OUTPUT_DIR + outputFileName,
                    tableInfo,
                    textPositions
            );
        }
    }

    @NotNull
    private static TableInfo getTableInfo(TemplateInfo pptTemplateInfo) {
        List<TemplateDetail> details = pptTemplateInfo.getItems();

        List<List<String>> table = new ArrayList<>();
        // 添加表头
        table.add(Arrays.asList("检测项目", "位点名称", "结果解读"));
        // 添加数据行
        for (TemplateDetail detail : details) {
            table.add(Arrays.asList(detail.getProjectName(), detail.getPosition(), detail.getRes()));
        }

        TableInfo tableInfo = new TableInfo(
                350,   // 表格左上角x坐标（可根据实际图片调整）
                700,   // 表格左上角y坐标（可根据实际图片调整）
                300,   // 单元格宽度
                50,    // 单元格高度
                table
        );
        return tableInfo;
    }

    private static TemplateDetail createDetail(String projectName, String position, String res) {
        TemplateDetail detail = new TemplateDetail();
        detail.setProjectName(projectName);
        detail.setPosition(position);
        detail.setRes(res);
        return detail;
    }
}
