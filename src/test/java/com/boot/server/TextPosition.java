package com.boot.server;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * TextPosition
 *
 * <AUTHOR> 2025/5/15 22:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TextPosition {
    private String text;
    private int x;
    private int y;

    public static TextPosition of(ItemEnum item, String text) {
        return new TextPosition(text, item.getX(), item.getY());
    }
}
