package com.boot.server.listener;

import com.boot.server.dto.excel.OfflineOrderImportDto;
import com.boot.server.dto.response.ImportResultResponse;
import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppGoodsSpecsEntity;
import com.boot.server.repository.AppGoodsRepository;
import com.boot.server.repository.AppGoodsSpecsRepository;
import com.boot.server.repository.AppOfflineOrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 线下渠道订单Excel导入监听器测试
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@ExtendWith(MockitoExtension.class)
public class OfflineOrderImportListenerTest {

    @Mock
    private AppOfflineOrderRepository appOfflineOrderRepository;

    @Mock
    private AppGoodsRepository appGoodsRepository;

    @Mock
    private AppGoodsSpecsRepository appGoodsSpecsRepository;

    private ImportResultResponse importResult;
    private OfflineOrderImportListener listener;

    @BeforeEach
    void setUp() {
        importResult = new ImportResultResponse();
        listener = new OfflineOrderImportListener(
                appOfflineOrderRepository,
                appGoodsRepository,
                appGoodsSpecsRepository,
                importResult
        );

        // 设置基础Mock数据
        AppGoodsEntity goods = new AppGoodsEntity();
        goods.setId(1L);
        goods.setTitle("测试商品");

        AppGoodsSpecsEntity specs = new AppGoodsSpecsEntity();
        specs.setId(1L);
        specs.setGoodsId(1);
        specs.setTitle("测试规格");

        when(appGoodsRepository.getByTitle("测试商品")).thenReturn(goods);
        when(appGoodsSpecsRepository.getByGoodsIdAndTitle(1, "测试规格")).thenReturn(specs);
        when(appOfflineOrderRepository.getByKitsNo(any())).thenReturn(null);
        when(appOfflineOrderRepository.saveBatch(any())).thenReturn(true);
    }

    @Test
    void testExcelInternalDuplicateDetection() {
        // 创建第一条数据
        OfflineOrderImportDto data1 = new OfflineOrderImportDto();
        data1.setKitsNo("KIT001");
        data1.setGoodsName("测试商品");
        data1.setGoodsSpecsName("测试规格");

        // 创建第二条重复数据
        OfflineOrderImportDto data2 = new OfflineOrderImportDto();
        data2.setKitsNo("KIT001"); // 相同的试剂盒编号
        data2.setGoodsName("测试商品");
        data2.setGoodsSpecsName("测试规格");

        // 模拟处理第一条数据
        listener.invoke(data1, createMockContext(1));
        
        // 模拟处理第二条数据（应该检测到重复）
        listener.invoke(data2, createMockContext(2));

        // 验证结果
        assertEquals(0, importResult.getSuccessCount());
        assertEquals(1, importResult.getFailCount());
        assertTrue(importResult.getErrorMessages().get(0).contains("在Excel中重复"));
    }

    @Test
    void testMultipleSpecsProcessing() {
        // 设置多个规格的Mock数据
        AppGoodsSpecsEntity specs1 = new AppGoodsSpecsEntity();
        specs1.setId(1L);
        specs1.setGoodsId(1);
        specs1.setTitle("规格1");

        AppGoodsSpecsEntity specs2 = new AppGoodsSpecsEntity();
        specs2.setId(2L);
        specs2.setGoodsId(1);
        specs2.setTitle("规格2");

        when(appGoodsSpecsRepository.getByGoodsIdAndTitle(1, "规格1")).thenReturn(specs1);
        when(appGoodsSpecsRepository.getByGoodsIdAndTitle(1, "规格2")).thenReturn(specs2);

        // 创建多规格数据
        OfflineOrderImportDto data = new OfflineOrderImportDto();
        data.setKitsNo("KIT001");
        data.setGoodsName("测试商品");
        data.setGoodsSpecsName("规格1，规格2");

        // 处理数据
        listener.invoke(data, createMockContext(1));

        // 验证没有错误
        assertEquals(0, importResult.getFailCount());
        assertTrue(importResult.getErrorMessages().isEmpty());
    }

    private com.alibaba.excel.context.AnalysisContext createMockContext(int rowIndex) {
        // 这里可以创建一个简单的Mock Context
        // 为了简化测试，我们可以返回null，因为在实际测试中主要关注业务逻辑
        return new com.alibaba.excel.context.AnalysisContext() {
            @Override
            public com.alibaba.excel.read.metadata.holder.ReadRowHolder readRowHolder() {
                return new com.alibaba.excel.read.metadata.holder.ReadRowHolder() {
                    @Override
                    public Integer getRowIndex() {
                        return rowIndex;
                    }
                };
            }
            
            // 其他方法可以返回null或默认值
            @Override
            public com.alibaba.excel.read.metadata.holder.ReadSheetHolder readSheetHolder() { return null; }
            @Override
            public com.alibaba.excel.read.metadata.holder.ReadWorkbookHolder readWorkbookHolder() { return null; }
            @Override
            public com.alibaba.excel.read.metadata.holder.ReadHolder currentReadHolder() { return null; }
            @Override
            public Object getCustom() { return null; }
        };
    }
}
