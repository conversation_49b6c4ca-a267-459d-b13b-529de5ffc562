package com.boot.server;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * ItemEnum
 * Y轴+140
 * X轴+150
 *
 * <AUTHOR> 2025/5/15 22:32
 */
@Getter
@RequiredArgsConstructor
public enum ItemEnum {
    // 繁育机构
    ORG(480, 432),
    // 宠物名
    PET_NAME(1060, 432),
    // 品种
    PET_CLASSIFY(480, 495),
    // 性别
    PET_GENDER(1060, 495),
    // 宠物编号
    PET_NO(480, 555),
    ;
    private final Integer x;
    private final Integer y;

    public TextPosition buildPosition(String text) {
        return new TextPosition(text, this.getX(), this.getY());
    }
}
