//package com.boot.server;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.http.HttpRequest;
//import com.alibaba.fastjson.JSON;
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.xmll.breeding.command.param.CzyCallbackNotifyUrlParam;
//import com.xmll.breeding.command.param.CzyCreatePetParam;
//import com.xmll.breeding.command.param.CzySampleBreakParam;
//import com.xmll.breeding.command.param.CzyUpdatePetParam;
//import com.xmll.breeding.command.properties.TrialProperties;
//import com.xmll.breeding.command.query.CzyOrderQueryCmd;
//import com.xmll.breeding.command.query.CzyProductQueryCmd;
//import com.xmll.breeding.command.query.CzyReportQueryCmd;
//import com.xmll.breeding.command.res.*;
//import com.xmll.breeding.domain.ThirdPartyRequestLog;
//import com.xmll.breeding.domain.trial.CzyTrialCreateOrderRequest;
//import com.xmll.breeding.mapper.ThirdPartyRequestLogMapper;
//import com.xmll.common.core.constant.RemotingConstants;
//import com.xmll.common.core.domain.CzyTrialResult;
//import com.xmll.common.core.domain.PageResult;
//import com.xmll.common.core.exception.XmllException;
//import com.xmll.common.core.utils.sign.TrialSignUtil;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Component;
//
//import java.io.UnsupportedEncodingException;
//import java.time.LocalDateTime;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 宠知因检测接口客户端
// *
// * <AUTHOR>
// * @date 2023-12-22
// **/
//@Slf4j
//@Component
//@RequiredArgsConstructor
//public class CzyTrialHttpClient {
//
//    private final TrialProperties trialProperties;
//    private final ThirdPartyRequestLogMapper thirdPartyRequestLogMapper;
//
//    /**
//     * 创建订单
//     *
//     * @param request 请求参数
//     * @return 订单信息
//     */
//    public CzyTrialCreateOrderResponse createOrder(CzyTrialCreateOrderRequest request) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(16);
//        // 宠知因要求宠物名称唯一，所以传系统的猫咪编号过去
//        map.put("PetName", request.getCatCode());
//        map.put("ClassId", request.getClassId());
//        map.put("Sex", request.getSex());
//        map.put("MasterName", "小猫来了");
//        map.put("LanguageReport", 1);
//        if (request.getSampleType() != null) {
//            map.put("SampleType", request.getSampleType());
//        } else {
//            map.put("SampleType", 1);
//        }
//        if (request.getBarCode() != null) {
//            map.put("BarCode", request.getBarCode());
//        }
//        if (StrUtil.isNotBlank(request.getPetNumber())) {
//            map.put("PetNumber", request.getPetNumber());
//        }
//        if (StrUtil.isNotBlank(request.getShopName())) {
//            map.put("Consignee", request.getShopName());
//        }
//        if (request.getUseSampleHistory() != null) {
//            map.put("UseSampleHistory", request.getUseSampleHistory());
//        }
//        map.put("ProductIds", request.getProductIds());
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        if (StrUtil.isNotBlank(request.getMilkName())) {
//            map.put("Remark", request.getMilkName());
//        }
//        String sign = sign(map, signTime);
//        map.put("sign", sign);
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRIAL_CREATE_ORDER;
//        log.info("请求Url:{}, 请求接口:{}, 请求参数:{}", createOrderUrl, RemotingConstants.TRIAL_CREATE_ORDER, JSON.toJSONString(newMap));
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//        log.info("宠知因-创建订单结果：{}", body);
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//
//        // 保存请求日志
//        if (request.getUseSampleHistory() == 1) {
//            saveRequestLog("宠知因-追加订单", createOrderUrl, map, body, timeConsuming);
//        } else {
//            saveRequestLog("宠知因-创建订单", createOrderUrl, map, body, timeConsuming);
//        }
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因创建订单失败：" + czyTrialResult.getRetMsg());
//        }
//        if (request.getUseSampleHistory() != 1) {
//            JSONObject jsonBody = JSONObject.parseObject(body);
//            Object backRetDate = jsonBody.get("BackRetDate");
//            if (backRetDate == null || !(boolean) backRetDate) {
//                throw new XmllException(jsonBody.getString("OtherRetData"));
//            }
//        }
//        return JSON.parseObject(czyTrialResult.getRetData().toString(), CzyTrialCreateOrderResponse.class);
//    }
//
//    /**
//     * 查询订单
//     *
//     * @param queryCmd 查询从那时
//     * @return 订单信息
//     */
//    public PageResult<CzyTrialCreateOrderResponse> queryOrder(CzyOrderQueryCmd queryCmd) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(16);
//        if (StrUtil.isNotBlank(queryCmd.getPetNumber())) {
//            map.put("PetNumber", queryCmd.getPetNumber());
//        }
//        if (StrUtil.isNotBlank(queryCmd.getPetName())) {
//            map.put("PetName", queryCmd.getPetName());
//        }
//        if (StrUtil.isNotBlank(queryCmd.getOrderNo())) {
//            map.put("OrderNo", queryCmd.getOrderNo());
//        }
//        if (queryCmd.getOrderState() != null) {
//            map.put("OrderState", queryCmd.getOrderState());
//        }
//        map.put("PageIndex", queryCmd.getPageIndex());
//        map.put("PageSize", queryCmd.getPageSize());
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRIAL_SELECT_ORDER;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-查询订单", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因查询订单失败：" + czyTrialResult.getRetMsg());
//        }
//        JSONObject retData = JSON.parseObject(czyTrialResult.getRetData().toString());
//        Integer rowCount = retData.getInteger("RowCount");
//        if (rowCount == 0) {
//            return new PageResult<>();
//        }
//        List<CzyTrialCreateOrderResponse> createOrderResponseList = JSON.parseArray(retData.get("Data").toString(), CzyTrialCreateOrderResponse.class);
//        PageResult<CzyTrialCreateOrderResponse> pageResult = new PageResult<>();
//        pageResult.setPageNum(Long.valueOf(retData.getInteger("PageIndex")));
//        pageResult.setPageSize(Long.valueOf(retData.getInteger("PageSize")));
//        pageResult.setTotal(Long.valueOf(retData.getInteger("RowCount")));
//        pageResult.setRows(createOrderResponseList);
//        return pageResult;
//    }
//
//    /**
//     * 取消订单
//     *
//     * @return 结果
//     */
//    public Boolean cancelOrder(String orderNo) {
//        if (StrUtil.isBlank(orderNo)) {
//            throw new XmllException("订单号不能为空");
//        }
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("OrderNo", orderNo);
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRIAL_CANCEL_ORDER;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-取消订单", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因取消订单失败：" + czyTrialResult.getRetMsg());
//        }
//        Boolean retData = (Boolean) czyTrialResult.getRetData();
//        if (!retData) {
//            throw new XmllException("调用宠知因取消订单失败");
//        }
//        return true;
//    }
//
//    /**
//     * 查询采样包是否已绑定
//     *
//     * @return 布尔
//     */
//    public CzyBarPackQueryResponse queryBarPack(String barCode) {
//        if (StrUtil.isBlank(barCode)) {
//            throw new XmllException("采样包编号不能为空");
//        }
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("BarCode", barCode);
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_SELECT_BARPACK;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-查询采样包是否已绑定", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException(czyTrialResult.getRetMsg());
//        }
//        return JSON.parseObject(czyTrialResult.getRetData().toString(), CzyBarPackQueryResponse.class);
//    }
//
//    /**
//     * 绑定采样包
//     *
//     * @param barCode    采样包编号
//     * @param petNumber  宠物编号
//     * @param sampleType 样本编号
//     * @return 绑定结果
//     */
//    public Boolean bindBar(String barCode, String petNumber, Integer sampleType) {
//        if (StrUtil.isBlank(barCode) || StrUtil.isBlank(petNumber)) {
//            throw new XmllException("采样包编号或宠物编号不能为空");
//        }
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("BarCode", barCode);
//        map.put("PetNumber", petNumber);
//        if (sampleType != null) {
//            map.put("SampleType", sampleType);
//        }
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_BIND_BAR;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-绑定采样包", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因绑定采样包失败：" + czyTrialResult.getRetMsg());
//        }
//        Boolean retData = (Boolean) czyTrialResult.getRetData();
//        if (!retData) {
//            throw new XmllException("调用宠知因绑定采样包失败");
//        }
//        return true;
//    }
//
//    /**
//     * 查询待回寄样本订单列表
//     *
//     * @return 待回寄样本订单列表
//     */
//    public PageResult<CzyTrialCreateOrderResponse> getSampleNotDeliveryOrderList(CzyOrderQueryCmd queryCmd) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        if (StrUtil.isNotBlank(queryCmd.getPetNumber())) {
//            map.put("PetNumber", queryCmd.getPetNumber());
//        }
//        if (StrUtil.isNotBlank(queryCmd.getPetName())) {
//            map.put("PetName", queryCmd.getPetName());
//        }
//        if (StrUtil.isNotBlank(queryCmd.getOrderNo())) {
//            map.put("OrderNo", queryCmd.getOrderNo());
//        }
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_SAMPLE_NOT_DELIVERY;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-待回寄样本订单列表", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因查询待回寄样本订单列表失败：" + czyTrialResult.getRetMsg());
//        }
//        JSONObject retData = JSON.parseObject(czyTrialResult.getRetData().toString());
//        List<CzyTrialCreateOrderResponse> createOrderResponseList = JSON.parseArray(retData.get("Data").toString(), CzyTrialCreateOrderResponse.class);
//        PageResult<CzyTrialCreateOrderResponse> pageResult = new PageResult<>();
//        pageResult.setPageNum(Long.valueOf(retData.getInteger("PageIndex")));
//        pageResult.setPageSize(Long.valueOf(retData.getInteger("PageSize")));
//        pageResult.setTotal(Long.valueOf(retData.getInteger("RowCount")));
//        pageResult.setRows(createOrderResponseList);
//        return pageResult;
//    }
//
//    /**
//     * 回寄样本快递下单
//     *
//     * @param sampleBreak 样品信息
//     * @return 布尔
//     */
//    public String sampleBreak(CzySampleBreakParam sampleBreak) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(16);
//        map.put("Consignee", sampleBreak.getConsignee());
//        map.put("ConsigneePhone", sampleBreak.getConsigneePhone());
//        map.put("Address", sampleBreak.getAddress());
//        map.put("OrderSampleIds", sampleBreak.getOrderSampleIds());
//        if (StrUtil.isNotBlank(sampleBreak.getPickUpTime())) {
//            map.put("PickUpTime", sampleBreak.getPickUpTime());
//        }
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_SAMP_BREAK;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-回寄样本快递下单", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因回寄样本快递下单失败：" + czyTrialResult.getRetMsg());
//        }
//        Boolean retData = (Boolean) czyTrialResult.getRetData();
//        if (!retData) {
//            throw new XmllException("调用宠知因回寄样本快递下单失败");
//        }
//
//        JSONObject jsonBody = JSONObject.parseObject(body);
//        Object backRetDate = jsonBody.get("BackRetDate");
//        if (backRetDate == null) {
//            return null;
//        }
//        return backRetDate.toString();
//    }
//
//    /**
//     * 报告查询
//     *
//     * @return 页面结果<czy报告列表响应>
//     */
//    public String getReport(CzyReportQueryCmd queryCmd) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        if (StrUtil.isNotBlank(queryCmd.getPetNumber())) {
//            map.put("PetNumber", queryCmd.getPetNumber());
//        }
//        if (StrUtil.isNotBlank(queryCmd.getPetName())) {
//            map.put("PetName", queryCmd.getPetName());
//        }
//        if (StrUtil.isNotBlank(queryCmd.getOrderNo())) {
//            map.put("OrderNo", queryCmd.getOrderNo());
//        }
//        if (StrUtil.isNotBlank(queryCmd.getProductId())) {
//            map.put("ProductId", queryCmd.getProductId());
//        }
//        if (queryCmd.getTestState() != null) {
//            map.put("TestState", queryCmd.getTestState());
//        }
//        map.put("PageIndex", queryCmd.getPageIndex());
//        map.put("PageSize", queryCmd.getPageSize());
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.GET_REPORT;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-报告查询", createOrderUrl, map, body, timeConsuming);
//
//        return body;
//    }
//
//    public PageResult<CzyProductListResponse> getProductList(CzyProductQueryCmd queryCmd) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        if (CollUtil.isNotEmpty(queryCmd.getClassIds())) {
//            map.put("ClassIds", queryCmd.getClassIds());
//        }
//        map.put("PageIndex", queryCmd.getPageIndex());
//        map.put("PageSize", queryCmd.getPageSize());
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.GET_PRODUCT;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-项目查询", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因项目查询失败：" + czyTrialResult.getRetMsg());
//        }
//        JSONObject retData = JSON.parseObject(czyTrialResult.getRetData().toString());
//        List<CzyProductListResponse> createOrderResponseList = JSON.parseArray(retData.get("Data").toString(), CzyProductListResponse.class);
//        PageResult<CzyProductListResponse> pageResult = new PageResult<>();
//        pageResult.setPageNum(Long.valueOf(retData.getInteger("PageIndex")));
//        pageResult.setPageSize(Long.valueOf(retData.getInteger("PageSize")));
//        pageResult.setTotal(Long.valueOf(retData.getInteger("RowCount")));
//        pageResult.setRows(createOrderResponseList);
//        return pageResult;
//    }
//
//    /**
//     * 推送通知地址配置
//     *
//     * @param czyCallbackNotifyUrlParam 推送地址URL
//     * @return 配置结果
//     */
//    public Boolean setNotifyUrl(CzyCallbackNotifyUrlParam czyCallbackNotifyUrlParam) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("NotifyUrl_OrderState", czyCallbackNotifyUrlParam.getNotifyUrl());
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_SET_NOTIFY;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-推送地址设置", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因推送地址设置失败：" + czyTrialResult.getRetMsg());
//        }
//        Boolean retData = (Boolean) czyTrialResult.getRetData();
//        if (!retData) {
//            throw new XmllException("调用宠知因推送地址设置失败");
//        }
//        return true;
//    }
//
//    /**
//     * 创建宠物
//     *
//     * @return 宠物编号
//     */
//    public String createPet(CzyCreatePetParam param) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(16);
//        map.put("PetCNName", param.getPetCnName());
//        map.put("ClassId", param.getClassId());
//        map.put("Sex", param.getSex());
//        map.put("MasterName", "小猫来了");
//        map.put("SpeciesId", "f0000000-0000-0000-0000-000000000000");
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_CREATE_PET;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//        log.info("创建宠物结果：{}", body);
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-创建宠物", createOrderUrl, map, body, timeConsuming);
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            // 创建宠物失败则查询宠物
//            return queryPetInfo(param.getPetCnName());
//        }
//        JSONObject retData = JSON.parseObject(czyTrialResult.getRetData().toString());
//        return retData.getString("PetNumber");
//    }
//
//    /**
//     * 修改宠物
//     *
//     * @return 宠物编号
//     */
//    public Boolean updatePet(CzyUpdatePetParam param) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(16);
//        map.put("PetNumber", param.getPetNumber());
//        if (StrUtil.isNotBlank(param.getClassId())) {
//            map.put("ClassId", param.getClassId());
//        }
//        map.put("PetCNName", param.getPetCnName());
//        map.put("Sex", param.getSex());
//        map.put("MasterName", "小猫来了");
//        map.put("AssociationId", "00000000-0000-0000-0000-000000000000");
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_UPDATE_PET;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//        log.info("修改宠物结果：{}", body);
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-修改宠物", createOrderUrl, map, body, timeConsuming);
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        return czyTrialResult.getRetCode() == 0;
//    }
//
//    /**
//     * 根据宠物名称查询宠知因宠物信息
//     *
//     * @param catCode 宠物编号
//     * @return 宠知因宠物信息
//     */
//    public String queryPetInfo(String catCode) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("PetCNNameAll", catCode);
//        map.put("MasterName", "小猫来了");
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_GET_PET;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//        log.info("宠物查询结果：{}", body);
//
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-查询宠物", createOrderUrl, map, body, timeConsuming);
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因宠物查询失败：" + czyTrialResult.getRetMsg());
//        }
//        JSONObject retData = JSON.parseObject(czyTrialResult.getRetData().toString());
//        JSONArray data = retData.getJSONArray("Data");
//        if (CollUtil.isEmpty(data)) {
//            return null;
//        }
//        JSONObject resultData = JSONObject.parseObject(data.get(0).toString());
//        return resultData.getString("PetNumber");
//    }
//
//    /**
//     * 查询账户余额
//     *
//     * @return 字符串
//     */
//    public CzyMemberCashQueryResponse getMemberCash() {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_GET_CASH;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-查询账户余额", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因查询账户余额失败：" + czyTrialResult.getRetMsg());
//        }
//        JSONObject jsonBody = JSONObject.parseObject(body);
//        Object backRetDate = jsonBody.get("OtherRetData");
//        if (backRetDate == null) {
//            return null;
//        }
//        return JSON.parseObject(backRetDate.toString(), CzyMemberCashQueryResponse.class);
//    }
//
//    /**
//     * 查询最新历史样本
//     *
//     * @param petNumber  宠知因宠物编号
//     * @param productIds 项目id，选择该项目可用样本
//     * @return 历史样本信息
//     */
//    public CzyPetOldSampleResponse getPetOldSample(String petNumber, List<String> productIds) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("petNumber", petNumber);
//        if (CollUtil.isNotEmpty(productIds)) {
//            map.put("productId", productIds);
//        }
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        map.put("sign", sign(map, signTime));
//        Map<String, Object> newMap = TrialSignUtil.objSortAndFormat(map);
//        String createOrderUrl = trialProperties.getHost() + RemotingConstants.TRAIL_GET_PET_OLD_SAMPLE;
//        String body = HttpRequest.post(createOrderUrl).body(JSON.toJSONString(newMap)).execute().body();
//        // 请求耗时
//        long timeConsuming = System.currentTimeMillis() - signTime;
//        // 保存请求日志
//        saveRequestLog("宠知因-查询最新历史样本", createOrderUrl, map, body, timeConsuming);
//
//        CzyTrialResult czyTrialResult = JSON.parseObject(body, CzyTrialResult.class);
//        if (czyTrialResult.getRetCode() != 0) {
//            throw new XmllException("宠知因查询最新历史样本失败：" + czyTrialResult.getRetMsg());
//        }
//        if (czyTrialResult.getRetData() == null) {
//            return new CzyPetOldSampleResponse();
//        }
//        JSONObject jsonObject = JSONObject.parseObject(czyTrialResult.getRetData().toString());
//
//        CzyPetOldSampleResponse petSampBoxInfo = JSON.parseObject(jsonObject.getString("PetSampBoxInfo"), CzyPetOldSampleResponse.class);
//        if (petSampBoxInfo == null) {
//            return new CzyPetOldSampleResponse();
//        }
//        // 时间格式化
//        if (StrUtil.isNotBlank(petSampBoxInfo.getConfirmTime())) {
//            String confirmTime = petSampBoxInfo.getConfirmTime();
//            petSampBoxInfo.setConfirmTime(TrialSignUtil.objFormat(confirmTime));
//        }
//        return petSampBoxInfo;
//    }
//
//    /**
//     * 打印报告
//     *
//     * @return 打印地址
//     */
//    public String printReport(String reportNumber) {
//        long signTime = System.currentTimeMillis();
//        String apiKey = trialProperties.getApiKey();
//        Map<String, Object> map = new HashMap<>(8);
//        map.put("reportNumber", reportNumber);
//        map.put("apikey", apiKey);
//        map.put("signtime", signTime);
//        String printUrl = trialProperties.getHost() + RemotingConstants.TRAIL_PRINT_REPORT;
//        return printUrl + "?apikey=" + apiKey + "&sign=" + sign(map, signTime) + "&signtime=" + signTime + "&reportNumber=" + reportNumber;
//    }
//
//    /**
//     * 查询用户待送达采样包对应宠物列表
//     *
//     * @param petNumber  宠物数量
//     * @param productIds 产品id
//     * @return 字符串
//     */
//    public String getSamplePackageCountForMember(String petNumber, List<String> productIds) {
//        return null;
//    }
//
//    private String sign(Map<String, Object> map, Long signTime) {
//        String sign = null;
//        try {
//            sign = TrialSignUtil.signData(map, trialProperties.getSignKey(), signTime);
//        } catch (UnsupportedEncodingException e) {
//            log.error("宠知因创建订单加密签名异常：{}", e.getMessage(), e);
//        }
//        return sign;
//    }
//
//
//    private void saveRequestLog(String apiName, String createOrderUrl, Map<String, Object> newMap, String
//            body, Long timeConsuming) {
//        ThirdPartyRequestLog thirdPartyRequestLog = new ThirdPartyRequestLog();
//        thirdPartyRequestLog.setApiName(apiName);
//        thirdPartyRequestLog.setApiUrl(createOrderUrl);
//        thirdPartyRequestLog.setRequestParam(JSON.toJSONString(newMap));
//        thirdPartyRequestLog.setResponseParam(body);
//        thirdPartyRequestLog.setTimeConsuming(timeConsuming);
//        thirdPartyRequestLog.setCreateTime(LocalDateTime.now());
//        thirdPartyRequestLogMapper.insert(thirdPartyRequestLog);
//    }
//}
