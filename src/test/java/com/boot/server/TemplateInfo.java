package com.boot.server;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description TODO
 * @date 2025/5/15 17:32
 */
@Accessors(chain = true)
@Data
public class TemplateInfo {
    // 繁育社
    private String ffs;
    // 检测编号
    private String kitoNo;
    // 宠物名称
    private String petName;
    // 宠物乳名
    private String petRuNm;
    // 宠物品种
    private String petCategory;
    // 宠物性别
    private String sex;
    // 套餐
    private String taocan;

    private List<TemplateDetail> items = new ArrayList<>();
}
