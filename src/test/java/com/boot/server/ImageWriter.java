package com.boot.server;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

public class ImageWriter {

    // 默认字体设置
    private static final String DEFAULT_FONT_NAME = "微软雅黑";
    private static final int DEFAULT_FONT_STYLE = Font.PLAIN;
    private static final int DEFAULT_FONT_SIZE = 75;
    private static final Color DEFAULT_COLOR = Color.BLACK;

    /**
     * 在图片上写入文字
     *
     * @param imagePath  原始图片路径
     * @param outputPath 输出图片路径
     * @param text       要写入的文字
     * @param x          文字起始x坐标
     * @param y          文字起始y坐标
     * @param fontSize   字体大小
     * @param color      文字颜色
     * @return 是否写入成功
     */
    public static boolean writeTextOnImage(String imagePath, String outputPath,
                                           String text, int x, int y,
                                           int fontSize, Color color) {
        try {
            // 读取原始图片
            BufferedImage image = ImageIO.read(new File(imagePath));

            // 创建Graphics2D对象
            Graphics2D g2d = image.createGraphics();

            // 设置文字渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                    RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            // 设置字体
            Font font = new Font("微软雅黑", Font.PLAIN, fontSize);
            g2d.setFont(font);

            // 设置颜色
            g2d.setColor(color);

            // 绘制文字
            g2d.drawString(text, x, y);

            // 释放资源
            g2d.dispose();

            // 保存图片
            return ImageIO.write(image, "png", new File(outputPath));

        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 在图片上写入文字的简化方法（使用默认颜色和字体）
     *
     * @param imagePath  原始图片路径
     * @param outputPath 输出图片路径
     * @param text       要写入的文字
     * @param x          文字起始x坐标
     * @param y          文字起始y坐标
     * @return 是否写入成功
     */
    public static boolean writeTextOnImage(String imagePath, String outputPath,
                                           String text, int x, int y) {
        return writeTextOnImage(imagePath, outputPath, text, x, y, 20, Color.BLACK);
    }

    /**
     * 在图片上写入文字（支持自定义字体）
     *
     * @param imagePath  原始图片路径
     * @param outputPath 输出图片路径
     * @param text       要写入的文字
     * @param x          文字起始x坐标
     * @param y          文字起始y坐标
     * @param fontName   字体名称（如：微软雅黑、宋体、Arial等）
     * @param fontStyle  字体样式（Font.PLAIN=0, Font.BOLD=1, Font.ITALIC=2,
     *                   Font.BOLD|Font.ITALIC=3）
     * @param fontSize   字体大小
     * @param color      文字颜色
     * @return 是否写入成功
     */
    public static boolean writeTextOnImage(String imagePath, String outputPath,
                                           String text, int x, int y,
                                           String fontName, int fontStyle,
                                           int fontSize, Color color) {
        try {
            // 读取原始图片
            BufferedImage image = ImageIO.read(new File(imagePath));

            // 创建Graphics2D对象
            Graphics2D g2d = image.createGraphics();

            // 设置文字渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING,
                    RenderingHints.VALUE_TEXT_ANTIALIAS_ON);

            // 设置字体
            Font font = new Font(fontName, fontStyle, fontSize);
            g2d.setFont(font);

            // 设置颜色
            g2d.setColor(color);

            // 绘制文字
            g2d.drawString(text, x, y);

            // 释放资源
            g2d.dispose();

            // 保存图片
            return ImageIO.write(image, "png", new File(outputPath));

        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取系统所有可用字体名称
     *
     * @return 字体名称数组
     */
    public static String[] getAvailableFontNames() {
        return GraphicsEnvironment.getLocalGraphicsEnvironment()
                .getAvailableFontFamilyNames();
    }

    /**
     * 在图片上批量写入多组文字（使用固定字体样式）
     *
     * @param imagePath     原始图片路径
     * @param outputPath    输出图片路径
     * @param textPositions 文字位置列表
     * @return 是否写入成功
     */
    public static boolean writeTextWithFixedStyle(String imagePath, String outputPath,
                                                  List<TextPosition> textPositions) {
        try {
            // 读取原始图片
            BufferedImage image = ImageIO.read(new File(imagePath));
            // 创建Graphics2D对象
            Graphics2D g2d = image.createGraphics();
            // 设置文字渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 设置固定字体
            Font font = new Font(DEFAULT_FONT_NAME, Font.PLAIN, DEFAULT_FONT_SIZE);
            g2d.setFont(font);
            g2d.setColor(DEFAULT_COLOR);
            // 遍历文字位置列表，逐个写入文字
            for (TextPosition position : textPositions) {
                g2d.drawString(position.getText(), position.getX(), position.getY());
            }
            // 释放资源
            g2d.dispose();

            // 保存图片
            return ImageIO.write(image, "png", new File(outputPath));

        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 在图片上绘制表格内容和横线，支持第0列内容自动合并单元格。
     * <p>
     * 合并规则：第0列内容连续相同的多行会合并为一个大单元格，只在合并区间首行绘制内容，
     * 合并区间内部不绘制横线，文字居中显示。其余单元格正常显示，表头加粗。
     *
     * @param imagePath  原始图片路径
     * @param outputPath 输出图片路径
     * @param tableInfo  表格信息（包括起始坐标、单元格宽高、内容）
     * @return 是否写入成功
     */
    public static boolean drawTableWithBorder(String imagePath, String outputPath, TableInfo tableInfo, List<TextPosition> textPositions) {
        try {
            BufferedImage image = ImageIO.read(new File(imagePath));
            // 创建Graphics2D对象
            Graphics2D g2d = image.createGraphics();
            // 设置文字渲染质量
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            // 设置固定字体
            Font titleFont = new Font(DEFAULT_FONT_NAME, Font.PLAIN, DEFAULT_FONT_SIZE);
            g2d.setFont(titleFont);
            g2d.setColor(DEFAULT_COLOR);
            // 遍历文字位置列表，逐个写入文字
            for (TextPosition position : textPositions) {
                g2d.drawString(position.getText(), position.getX(), position.getY());
            }

            int rows = tableInfo.getContent().size();
            int cols = tableInfo.getContent().get(0).size();

            // 1. 计算第0列需要合并的区间（起始行 -> 合并行数）
            java.util.Map<Integer, Integer> mergeStartRow = new java.util.HashMap<>();
            boolean[] isMergedRow = new boolean[rows];
            int i = 0;
            while (i < rows) {
                String val = tableInfo.getContent().get(i).get(0);
                int j = i + 1;
                while (j < rows && val != null && val.equals(tableInfo.getContent().get(j).get(0))) {
                    j++;
                }
                int mergeLen = j - i;
                if (mergeLen > 1) {
                    mergeStartRow.put(i, mergeLen);
                    for (int r = i + 1; r < i + mergeLen; r++) {
                        isMergedRow[r] = true;
                    }
                }
                i = j;
            }

            // 1.1 标记哪些横线需要跳过
            boolean[] skipLine = new boolean[rows + 1];
            for (java.util.Map.Entry<Integer, Integer> entry : mergeStartRow.entrySet()) {
                int start = entry.getKey();
                int len = entry.getValue();
                for (int l = start + 1; l < start + len; l++) {
                    skipLine[l] = true;
                }
            }

            // 2. 缓存字体和FontMetrics
            Font headerFont = getDefaultFont(0);
            Font normalFont = getDefaultFont(1);
            FontMetrics headerMetrics = g2d.getFontMetrics(headerFont);
            FontMetrics normalMetrics = g2d.getFontMetrics(normalFont);

            // 3. 写入表格内容
            for (int row = 0; row < rows; row++) {
                boolean isHeader = (row == 0);
                for (int col = 0; col < cols; col++) {
                    if (col == 0) {
                        if (mergeStartRow.containsKey(row)) {
                            int mergeRows = mergeStartRow.get(row);
                            String cellText = tableInfo.getContent().get(row).get(0);
                            int cellX = tableInfo.getStartX();
                            int cellY = tableInfo.getStartY() + row * tableInfo.getCellHeight();
                            int cellHeight = tableInfo.getCellHeight() * mergeRows;
                            Font font = isHeader ? headerFont : normalFont;
                            FontMetrics metrics = isHeader ? headerMetrics : normalMetrics;
                            int textWidth = metrics.stringWidth(cellText);
                            int x = cellX + (tableInfo.getCellWidth() - textWidth) / 2;
                            int y = cellY + (cellHeight - metrics.getHeight()) / 2 + metrics.getAscent();
                            g2d.setFont(font);
                            g2d.setColor(DEFAULT_COLOR);
                            g2d.drawString(cellText, x, y);
                        } else if (isMergedRow[row]) {
                            continue;
                        } else {
                            String cellText = tableInfo.getContent().get(row).get(0);
                            int cellX = tableInfo.getStartX();
                            int cellY = tableInfo.getStartY() + row * tableInfo.getCellHeight();
                            Font font = isHeader ? headerFont : normalFont;
                            FontMetrics metrics = isHeader ? headerMetrics : normalMetrics;
                            int textWidth = metrics.stringWidth(cellText);
                            int x = cellX + (tableInfo.getCellWidth() - textWidth) / 2;
                            int y = cellY + (tableInfo.getCellHeight() - metrics.getHeight()) / 2 + metrics.getAscent();
                            g2d.setFont(font);
                            g2d.setColor(DEFAULT_COLOR);
                            g2d.drawString(cellText, x, y);
                        }
                    } else {
                        String cellText = tableInfo.getContent().get(row).get(col);
                        int cellX = tableInfo.getStartX() + col * tableInfo.getCellWidth();
                        int cellY = tableInfo.getStartY() + row * tableInfo.getCellHeight();
                        Font font = isHeader ? headerFont : normalFont;
                        FontMetrics metrics = isHeader ? headerMetrics : normalMetrics;
                        int textWidth = metrics.stringWidth(cellText);
                        int x = cellX + (tableInfo.getCellWidth() - textWidth) / 2;
                        int y = cellY + (tableInfo.getCellHeight() - metrics.getHeight()) / 2 + metrics.getAscent();
                        g2d.setFont(font);
                        g2d.setColor(DEFAULT_COLOR);
                        g2d.drawString(cellText, x, y);
                    }
                }
            }

            // 4. 画黑色加粗横线（合并区间内部不画横线）
            g2d.setColor(Color.BLACK);
            for (int line = 0; line <= rows; line++) {
                if (skipLine[line]) continue;
                int y = tableInfo.getStartY() + line * tableInfo.getCellHeight();
                if (line == 0) {
                    g2d.setStroke(new BasicStroke(8)); // 第一条横线更粗
                } else {
                    g2d.setStroke(new BasicStroke(5)); // 其余横线
                }
                g2d.drawLine(tableInfo.getStartX(), y, tableInfo.getStartX() + cols * tableInfo.getCellWidth(), y);
            }

            g2d.dispose();
            return ImageIO.write(image, "png", new File(outputPath));
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取表格默认字体，表头加粗。
     * @param row 行号
     * @return Font
     */
    private static Font getDefaultFont(int row) {
        return Objects.equals(row, 0) ? new Font(DEFAULT_FONT_NAME, Font.BOLD, 50) : new Font(DEFAULT_FONT_NAME, Font.PLAIN, 50);
    }
}
