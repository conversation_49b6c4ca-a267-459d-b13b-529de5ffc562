package com.boot.server;

import cn.hutool.core.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 验签AOP切面测试类
 * 演示如何使用@SignVerify注解
 *
 * <AUTHOR>
 * @date 2023-12-23
 **/
@Slf4j
public class SignVerifyAspectTest {

    public static void main(String[] args) {
        System.out.println(RandomUtil.randomString(32));
        // 测试密钥
        String signkey = "a3e4ab91ca39-1e57-ff07-890f-0d5b7617";
//
        log.info("=== 开始验签AOP切面测试 ===");
//
        // 测试1: 基本验签功能
        testBasicSignVerify(signkey);
        
//        // 测试2: 嵌套对象验签
//        testNestedObjectSignVerify(signkey);
//
//        // 测试3: 自定义字段名验签
//        testCustomFieldSignVerify(signkey);
//
//        // 测试4: 验签失败情况
//        testSignVerifyFailure(signkey);
        
        log.info("=== 验签AOP切面测试完成 ===");
    }
    
    /**
     * 测试基本验签功能
     */
    private static void testBasicSignVerify(String signkey) {
        log.info("--- 测试基本验签功能 ---");
        
        try {
            // 准备测试数据
            Map<String, Object> testData = new HashMap<>();
            testData.put("OrderNo", "94");
            // 生成签名
            long signTime = System.currentTimeMillis();
            String sign = TrialSignUtil.signData(testData, signkey, signTime);
            
            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>(testData);
            requestData.put("sign", sign);
            requestData.put("signtime", signTime);
            
            log.info("测试数据: {}", requestData);
            log.info("生成的签名: {}", sign);
            
            // 模拟AOP验签过程
            boolean verifyResult = simulateSignVerify(requestData, signkey);
            log.info("验签结果: {}", verifyResult);
            
        } catch (Exception e) {
            log.error("基本验签测试失败", e);
        }
    }
    
    /**
     * 测试嵌套对象验签
     */
    private static void testNestedObjectSignVerify(String signkey) {
        log.info("--- 测试嵌套对象验签 ---");
        
        try {
            // 准备包含嵌套对象的测试数据
            Map<String, Object> testData = new HashMap<>();
            testData.put("userId", "12345");
            testData.put("userName", "张三");
            
            // 嵌套的用户信息
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("age", 25);
            userInfo.put("email", "<EMAIL>");
            userInfo.put("phone", "13800138000");
            testData.put("userInfo", userInfo);
            
            // 嵌套的地址信息
            Map<String, Object> address = new HashMap<>();
            address.put("province", "北京市");
            address.put("city", "北京市");
            address.put("district", "朝阳区");
            List<String> tags = Arrays.asList("工作地", "居住地");
            address.put("tags", tags);
            testData.put("address", address);
            
            // 生成签名
            long signTime = System.currentTimeMillis();
            String sign = TrialSignUtil.signData(testData, signkey, signTime);
            
            // 构建请求数据
            Map<String, Object> requestData = new HashMap<>(testData);
            requestData.put("sign", sign);
            requestData.put("signtime", signTime);
            
            log.info("嵌套对象测试数据: {}", requestData);
            log.info("生成的签名: {}", sign);
            
            // 模拟AOP验签过程
            boolean verifyResult = simulateSignVerify(requestData, signkey);
            log.info("嵌套对象验签结果: {}", verifyResult);
            
        } catch (Exception e) {
            log.error("嵌套对象验签测试失败", e);
        }
    }
    
    /**
     * 测试自定义字段名验签
     */
    private static void testCustomFieldSignVerify(String signkey) {
        log.info("--- 测试自定义字段名验签 ---");
        
        try {
            // 准备测试数据
            Map<String, Object> testData = new HashMap<>();
            testData.put("userId", "12345");
            testData.put("userName", "李四");
            testData.put("action", "register");
            
            // 生成签名
            long signTime = System.currentTimeMillis();
            String sign = TrialSignUtil.signData(testData, signkey, signTime);
            
            // 构建请求数据（使用自定义字段名）
            Map<String, Object> requestData = new HashMap<>(testData);
            requestData.put("customSign", sign);  // 自定义签名字段
            requestData.put("customTime", signTime);  // 自定义时间戳字段
            
            log.info("自定义字段名测试数据: {}", requestData);
            log.info("生成的签名: {}", sign);
            
            // 模拟AOP验签过程（使用自定义字段名）
            boolean verifyResult = simulateCustomFieldSignVerify(requestData, signkey);
            log.info("自定义字段名验签结果: {}", verifyResult);
            
        } catch (Exception e) {
            log.error("自定义字段名验签测试失败", e);
        }
    }
    
    /**
     * 测试验签失败情况
     */
    private static void testSignVerifyFailure(String signkey) {
        log.info("--- 测试验签失败情况 ---");
        
        try {
            // 测试1: 缺少签名
            Map<String, Object> noSignData = new HashMap<>();
            noSignData.put("userId", "12345");
            noSignData.put("signtime", System.currentTimeMillis());
            boolean result1 = simulateSignVerify(noSignData, signkey);
            log.info("缺少签名验签结果: {}", result1);
            
            // 测试2: 缺少时间戳
            Map<String, Object> noTimeData = new HashMap<>();
            noTimeData.put("userId", "12345");
            noTimeData.put("sign", "fake_sign");
            boolean result2 = simulateSignVerify(noTimeData, signkey);
            log.info("缺少时间戳验签结果: {}", result2);
            
            // 测试3: 错误的签名
            Map<String, Object> wrongSignData = new HashMap<>();
            wrongSignData.put("userId", "12345");
            wrongSignData.put("sign", "wrong_sign");
            wrongSignData.put("signtime", System.currentTimeMillis());
            boolean result3 = simulateSignVerify(wrongSignData, signkey);
            log.info("错误签名验签结果: {}", result3);
            
            // 测试4: 过期的时间戳
            Map<String, Object> expiredData = new HashMap<>();
            expiredData.put("userId", "12345");
            expiredData.put("sign", "fake_sign");
            expiredData.put("signtime", System.currentTimeMillis() - 10 * 60 * 1000); // 10分钟前
            boolean result4 = simulateSignVerify(expiredData, signkey);
            log.info("过期时间戳验签结果: {}", result4);
            
        } catch (Exception e) {
            log.error("验签失败情况测试失败", e);
        }
    }
    
    /**
     * 模拟AOP验签过程
     */
    private static boolean simulateSignVerify(Map<String, Object> requestData, String signkey) {
        try {
            // 获取签名和时间戳
            String sign = (String) requestData.get("sign");
            Object timeObj = requestData.get("signtime");
            
            if (sign == null || timeObj == null) {
                log.error("缺少签名或时间戳字段: sign={}, time={}", sign, timeObj);
                return false;
            }
            
            // 转换时间戳
            long signTime;
            if (timeObj instanceof Long) {
                signTime = (Long) timeObj;
            } else if (timeObj instanceof String) {
                signTime = Long.parseLong((String) timeObj);
            } else {
                log.error("时间戳格式错误: {}", timeObj);
                return false;
            }
            
            // 验证时间戳（5分钟有效期）
            if (!TrialSignDecryptUtil.isSignTimeValid(signTime, 5 * 60 * 1000)) {
                log.error("时间戳已过期: {}", signTime);
                return false;
            }
            
            // 提取原始数据（不包含签名和时间戳）
            Map<String, Object> originalData = new HashMap<>(requestData);
            originalData.remove("sign");
            originalData.remove("signtime");
            
            // 验证签名
            boolean isValid = TrialSignDecryptUtil.verifySign(originalData, sign, signkey, signTime);
            
            log.info("验签结果: {}, 签名: {}, 时间戳: {}", isValid, sign, signTime);
            
            return isValid;
            
        } catch (Exception e) {
            log.error("验签过程中发生异常", e);
            return false;
        }
    }
    
    /**
     * 模拟自定义字段名AOP验签过程
     */
    private static boolean simulateCustomFieldSignVerify(Map<String, Object> requestData, String signkey) {
        try {
            // 获取签名和时间戳（使用自定义字段名）
            String sign = (String) requestData.get("customSign");
            Object timeObj = requestData.get("customTime");
            
            if (sign == null || timeObj == null) {
                log.error("缺少自定义签名或时间戳字段: customSign={}, customTime={}", sign, timeObj);
                return false;
            }
            
            // 转换时间戳
            long signTime;
            if (timeObj instanceof Long) {
                signTime = (Long) timeObj;
            } else if (timeObj instanceof String) {
                signTime = Long.parseLong((String) timeObj);
            } else {
                log.error("时间戳格式错误: {}", timeObj);
                return false;
            }
            
            // 验证时间戳（5分钟有效期）
            if (!TrialSignDecryptUtil.isSignTimeValid(signTime, 5 * 60 * 1000)) {
                log.error("时间戳已过期: {}", signTime);
                return false;
            }
            
            // 提取原始数据（不包含自定义签名和时间戳字段）
            Map<String, Object> originalData = new HashMap<>(requestData);
            originalData.remove("customSign");
            originalData.remove("customTime");
            
            // 验证签名
            boolean isValid = TrialSignDecryptUtil.verifySign(originalData, sign, signkey, signTime);
            
            log.info("自定义字段名验签结果: {}, 签名: {}, 时间戳: {}", isValid, sign, signTime);
            
            return isValid;
            
        } catch (Exception e) {
            log.error("自定义字段名验签过程中发生异常", e);
            return false;
        }
    }
} 