package com.boot.server.pay;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.pay.AbstractIJPay;
import com.boot.server.common.pay.domain.dto.PayDto;
import com.boot.server.common.pay.domain.dto.QueryPayResultRequest;
import com.boot.server.common.pay.domain.dto.WxPayDto;
import com.boot.server.common.pay.domain.vo.QueryPayResultResponse;
import com.boot.server.common.pay.domain.vo.WxPushOrderResponse;
import com.boot.server.common.util.DateUtil;
import com.boot.server.common.util.PriceUtil;
import com.boot.server.common.util.ServletUtils;
import com.boot.server.config.AppConfig;
import com.boot.server.config.pay.WxPayBean;
import com.boot.server.entity.AppOrdersEntity;
import com.boot.server.entity.AppUserEntity;
import com.boot.server.enums.PayStatusEnum;
import com.boot.server.repository.*;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.enums.TradeType;
import com.ijpay.core.kit.IpKit;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApi;
import com.ijpay.wxpay.WxPayApiConfig;
import com.ijpay.wxpay.WxPayApiConfigKit;
import com.ijpay.wxpay.model.OrderQueryModel;
import com.ijpay.wxpay.model.UnifiedOrderModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * WxPayService
 *
 * <AUTHOR> 2025/5/5 23:32
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxPayService extends AbstractIJPay<WxPushOrderResponse, WxPayDto> {
    private final WxPayBean wxPayBean;
    private final AppOrdersRepository appOrdersRepository;
    private final AppOrderDetailsRepository appOrderDetailsRepository;
    private final AppUserRepository appUserRepository;
    private final AppPaymentRepository appPaymentRepository;
    private final ExpressHelper expressHelper;
    private final AppUserIntegralRecordRepository appUserIntegralRecordRepository;
    private final AppConfig appConfig;

    @Override
    protected void prePay(WxPayDto wxPayDto) {
        String orderNo = wxPayDto.getOrderNo();
        AppOrdersEntity appOrders = appOrdersRepository.getByOrderNo(orderNo);
        if (Objects.equals(appOrders.getPayStatus(), PayStatusEnum.CANCEL.getCode())) {
            log.info("订单号: {} 已经支付成功", orderNo);
            throw new BusinessException("订单已取消，无法支付");
        }
        appOrders.setPayTime(LocalDateTime.now());
        appOrdersRepository.updateById(appOrders);
    }

    @Override
    protected WxPushOrderResponse toPay(WxPayDto wxPayDto) {

        String ipAddr = IpKit.getRealIp(ServletUtils.getRequest());
        log.info("发起支付: {} IP {}", JSON.toJSONString(wxPayDto), ipAddr);
        // 设置发起支付参数
        WxPayApiConfig wxPayApiConfig = wxPayBean.getWxPayApiConfig();
        WxPayApiConfigKit.setThreadLocalWxPayApiConfig(wxPayApiConfig);
        // 可以开启支付
        String orderNo = wxPayDto.getOrderNo();
        AppOrdersEntity appOrders = appOrdersRepository.getByOrderNo(orderNo);
        PayDto payDto = new PayDto();
        payDto.setOrderNo(orderNo);
        // 单位分
        payDto.setAmount(appOrders.getPayAmount().multiply(new BigDecimal("100")).setScale(0, RoundingMode.HALF_UP).toString());
        payDto.setBody(appOrderDetailsRepository.getGoodsName(orderNo));
        payDto.setAttach(orderNo);
        payDto.setPayAmount(appOrders.getPayAmount());
        Map<String, String> params = toParams(ipAddr, payDto);
        log.info("微信预下单请求参数: {}", JSON.toJSONString(params));
        String xmlResult = WxPayApi.pushOrder(false, params);
        log.info("微信预下单接口: {}", xmlResult);
        Map<String, String> result = WxPayKit.xmlToMap(xmlResult);
        String returnCode = result.get("return_code");
        String returnMsg = result.get("return_msg");
        String errCodeDes = result.get("err_code_des");
        if (!WxPayKit.codeIsOk(returnCode)) {
            log.error("预下单错误 returnCode 错误: {} {}", returnMsg, errCodeDes);
            throw new BusinessException(returnMsg);
        }
        String resultCode = result.get("result_code");
        if (!WxPayKit.codeIsOk(resultCode)) {
            log.error("预下单错误 resultCode 错误: {}", returnMsg);
            throw new BusinessException(returnMsg);
        }
        // 以下字段在 return_code 和 result_code 都为 SUCCESS 的时候有返回
        String prepayId = result.get("prepay_id");
        Map<String, String> packageParams = WxPayKit.prepayIdCreateSign(
                prepayId,
                wxPayApiConfig.getAppId(),
                wxPayApiConfig.getPartnerKey(),
                SignType.HMACSHA256
        );
        WxPushOrderResponse wxPushOrderResponse = WxPushOrderResponse.mapToWxPushOrderResponse(packageParams);
        wxPushOrderResponse.setPrepayId(prepayId);
        wxPushOrderResponse.setSignType(SignType.HMACSHA256.getType());
        log.info("wxPushOrderResponse: {}", JSON.toJSONString(wxPushOrderResponse));
        appPaymentRepository.createWxPayment(wxPushOrderResponse, payDto);
        return wxPushOrderResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String notifyPay(String params) {
        log.info("支付回调: {}", params);
        // 设置发起支付参数
        WxPayApiConfig wxPayApiConfig = wxPayBean.getWxPayApiConfig();
        WxPayApiConfigKit.setThreadLocalWxPayApiConfig(wxPayApiConfig);
        Map<String, String> map = WxPayKit.xmlToMap(params);
        String returnCode = map.get("return_code");
        // 注意重复通知的情况，同一订单号可能收到多次通知，请注意一定先判断订单状态
        // 注意此处签名方式需与统一下单的签名类型一致
        if (WxPayKit.verifyNotify(map, WxPayApiConfigKit.getWxPayApiConfig().getPartnerKey(), SignType.HMACSHA256)) {
            if (WxPayKit.codeIsOk(returnCode)) {
                // 回调处理成功后返回
                String transactionId = map.get("transaction_id");
                String orderNo = map.get("out_trade_no");
                SpringUtil.getBean(WxPayService.class).success(orderNo, transactionId);
                Map<String, String> xml = new HashMap<String, String>(2);
                xml.put("return_code", "SUCCESS");
                xml.put("return_msg", "OK");
                return WxPayKit.toXml(xml);
            }
        }
        return null;
    }

    @Override
    protected QueryPayResultResponse queryPayResult(QueryPayResultRequest request) {
        // 从数据库查询支付订单状态
        Boolean paidResult = appPaymentRepository.payResult(request);
        if (paidResult) {
            log.info("payment 中返回结果 支付成功");
            return QueryPayResultResponse.success();
        }
        // 设置发起支付参数
        WxPayApiConfig wxPayApiConfig = wxPayBean.getWxPayApiConfig();
        WxPayApiConfigKit.setThreadLocalWxPayApiConfig(wxPayApiConfig);
        Map<String, String> params = OrderQueryModel.builder()
                .appid(wxPayApiConfig.getAppId())
                .mch_id(wxPayApiConfig.getMchId())
                .transaction_id(request.getTransactionId())
                .out_trade_no(request.getOrderNo())
                .nonce_str(WxPayKit.generateStr())
                .build()
                .createSign(wxPayApiConfig.getPartnerKey(), SignType.MD5);
        log.info("支付结果查询请求参数：{}", WxPayKit.toXml(params));
        String query = WxPayApi.orderQuery(params);
        log.info("支付结果查询结果: {}", query);
        Map<String, String> payResult = WxPayKit.xmlToMap(query);
        String returnCode = payResult.get("return_code");
        String resultCode = payResult.get("result_code");
        String returnMsg = payResult.get("return_msg");
        if (!WxPayKit.codeIsOk(returnCode)) {
            log.error("支付结果查询失败 returnCode 错误: {} {}", returnCode, returnMsg);
            return QueryPayResultResponse.failure();
        }
        if (!WxPayKit.codeIsOk(resultCode)) {
            log.error("支付结果查询失败 resultCode 错误: {} {}", resultCode, returnMsg);
            return QueryPayResultResponse.failure();
        }
        // 支付结果
        String tradeState = payResult.get("trade_state");
        if (!WxPayKit.codeIsOk(tradeState)) {
            log.error("支付结果查询失败 tradeState 非 SUCCESS: {} {}", tradeState, returnMsg);
            return QueryPayResultResponse.failure();
        }
        String transactionId = payResult.get("transaction_id");
        // 支付结果查询成功 更新 order/payment 订单状态
        SpringUtil.getBean(WxPayService.class).success(request.getOrderNo(), transactionId);
        // 更新支付状态
        return QueryPayResultResponse.success();
    }

    /**
     * 构建支付参数
     *
     * @param ipAddr 客户端IP地址
     * @param payDto 支付参数
     * @return 请求微信参数
     */
    private Map<String, String> toParams(String ipAddr, PayDto payDto) {
        WxPayApiConfig wxPayApiConfig = WxPayApiConfigKit.getWxPayApiConfig();
        return UnifiedOrderModel
                .builder()
                .appid(wxPayApiConfig.getAppId())
                .mch_id(wxPayApiConfig.getMchId())
                .nonce_str(WxPayKit.generateStr())
                // 商品描述交易字段格式根据不同的应用场景按照以下格式：
                //APP——需传入应用市场上的APP名字-实际商品名称，天天爱消除-游戏充值。
                .body(payDto.getBody())
                // 附加数据，在查询API和支付通知中原样返回，该字段主要用于商户携带订单的自定义数据
                .attach(payDto.getOrderNo())
                .out_trade_no(payDto.getOrderNo())
                // 订单总金额，单位为分，详见支付金额
                .total_fee(payDto.getAmount())
                .spbill_create_ip(ipAddr)
                // 支付回调地址
                .notify_url(wxPayBean.getPayNotify())
                .trade_type(TradeType.JSAPI.getTradeType())
                // 小程序支付设置用户 openId
                .openid(appUserRepository.getCurrentUserOpenId())
                .build()
                .createSign(wxPayApiConfig.getPartnerKey(), SignType.HMACSHA256);
    }

    @Transactional(rollbackFor = Exception.class)
    public void success(String orderNo, String transactionId) {
        // 物流下单
        AppOrdersEntity appOrders = appOrdersRepository.getByOrderNo(orderNo);
        if (Objects.equals(appOrders.getPayStatus(), PayStatusEnum.SUCCESS.getCode())) {
            log.info("订单号: {} 已经支付成功", orderNo);
            return;
        }
        // 支付结果查询成功 更新 order/payment 订单状态
        appOrdersRepository.wxPaySuccess(orderNo);
        appPaymentRepository.wxPaySuccess(orderNo, transactionId);
        try {
            String logistics = appOrders.getLogistics();
            log.info("支付订单号: {} 物流单号: {}", orderNo, logistics);
            if (StrUtil.isBlank(logistics)) {
                // 顺丰单号
                String expressOrderNo = expressHelper.createExpressOrder(appOrders);
                // 更新物流单号
                appOrders.setLogistics(expressOrderNo);
                appOrdersRepository.updateById(appOrders);
            }
            boolean firstShop = appUserRepository.isFirstShop(appOrders.getCreateUser());
            // 还没有首次消费标记
            if (!firstShop) {
                appUserRepository.updateFirstShop(appOrders.getCreateUser());
                AppUserEntity appUser = appUserRepository.getById(appOrders.getCreateUser());
                String inviteShareCode = appUser.getInviteShareCode();
                // 30天前
                LocalDateTime plusDays = LocalDateTime.now().plusDays(-30);
                if (StrUtil.isNotBlank(inviteShareCode) && DateUtil.lt(plusDays, appUser.getCreateTime())) {
                    log.info("有邀请人 邀请人分享码: {}", inviteShareCode);
                    AppUserEntity inviteUser = appUserRepository.getByShareCode(inviteShareCode);
                    // 邀请者赠送积分
                    appUserIntegralRecordRepository.addIntegralRecord(
                            inviteUser.getId(),
                            appConfig.getThirtyDaysShareIntegral(),
                            "邀请的用户在30天内消费赠送积分"
                    );
                }
            }
            // 更新用户积分
            appUserIntegralRecordRepository.addIntegralRecord(
                    appOrders.getCreateUser(),
                    PriceUtil.amountToIntegral(appOrders.getPayAmount()),
                    "支付订单号: " + orderNo + " 获取积分"
            );
        } catch (Exception e) {
            log.error("支付回调失败 {}", e.getMessage(), e);
        }
    }
}
