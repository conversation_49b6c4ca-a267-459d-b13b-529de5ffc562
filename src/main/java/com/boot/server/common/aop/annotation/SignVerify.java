package com.boot.server.common.aop.annotation;

import java.lang.annotation.*;

/**
 * 验签注解
 * 用于标记需要进行签名验证的方法
 * 
 * <AUTHOR>
 * @date 2023-12-23
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SignVerify {
    
    /**
     * 签名密钥的配置键名
     * 如果不指定，将使用默认的签名密钥
     * 
     * @return 签名密钥配置键名
     */
    String signKey() default "";
    
    /**
     * 签名时间有效期（毫秒）
     * 默认5分钟
     * 
     * @return 有效期毫秒数
     */
    long validPeriod() default 5 * 60 * 1000;
    
    /**
     * 是否启用时间戳验证
     * 默认启用
     * 
     * @return 是否启用时间戳验证
     */
    boolean enableTimeVerify() default true;
    
    /**
     * 请求参数中签名字段名
     * 默认"sign"
     * 
     * @return 签名字段名
     */
    String signField() default "sign";
    
    /**
     * 请求参数中时间戳字段名
     * 默认"signtime"
     * 
     * @return 时间戳字段名
     */
    String timeField() default "signtime";
    
    /**
     * 验签失败时的错误消息
     * 
     * @return 错误消息
     */
    String errorMessage() default "签名验证失败";
    
    /**
     * 是否记录验签日志
     * 默认启用
     * 
     * @return 是否记录日志
     */
    boolean enableLog() default true;
} 