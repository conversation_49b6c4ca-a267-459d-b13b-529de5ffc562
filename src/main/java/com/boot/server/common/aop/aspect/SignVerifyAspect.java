package com.boot.server.common.aop.aspect;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.boot.server.common.aop.annotation.SignVerify;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.exception.RException;
import com.boot.server.common.result.R;
import com.boot.server.common.result.ResultCode;
import com.boot.server.common.util.ResponseUtil;
import com.boot.server.common.util.TrialSignDecryptUtil;
import com.boot.server.config.AppConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * 验签AOP切面
 * 处理带有@SignVerify注解的方法
 *
 * <AUTHOR>
 * @date 2023-12-23
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
public class SignVerifyAspect {

    private final AppConfig appConfig;

    /**
     * 定义切点：所有带有@SignVerify注解的方法
     */
    @Pointcut("@annotation(com.boot.server.common.aop.annotation.SignVerify)")
    public void signVerifyPointcut() {
    }

    /**
     * 环绕通知：在方法执行前后进行验签
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 异常
     */
    @Around("signVerifyPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取方法签名
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        // 获取注解
        SignVerify signVerify = method.getAnnotation(SignVerify.class);
        if (signVerify == null) {
            return joinPoint.proceed();
        }

        // 记录开始日志
        if (signVerify.enableLog()) {
            log.info("开始验签处理，方法: {}.{}", method.getDeclaringClass().getSimpleName(), method.getName());
        }

        try {
            // 获取请求参数
            Map<String, Object> requestData = extractRequestData(joinPoint, signVerify);

            // 获取签名密钥
            String signKey = getSignKey(signVerify);

            // 执行验签
            boolean verifyResult = verifySignature(requestData, signKey, signVerify);

            if (!verifyResult) {
                String errorMsg = signVerify.errorMessage();
                log.error("验签失败: {}", errorMsg);
                return R.failed(errorMsg);
            }

            // 记录成功日志
            if (signVerify.enableLog()) {
                log.info("验签成功，继续执行方法: {}.{}", method.getDeclaringClass().getSimpleName(), method.getName());
            }

            // 执行原方法
            return joinPoint.proceed();

        } catch (BusinessException e) {
            // 业务异常直接抛出
            return R.failed(e.getMessage());
        } catch (Exception e) {
            // 其他异常包装为业务异常
            log.error("验签过程中发生异常", e);
            return R.failed(e.getMessage());
        }
    }

    /**
     * 提取请求数据
     *
     * @param joinPoint  连接点
     * @param signVerify 验签注解
     * @return 请求数据Map
     */
    private Map<String, Object> extractRequestData(ProceedingJoinPoint joinPoint, SignVerify signVerify) {
        Map<String, Object> requestData = new HashMap<>();
        // 获取方法参数
        Object[] args = joinPoint.getArgs();
        // 将参数转换为Map
        for (int i = 0; i < args.length; i++) {
            if (args[i] instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> paramMap = (Map<String, Object>) args[i];
                requestData.putAll(paramMap);
            } else if (args[i] != null) {
                // 如果是单个对象，尝试转换为Map
                Map<String, Object> objectMap = JSON.parseObject(JSON.toJSONString(args[i]), new TypeReference<Map<String, Object>>() {
                });
                requestData.putAll(objectMap);
            }
        }
        return requestData;
    }

    /**
     * 获取签名密钥
     *
     * @param signVerify 验签注解
     * @return 签名密钥
     */
    private String getSignKey(SignVerify signVerify) {
        String signKey = signVerify.signKey();
        if (signKey == null || signKey.trim().isEmpty()) {
            return appConfig.getSignKey();
        }
        return signKey;
    }

    /**
     * 验证签名
     *
     * @param requestData 请求数据
     * @param signKey     签名密钥
     * @param signVerify  验签注解
     * @return 验证结果
     */
    private boolean verifySignature(Map<String, Object> requestData, String signKey, SignVerify signVerify) {
        try {
            // 获取签名和时间戳
            String sign = (String) requestData.get(signVerify.signField());
            Object timeObj = requestData.get(signVerify.timeField());

            if (sign == null || timeObj == null) {
                log.error("缺少签名或时间戳字段: sign={}, time={}", sign, timeObj);
                return false;
            }

            // 转换时间戳
            long signTime;
            if (timeObj instanceof Long) {
                signTime = (Long) timeObj;
            } else if (timeObj instanceof String) {
                signTime = Long.parseLong((String) timeObj);
            } else {
                log.error("时间戳格式错误: {}", timeObj);
                return false;
            }

            // 验证时间戳
            if (signVerify.enableTimeVerify()) {
                if (!TrialSignDecryptUtil.isSignTimeValid(signTime, signVerify.validPeriod())) {
                    log.error("时间戳已过期: {}", signTime);
                    return false;
                }
            }

            // 提取原始数据（不包含签名和时间戳）
            Map<String, Object> originalData = new HashMap<>(requestData);
            originalData.remove(signVerify.signField());
            originalData.remove(signVerify.timeField());

            // 验证签名
            boolean isValid = TrialSignDecryptUtil.verifySign(originalData, sign, signKey, signTime);

            if (signVerify.enableLog()) {
                log.info("验签结果: {}, 签名: {}, 时间戳: {}", isValid, sign, signTime);
            }

            return isValid;

        } catch (Exception e) {
            log.error("验签过程中发生异常", e);
            return false;
        }
    }
} 