package com.boot.server.common.exception;

import com.boot.server.common.result.ResultCode;

/**
 * 权限不足异常
 */
public class ForbiddenException extends BaseException {

    private static final long serialVersionUID = 1L;

    public ForbiddenException() {
        super(ResultCode.FORBIDDEN);
    }

    public ForbiddenException(String message) {
        super(ResultCode.FORBIDDEN.getCode(), message);
    }

    public ForbiddenException(Throwable cause) {
        super(ResultCode.FORBIDDEN, cause);
    }

    public ForbiddenException(String message, Throwable cause) {
        super(ResultCode.FORBIDDEN.getCode(), message, cause);
    }
}
