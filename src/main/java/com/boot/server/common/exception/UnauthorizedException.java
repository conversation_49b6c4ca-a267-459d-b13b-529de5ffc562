package com.boot.server.common.exception;

import com.boot.server.common.result.ResultCode;

/**
 * 未登录异常
 */
public class UnauthorizedException extends BaseException {

    private static final long serialVersionUID = 1L;

    public UnauthorizedException() {
        super(ResultCode.UNAUTHORIZED);
    }

    public UnauthorizedException(String message) {
        super(ResultCode.UNAUTHORIZED.getCode(), message);
    }

    public UnauthorizedException(Throwable cause) {
        super(ResultCode.UNAUTHORIZED, cause);
    }

    public UnauthorizedException(String message, Throwable cause) {
        super(ResultCode.UNAUTHORIZED.getCode(), message, cause);
    }
}
