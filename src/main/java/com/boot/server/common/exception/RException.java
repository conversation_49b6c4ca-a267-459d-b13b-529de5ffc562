package com.boot.server.common.exception;

import com.boot.server.common.result.IResultCode;

/**
 * 业务异常
 */
public class RException extends BaseException {

    private static final long serialVersionUID = 1L;

    public RException(String message) {
        super(message);
    }

    public RException(Integer code, String message) {
        super(code, message);
    }

    public RException(IResultCode resultCode) {
        super(resultCode);
    }

    public RException(IResultCode resultCode, String message) {
        super(resultCode, message);
    }

    public RException(IResultCode resultCode, Throwable cause) {
        super(resultCode, cause);
    }

    public RException(Integer code, String message, Throwable cause) {
        super(code, message, cause);
    }

    /**
     * 根据条件抛出异常
     *
     * @param condition 条件
     * @param message   消息
     */
    public static void throwIf(boolean condition, String message) {
        if (condition) {
            throw new RException(message);
        }
    }

    /**
     * 根据条件抛出异常
     *
     * @param condition  条件
     * @param resultCode 响应码
     */
    public static void throwIf(boolean condition, IResultCode resultCode) {
        if (condition) {
            throw new RException(resultCode);
        }
    }

    /**
     * 根据条件抛出异常
     *
     * @param condition 条件
     * @param code      状态码
     * @param message   消息
     */
    public static void throwIf(boolean condition, Integer code, String message) {
        if (condition) {
            throw new RException(code, message);
        }
    }

    /**
     * 当对象为空时抛出异常
     *
     * @param object  对象
     * @param message 消息
     */
    public static void throwIfNull(Object object, String message) {
        if (object == null) {
            throw new RException(message);
        }
    }

    /**
     * 当对象为空时抛出异常
     *
     * @param object     对象
     * @param resultCode 响应码
     */
    public static void throwIfNull(Object object, IResultCode resultCode) {
        if (object == null) {
            throw new RException(resultCode);
        }
    }
}
