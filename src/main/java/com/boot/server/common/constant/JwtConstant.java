package com.boot.server.common.constant;

/**
 * JWT u5e38u91cfu7c7b
 */
public class JwtConstant {

    /**
     * header 前缀
     */
    public static final String TOKEN_HEADER = "Authorization";

    /**
     * Token 前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";

    public static final String USER_ID_KEY = "userId";

    public static final String USERNAME_KEY = "username";

    public static final String ROLES_KEY = "roles";


    /**
     * 用户登录令牌缓存前缀
     */
    public static final String USER_TOKEN_PREFIX = "user:token:";


    /**
     * 令牌有效期（毫秒）
     */
    public static final long TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60 * 60;
}
