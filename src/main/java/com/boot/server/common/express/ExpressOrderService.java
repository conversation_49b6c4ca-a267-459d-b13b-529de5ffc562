package com.boot.server.common.express;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.boot.server.common.express.aop.ExpressLog;
import com.boot.server.common.express.dto.CreateOrderRequest;
import com.boot.server.common.express.dto.PreOrderRequest;
import com.boot.server.common.express.dto.RoutesRequest;
import com.boot.server.common.express.enums.ApiEnum;
import com.boot.server.common.express.enums.ServiceEnum;
import com.boot.server.common.express.utils.HttpExpressUtil;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * OrderService
 *
 * <AUTHOR> 2025/5/11 15:38
 */
@Service
@RequiredArgsConstructor
public class ExpressOrderService {
    private final HttpExpressUtil httpExpressUtil;


    @ExpressLog(serviceCode = "EXP_RECE_CREATE_ORDER")
    public JSONObject createOrder(CreateOrderRequest createOrderRequest) {
        HttpRequest httpRequest = httpExpressUtil.post(ApiEnum.COMMON, ServiceEnum.EXP_RECE_CREATE_ORDER, JSON.toJSONString(createOrderRequest));
        @Cleanup HttpResponse httpResponse = httpRequest.execute();
        return JSON.parseObject(httpResponse.body());
    }

    @ExpressLog(serviceCode = "EXP_RECE_SEARCH_ROUTES")
    public JSONObject queryRoutes(RoutesRequest routesRequest) {
        HttpRequest httpRequest = httpExpressUtil.post(ApiEnum.COMMON, ServiceEnum.EXP_RECE_SEARCH_ROUTES, JSON.toJSONString(routesRequest));
        @Cleanup HttpResponse httpResponse = httpRequest.execute();
        return JSON.parseObject(httpResponse.body());
    }

    @ExpressLog(serviceCode = "EXP_RECE_PRE_ORDER")
    public JSONObject preOrder(PreOrderRequest preOrderRequest) {
        HttpRequest httpRequest = httpExpressUtil.post(ApiEnum.COMMON, ServiceEnum.EXP_RECE_PRE_ORDER, JSON.toJSONString(preOrderRequest));
        @Cleanup HttpResponse httpResponse = httpRequest.execute();
        return JSON.parseObject(httpResponse.body());
    }
}
