package com.boot.server.common.express.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * RouterResponse
 *
 * <AUTHOR> 2025/5/11 16:58
 */
@Data
public class RouterResponse {
    /**
     * 运单号｜订单号
     */
    private String mailNo;
    // 路由节点发生的时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date acceptTime;
    // 	路由节点具体描述
    private String remark;
    // 路由节点发生的地点
    private String acceptAddress;
    // 路由节点操作码
    private String opCode;
}
