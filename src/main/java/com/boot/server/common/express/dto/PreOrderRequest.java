package com.boot.server.common.express.dto;

import lombok.Data;

import java.util.List;

/**
 * PreOrderRequest
 * 预下单
 *
 * <AUTHOR> 2025/5/13 09:49
 */
@Data
public class PreOrderRequest {
    // 订单号
    private String orderId;
    // 发货地址收货地址
    private List<ExpAddress> contactInfoList;
    // 月结卡号
    private String monthlyCard;
    // 快件产品类别
    // https://open.sf-express.com/developSupport/734349?activeIndex=324604
    private Integer expressTypeId = 1;
}
