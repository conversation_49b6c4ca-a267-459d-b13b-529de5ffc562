package com.boot.server.common.express.dto;

import com.boot.server.common.express.constant.ExpressConstant;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CreateOrderRequest
 * https://qiao.sf-express.com/Api/ApiDetails?apiServiceCode=EXP_RECE_CREATE_ORDER&category=1&apiClassify=1&interName=%E4%B8%8B%E8%AE%A2%E5%8D%95%E6%8E%A5%E5%8F%A3-EXP_RECE_CREATE_ORDER
 *
 * <AUTHOR> 2025/5/11 15:57
 */
@Data
public class CreateOrderRequest {
    private String language = ExpressConstant.CN;
    // 客户订单号，重复使用订单号时返回第一次下单成功时的运单信息
    private String orderId;
    // 托寄物信息
    private List<Map<String, Object>> cargoDetails;
    // 收寄双方信息
    private List<ExpAddress> contactInfoList;
    // 顺丰月结卡号
    private String monthlyCard;
    // 快件产品类别
    // https://open.sf-express.com/developSupport/734349?activeIndex=324604
    private Integer expressTypeId = 1;
    // 是否通过手持终端 通知顺丰收派 员上门收件，支持以下值： 1：要求 0：不要求
    private Integer isDocall = 1;
    // 付款方式，支持以下值： 1:寄方付 2:收方付 3:第三方付 默认 1
    private Integer payMethod = 1;
    // 要求上门取件开始时间
    private String sendStartTm;

    public String getSendStartTm() {
        LocalDateTime now = LocalDateTime.now();
        int hour = now.getHour();
        // 判断当前时间是否大于晚上8点
        if (hour >= 20) {
            // 如果是，则设置为明天的8点
            now = now.plusDays(1).withHour(8).withMinute(0).withSecond(0).withNano(0);
        } else {
            // 如果不是，则判断当前时间是否小于等于8点,如果小于等于8点则设置为今天的8点
            if (hour < 8) {
                // 否则设置为今天的9点
                now = now.withHour(8).withMinute(0).withSecond(0).withNano(0);
            }
        }
        return now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 固定托寄物信息
     */
    public static List<Map<String, Object>> getCargoDetails() {
        List<Map<String, Object>> cargoDetails = new ArrayList<>();
        Map<String, Object> cargoDetail = new HashMap<>();
        cargoDetail.put("count", 1);
        cargoDetail.put("unit", "个");
        cargoDetail.put("weight", 0.2);
        cargoDetail.put("amount", 100);
        cargoDetail.put("currency", "CNY");
        cargoDetail.put("name", "玻璃管");
        cargoDetail.put("sourceArea", "CHN");
        cargoDetails.add(cargoDetail);
        return cargoDetails;
    }
}
