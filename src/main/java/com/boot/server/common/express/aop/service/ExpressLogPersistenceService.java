package com.boot.server.common.express.aop.service;

import com.alibaba.fastjson2.JSONObject;
import com.boot.server.common.express.aop.ExpressLog;
import com.boot.server.common.express.enums.ServiceEnum;
import com.boot.server.entity.AppExpressLogEntity;
import com.boot.server.repository.AppExpressLogRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * ExpressLogPersistenceService - 独立事务保存日志
 *
 * <AUTHOR> 2025/5/12 21:56
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExpressLogPersistenceService {
    private final AppExpressLogRepository appExpressLogRepository;

    /**
     * 在独立事务中保存日志
     *
     * @param joinPoint  切点
     * @param expressLog 日志注解配置
     * @param proceed    返回值
     * @param exception  异常信息
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void saveLog(ProceedingJoinPoint joinPoint, ExpressLog expressLog, Object proceed, Throwable exception) {
        try {
            AppExpressLogEntity appExpressLogEntity = new AppExpressLogEntity();
            appExpressLogEntity.setRequestBody(getParam(joinPoint));
            appExpressLogEntity.setServiceCode(expressLog.serviceCode());
            appExpressLogEntity.setServiceName(ServiceEnum.getName(expressLog.serviceCode()));

            // 记录返回结果或异常信息
            if (exception != null) {
                appExpressLogEntity.setResponseBody("Exception: " + exception.getMessage());
            } else {
                appExpressLogEntity.setResponseBody(JSONObject.toJSONString(proceed));
            }

            appExpressLogRepository.save(appExpressLogEntity);
        } catch (Exception e) {
            log.error("保存日志失败", e);
        }
    }

    /**
     * 获取方法参数
     *
     * @param proceedingJoinPoint 切点
     * @return 参数
     */
    private String getParam(ProceedingJoinPoint proceedingJoinPoint) {
        Map<String, Object> map = new HashMap<String, Object>();
        Object[] values = proceedingJoinPoint.getArgs();
        String[] names = ((CodeSignature) proceedingJoinPoint.getSignature()).getParameterNames();
        for (int i = 0; i < names.length; i++) {
            map.put(names[i], values[i]);
        }
        return JSONObject.toJSONString(map);
    }
}
