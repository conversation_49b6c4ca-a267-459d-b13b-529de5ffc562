package com.boot.server.common.express.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * ServiceEnum
 *
 * <AUTHOR> 2025/5/11 15:34
 */
@Getter
@RequiredArgsConstructor
public enum ServiceEnum {
    // https://qiao.sf-express.com/Api/ApiDetails?apiServiceCode=EXP_RECE_CREATE_ORDER&category=1&apiClassify=1&interName=%E4%B8%8B%E8%AE%A2%E5%8D%95%E6%8E%A5%E5%8F%A3-EXP_RECE_CREATE_ORDER
    EXP_RECE_CREATE_ORDER("EXP_RECE_CREATE_ORDER", "下订单接口"),
    EXP_RECE_SEARCH_ROUTES("EXP_RECE_SEARCH_ROUTES", "路由查询接口"),
    EXP_RECE_PRE_ORDER("EXP_RECE_PRE_ORDER", "预下单接口"),
    ;
    private final String serviceCode;
    private final String name;

    public static String getName(String s) {
        ServiceEnum[] services = ServiceEnum.values();
        for (ServiceEnum serviceEnum : services) {
            if (serviceEnum.getServiceCode().equals(s)) {
                return serviceEnum.getName();
            }
        }
        return null;
    }
}
