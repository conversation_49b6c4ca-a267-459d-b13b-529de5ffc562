package com.boot.server.common.express.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.boot.server.common.express.OAuth2Service;
import com.boot.server.common.express.enums.ApiEnum;
import com.boot.server.common.express.enums.ServiceEnum;
import com.boot.server.config.ExpressConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * HttpExpressUtil
 *
 * <AUTHOR> 2025/5/11 15:19
 */
@Component
@RequiredArgsConstructor
public class HttpExpressUtil {
    private final ExpressConfig expressConfig;
    private final OAuth2Service auth2Service;


    public HttpRequest post(ApiEnum apiEnum, ServiceEnum serviceEnum, String msgData) {
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("partnerID", expressConfig.getAppId());
        hashMap.put("secret", expressConfig.getAppSecret());
        hashMap.put("requestID", IdWorker.get32UUID());
        hashMap.put("serviceCode", serviceEnum.getServiceCode());
        hashMap.put("timestamp", System.currentTimeMillis());
        hashMap.put("accessToken", auth2Service.getAccessToken());
        hashMap.put("msgData", msgData);
        return HttpUtil
                .createPost(expressConfig.getBase() + apiEnum.getUrl())
                .header("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
                .form(hashMap);
    }

}
