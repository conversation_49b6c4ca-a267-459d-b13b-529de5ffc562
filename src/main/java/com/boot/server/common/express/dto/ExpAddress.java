package com.boot.server.common.express.dto;

import lombok.Data;

/**
 * ExpAddress
 *
 * <AUTHOR> 2025/5/11 16:01
 */
@Data
public class ExpAddress {
    // 详细地址
    private String address;
    // 联系人
    private String contact;
    // 地址类型： 1，寄件方信息 2，到件方信息
    private Integer contactType;
    // 国家或地区代码 例如：内地件CN 香港852
    private String country = "CN";
    // 邮编，跨境件必填（中国内地， 港澳台互寄除外）
    private String postCode;
    private String tel;

    // 预下单必填
    private String province;
    private String city;
}
