package com.boot.server.common.express.utils;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.express.dto.RouterResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * ExpressUtil
 *
 * <AUTHOR> 2025/5/11 16:40
 */
@Slf4j
public class ExpressUtil {
    /**
     * 根据下单结果获取运单号
     *
     * @param jsonObject 下单结果
     * @return waybillNo 运单号
     */
    public static String getWaybillNo(JSONObject jsonObject) {
        JSONObject apiResultData = getApiResultData(jsonObject);
        JSONArray waybillNoInfoList = apiResultData.getJSONObject("msgData").getJSONArray("waybillNoInfoList");
        return waybillNoInfoList.getJSONObject(0).getString("waybillNo");
    }

    /**
     * 根据下单结果获取运单号
     *
     * @param jsonObject 下单结果
     * @return waybillNo 运单号
     */
    public static List<RouterResponse> getWaybillRouters(JSONObject jsonObject) {
        JSONObject apiResultData = getApiResultData(jsonObject);
        JSONArray routeResps = apiResultData.getJSONObject("msgData").getJSONArray("routeResps");
        List<RouterResponse> routers = new ArrayList<>();
        for (int i = 0; i < routeResps.size(); i++) {
            JSONObject routeResp = routeResps.getJSONObject(i);
            JSONArray routeRespJSONArray = routeResp.getJSONArray("routes");
            for (int j = 0; j < routeRespJSONArray.size(); j++) {
                RouterResponse response = routeRespJSONArray.getObject(j, RouterResponse.class);
                response.setMailNo(response.getMailNo());
                routers.add(response);
            }
        }
        if (CollectionUtils.isNotEmpty(routers)) {
            routers.sort(Comparator.comparing(RouterResponse::getAcceptTime).reversed());
            return routers;
        }
        return Collections.emptyList();
    }

    public static JSONObject getApiResultData(JSONObject jsonObject) {
        String apiResultCode = jsonObject.getString("apiResultCode");
        String apiErrorMsg = jsonObject.getString("apiErrorMsg");
        // 统一接入平台校验成功，调用后端服务成功 注意：不代表后端业务处理成功，实际业务处理结果， 需要查看响应属性apiResultData中的详细结果
        if (!StrUtil.equalsIgnoreCase(apiResultCode, "A1000")) {
            throw new BusinessException(apiErrorMsg);
        }
        String apiResultDataValue = jsonObject.getString("apiResultData");
        JSONObject object = JSONObject.parseObject(apiResultDataValue);
        String errorCode = object.getString("errorCode");
        String errorMsg = object.getString("errorMsg");
        if (!StrUtil.equalsIgnoreCase(errorCode, "S0000")) {
            log.error("code: {} message: {}", errorCode, errorMsg);
            throw new BusinessException(errorMsg);
        }
        return object;
    }
}
