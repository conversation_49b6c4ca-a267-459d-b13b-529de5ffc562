package com.boot.server.common.express;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.express.enums.ApiEnum;
import com.boot.server.common.util.AppCacheUtil;
import com.boot.server.config.ExpressConfig;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;

/**
 * OAuth2Service
 *
 * <AUTHOR> 2025/5/11 15:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OAuth2Service {
    private final ExpressConfig expressConfig;
    private final Duration timeout = Duration.ofSeconds(7000);


    /**
     * 获取顺丰ACCESS_TOKEN
     *
     * @return ACCESS_TOKEN
     */
    public String getAccessToken() {
        HttpRequest request = HttpUtil
                .createPost(expressConfig.getBase() + ApiEnum.ACCESS_TOKEN.getUrl())
                .header("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("partnerID", expressConfig.getAppId());
        hashMap.put("secret", expressConfig.getAppSecret());
        hashMap.put("grantType", "password");
        request.form(hashMap);
        @Cleanup HttpResponse httpResponse = request.execute();
        String body = httpResponse.body();
        JSONObject jsonObject = JSON.parseObject(body);
        String accessTokenValue = jsonObject.getString("accessToken");
        if (StrUtil.isBlank(accessTokenValue)) {
            throw new BusinessException("顺丰系统异常");
        }
        return accessTokenValue;
    }
}
