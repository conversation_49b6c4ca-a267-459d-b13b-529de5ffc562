package com.boot.server.common.express.dto;

import com.boot.server.common.express.constant.TrackingType;
import lombok.Data;

import java.util.List;

/**
 * RoutesRequest
 *
 * <AUTHOR> 2025/5/11 16:52
 */
@Data
public class RoutesRequest {
    // 1:根据顺丰运单号查询,trackingNumber将被当作顺丰运单号处理
    // 2:根据客户订单号查询,trackingNumber将被当作客户订单号处理
    private Integer trackingType = TrackingType.SF;
    // trackingType=1,则此值为顺丰运单号
    // 如果trackingType=2,则此值为客户订单号
    private List<String> trackingNumber;
    // 手机号后四位
    private String checkPhoneNo;
}
