package com.boot.server.common.express.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * ApiEnum
 *
 * <AUTHOR> 2025/5/11 15:17
 */
@Getter
@RequiredArgsConstructor
public enum ApiEnum {
    ACCESS_TOKEN("/oauth2/accessToken", "OAuth2认证", "express::accessToken::"),
    COMMON("/std/service", "OAuth2认证", null),
    ;
    private final String url;
    private final String name;
    private final String cacheKey;

    public String buildCacheKey(final String... params) {
        return this.getCacheKey() + String.join("::", params);
    }
}
