package com.boot.server.common.express.aop.service;

import com.boot.server.common.express.aop.ExpressLog;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * ExpressLogService
 *
 * <AUTHOR> 2025/5/11 15:47
 */


@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ExpressLogService {
    private final ExpressLogPersistenceService expressLogPersistenceService;

    @Around(value = "@annotation(expressLog)")
    public Object around(ProceedingJoinPoint joinPoint, ExpressLog expressLog) throws Throwable {

        String name = joinPoint.getSignature().getName();
        Object proceed = null;
        Throwable exception = null;
        
        try {
            // @Before
            log.info("【环绕前置通知】【{}方法开始】", name);
            // 执行
            proceed = joinPoint.proceed();
            // @AfterReturning
            log.info("【环绕返回通知】【{}方法返回，返回值: {}】", name, proceed);
        } catch (Throwable e) {
            exception = e;
            throw e;
        } finally {
            // @After
            log.info("【环绕后置通知】【{}方法结束】", name);
            // 使用独立服务保存日志，确保即使主事务回滚也能保存日志
            expressLogPersistenceService.saveLog(joinPoint, expressLog, proceed, exception);
        }

        return proceed;
    }


}
