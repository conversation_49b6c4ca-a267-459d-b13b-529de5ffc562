package com.boot.server.common.util;

import cn.hutool.extra.spring.SpringUtil;
import com.boot.server.config.AppConfig;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * PriceUtil
 *
 * <AUTHOR> 2025/6/5 18:29
 */
public class PriceUtil {
    /**
     * 将金额转换为积分
     * 1元等于 1 积分
     *
     * @param amount 金额
     * @return 积分值
     */
    public static Long amountToIntegral(BigDecimal amount) {
        if (amount == null) {
            return 0L;
        }
        // 保留两位小数，向下取整
        AppConfig bean = SpringUtil.getBean(AppConfig.class);
        BigDecimal integral = amount.multiply(new BigDecimal(bean.getBuyIntegralRate())).setScale(2, RoundingMode.DOWN);
        return integral.longValue();
    }

    /**
     * 将积分转换为金额 10:1
     *
     * @param integral 积分值
     * @return 金额
     */
    public static BigDecimal integralToAmount(Long integral) {
        if (integral == null || integral < 0) {
            return BigDecimal.ZERO;
        }
        // 积分转换为金额，1积分=0.1元
        AppConfig bean = SpringUtil.getBean(AppConfig.class);
        return BigDecimal.valueOf(integral).divide(new BigDecimal(bean.getConsumptionIntegralRate()), 2, RoundingMode.DOWN);
    }


    /**
     * 获取金额对应可扣除的积分
     *
     * @param amount 金额
     * @return 可扣除的积分 最小保留 0.1
     */
    public static Long getDeductionIntegral(BigDecimal amount) {
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            return 0L;
        }
        // 金额转换为积分，1元=10积分 1积分=0.1元
        AppConfig bean = SpringUtil.getBean(AppConfig.class);
        BigDecimal integral = amount
                .multiply(new BigDecimal(bean.getConsumptionIntegralRate()))
                .setScale(0, RoundingMode.DOWN);
        if (integral.compareTo(BigDecimal.ZERO) <= 0) {
            return 0L;
        }
        return integral.longValue();
    }
}
