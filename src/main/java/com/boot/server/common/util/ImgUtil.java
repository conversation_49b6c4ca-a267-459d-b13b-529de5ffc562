package com.boot.server.common.util;

import com.boot.server.config.FileUploadConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * ImgUtil
 *
 * <AUTHOR> 2025/7/18 15:41
 */
@Component
@RequiredArgsConstructor
public class ImgUtil {

    private final FileUploadConfig fileUploadConfig;

    /**
     * 获取OSS报告图片的完整路径
     *
     * @param fileName 文件名，不包含后缀
     * @return 完整的OSS报告图片路径
     */
    public String getOssReportImage(String fileName) {
        return fileUploadConfig.getOssGetPath() + fileUploadConfig.getEnv() + fileName + ".png";
    }

}
