package com.boot.server.common.util;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;

import java.time.Duration;

/**
 * TimedCache
 *
 * <AUTHOR> 2025/5/13 12:11
 */
public class AppCacheUtil {

    public final static TimedCache<String, String> CACHE = CacheUtil.newTimedCache(0);


    public static String get(String key) {
        return CACHE.get(key);
    }

    public static void set(String key, String value) {
        CACHE.put(key, value);
    }

    public static void set(String key, String value, Duration duration) {
        CACHE.put(key, value, duration.toMillis());
    }

}
