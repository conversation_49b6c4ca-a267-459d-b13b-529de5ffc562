package com.boot.server.common.util;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * PageUtils
 *
 * <AUTHOR> 2025/5/4 18:42
 */
public class PageUtil {

    /**
     * 构建分页参数
     *
     * @param pageNum  当前页
     * @param pageSize 每页多少条
     * @param <T>      泛型
     * @return 分页参数
     */
    public static <T> Page<T> getPage(Integer pageNum, Integer pageSize) {
        return new Page<>(pageNum, pageSize);
    }
}
