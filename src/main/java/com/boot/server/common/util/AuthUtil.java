package com.boot.server.common.util;

import com.boot.server.common.exception.UnauthorizedException;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * u8ba4u8bc1u5de5u5177u7c7b
 * u7528u4e8eu83b7u53d6u5f53u524du767bu5f55u7528u6237u7684u4fe1u606f
 */
public class AuthUtil {
    public static Long getCurrentUserId() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            throw new UnauthorizedException("认证失败");
        }
        HttpServletRequest request = attributes.getRequest();
        Object userId = request.getAttribute("userId");
        if (userId == null) {
            throw new UnauthorizedException("认证失败");
        }
        return (Long) userId;
    }

    public static boolean isAuthenticated() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                return false;
            }
            HttpServletRequest request = attributes.getRequest();
            return request.getAttribute("userId") != null;
        } catch (Exception e) {
            return false;
        }
    }
}
