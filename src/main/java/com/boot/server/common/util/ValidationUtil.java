package com.boot.server.common.util;

import com.boot.server.common.exception.BusinessException;
import org.springframework.util.CollectionUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 参数校验工具类
 */
public class ValidationUtil {

    private static final Validator validator;

    static {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    /**
     * 校验对象，如果校验不通过则抛出业务异常
     *
     * @param obj 待校验对象
     * @param <T> 对象类型
     */
    public static <T> void validate(T obj) {
        validate(obj, null);
    }

    /**
     * 校验对象，如果校验不通过则抛出业务异常
     *
     * @param obj     待校验对象
     * @param message 自定义错误消息
     * @param <T>     对象类型
     */
    public static <T> void validate(T obj, String message) {
        if (obj == null) {
            throw new BusinessException("校验对象不能为空");
        }
        Set<ConstraintViolation<T>> violations = validator.validate(obj);
        if (!CollectionUtils.isEmpty(violations)) {
            String errorMsg = message;
            if (errorMsg == null || errorMsg.isEmpty()) {
                errorMsg = violations.stream()
                        .map(ConstraintViolation::getMessage)
                        .collect(Collectors.joining(", "));
            }
            throw new BusinessException(errorMsg);
        }
    }

    /**
     * 校验对象的指定属性，如果校验不通过则抛出业务异常
     *
     * @param obj      待校验对象
     * @param property 属性名
     * @param <T>      对象类型
     */
    public static <T> void validateProperty(T obj, String property) {
        validateProperty(obj, property, null);
    }

    /**
     * 校验对象的指定属性，如果校验不通过则抛出业务异常
     *
     * @param obj      待校验对象
     * @param property 属性名
     * @param message  自定义错误消息
     * @param <T>      对象类型
     */
    public static <T> void validateProperty(T obj, String property, String message) {
        if (obj == null) {
            throw new BusinessException("校验对象不能为空");
        }
        if (property == null || property.isEmpty()) {
            throw new BusinessException("属性名不能为空");
        }
        Set<ConstraintViolation<T>> violations = validator.validateProperty(obj, property);
        if (!CollectionUtils.isEmpty(violations)) {
            String errorMsg = message;
            if (errorMsg == null || errorMsg.isEmpty()) {
                errorMsg = violations.stream()
                        .map(ConstraintViolation::getMessage)
                        .collect(Collectors.joining(", "));
            }
            throw new BusinessException(errorMsg);
        }
    }

    /**
     * 校验对象，返回校验结果
     *
     * @param obj 待校验对象
     * @param <T> 对象类型
     * @return 校验结果，如果没有错误则返回空字符串
     */
    public static <T> String validateWithResult(T obj) {
        if (obj == null) {
            return "校验对象不能为空";
        }
        Set<ConstraintViolation<T>> violations = validator.validate(obj);
        if (CollectionUtils.isEmpty(violations)) {
            return "";
        }
        return violations.stream()
                .map(violation -> violation.getPropertyPath() + ": " + violation.getMessage())
                .collect(Collectors.joining(", "));
    }
}
