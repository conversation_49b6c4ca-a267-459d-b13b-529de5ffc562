package com.boot.server.common.util;

import java.time.LocalDateTime;

/**
 * DateUtil
 *
 * <AUTHOR> 2025/7/23 12:56
 */
public class DateUtil {

    public static boolean lt(LocalDateTime dateTime1, LocalDateTime dateTime2) {
        if (dateTime1 == null || dateTime2 == null) {
            return false;
        }
        return dateTime1.isBefore(dateTime2);
    }

    public static void main(String[] args) {
        LocalDateTime dateTime1 = LocalDateTime.of(2025, 7, 23, 23, 0);
        LocalDateTime dateTime2 = LocalDateTime.of(2025, 7, 23, 13, 0);

        boolean result = lt(dateTime1, dateTime2);
        System.out.println("Is dateTime1 less than dateTime2? " + result); // Should print true
    }
}
