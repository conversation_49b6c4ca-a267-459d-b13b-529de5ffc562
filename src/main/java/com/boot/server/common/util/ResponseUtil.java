package com.boot.server.common.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 快速向 HttpServletResponse 写出 JSON
 */
public final class ResponseUtil {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    private ResponseUtil() {
    }

    /**
     * 直接写出 JSON，状态码 200
     */
    public static void writeJson(Object body) throws IOException {
        writeJson(HttpServletResponse.SC_OK, body);
    }

    /**
     * 指定状态码写出 JSON
     */
    public static void writeJson(int status, Object body) throws IOException {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getResponse();
        if (response == null) {
            throw new IllegalStateException("No HttpServletResponse available");
        }
        response.setStatus(status);
        response.setContentType("application/json;charset=UTF-8");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        MAPPER.writeValue(response.getWriter(), body);
    }
}