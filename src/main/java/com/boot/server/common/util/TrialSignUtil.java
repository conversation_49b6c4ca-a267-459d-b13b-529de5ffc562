package com.boot.server.common.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 宠知因接口签名算法
 *
 * <AUTHOR>
 * @date 2023-12-23
 **/
@Slf4j
public class TrialSignUtil {

    public static String objFormat(Object data) {
        if (data == null) {
            return null;
        }
        Object newData = data;
        if (data instanceof Date || (data instanceof String && ((String) data).contains("/Date("))) {
            newData = formatTime(data, "yyyy-MM-dd HH:mm:ss");
        } else if (data instanceof List<?>) {
            newData = ((List<?>) data).stream().map(TrialSignUtil::objFormat).collect(Collectors.toList());
        } else if (data instanceof Map<?, ?>) {
            newData = ((Map<?, ?>) data).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> objFormat(entry.getValue())));
        }
        return newData.toString();
    }

    public static Map<String, Object> objSortAndFormat(Map<String, Object> data) {

        // 将Map的entry集合转换为List
        List<Map.Entry<String, Object>> entryList = new ArrayList<>(data.entrySet());

        // 使用自定义的Comparator进行排序（忽略大小写）
        entryList.sort(Map.Entry.comparingByKey());

        // 创建一个新的有序Map，用于存储排序后的键值对
        Map<String, Object> sortedMap = new LinkedHashMap<>();
        for (Map.Entry<String, Object> entry : entryList) {
            sortedMap.put(entry.getKey(), entry.getValue());
        }

        // 输出排序后的Map
        log.info("排序后的Map: " + sortedMap);
        return sortedMap;
    }

    public static String signData(Map<String, Object> data, String signkey, long signTime) throws UnsupportedEncodingException {
        if (data == null) {
            data = new HashMap<>(8);
        }

        // 设置签名时间
        data.put("signtime", signTime);
        // 参数排序
        Map<String, Object> newData = objSortAndFormat(data);
        // 设置签名
        String staticParameter = JSON.toJSONString(newData);
        String signOld = staticParameter.replaceAll(" ", "").replaceAll("\\\"", "\"");

        // 手动处理一下URLEncoder没有编码的字符
        String[] ignoreStr = {"-", "_", "\\.", "!", "~", "\\*", "'", "\\(", "\\)"};
        for (String str : ignoreStr) {
            signOld = signOld.replaceAll(str, "a");
        }

        // URL编码
        signOld = URLEncoder.encode(signOld, "utf-8").toLowerCase();
        // Base64编码
        signOld = Base64.encode(signOld.getBytes(StandardCharsets.UTF_8));
        // MD5
        String sign = SecureUtil.md5(signOld + signkey);
        newData.put("sign", sign);
        return sign;
    }

    private static String formatTime(Object data, String pattern) {
        // 实现日期格式化逻辑  /Date(1704266892607)/
        String substring = data.toString().substring(6, 19);
        long time = Long.parseLong(substring);
        // 你需要根据实际情况选择合适的日期格式化方式
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(time);
    }
} 