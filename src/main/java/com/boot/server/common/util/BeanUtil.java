package com.boot.server.common.util;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.boot.server.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * BeanUtil
 *
 * <AUTHOR> 2025/5/2 10:21
 */
@Slf4j
public class BeanUtil extends BeanUtils {

    public static <T> T copyToBean(Object source, Class<T> clazz) {
        try {
            T newInstance = clazz.newInstance();
            BeanUtils.copyProperties(source, newInstance);
            return newInstance;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("网络错误，请稍后重试");
        }
    }

    public static <T, U> List<T> copyToList(Collection<U> source, Class<T> clazz) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }
        try {
            return source.stream()
                    .map(item -> (T) copyToBean(item, clazz))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("网络错误，请稍后重试");
        }
    }

}
