package com.boot.server.common.result;

import lombok.Data;

/**
 * R
 *
 * <AUTHOR> 2025/7/18 17:10
 */
@Data
public class R<T> {
    private Integer retCode;
    private T retData;
    private String retMsg;
    private String backRetDate;
    private String otherRetData;


    public static <T> R<T> success(T data) {
        R<T> r = new R<>();
        r.setRetCode(0);
        r.setRetData(data);
        r.setRetMsg("success");
        return r;
    }


    public static <T> R<T> failed(String message) {
        R<T> r = new R<>();
        r.setRetCode(500);
        r.setRetMsg(message);
        return r;
    }
}
