package com.boot.server.common.pay.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * PayDto
 * 支付所需的必要参数
 *
 * <AUTHOR> 2025/5/3 23:02
 */
@Data
public class PayDto {
    // 支付展示的商品名
    private String body;
    // 支付传给渠道的附加参数
    private String attach;
    // 支付金额(单位分)
    private String amount;
    // 支付金额(单位元)
    private BigDecimal payAmount;
    // 订单号
    private String orderNo;
}
