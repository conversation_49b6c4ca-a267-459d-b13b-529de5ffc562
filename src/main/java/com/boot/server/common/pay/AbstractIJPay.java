package com.boot.server.common.pay;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.pay.domain.dto.QueryPayResultRequest;
import com.boot.server.common.pay.domain.vo.QueryPayResultResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * IPay
 *
 * <AUTHOR> 2025/5/3 22:11
 */
@Slf4j
public abstract class AbstractIJPay<R extends IPayResponse, P> {

    /**
     * 执行支付方法
     *
     * @param p 参数
     * @return 结果
     */
    public final R execute(P p) {
        this.prePay(p);
        return this.toPay(p);
    }

    /**
     * 付款前的操作
     *
     * @param p 参数
     */

    protected void prePay(P p) {
    }

    /**
     * 发起支付
     *
     * @param p 参数
     * @return 返回类型
     */
    protected abstract R toPay(P p);

    /**
     * 支付回调
     *
     * @param params 参数
     */
    public abstract String notifyPay(String params);


    /**
     * 支付结果查询入口
     *
     * @param request 查询参数
     * @return 查询结果
     */
    public final QueryPayResultResponse executeQueryPayResult(QueryPayResultRequest request) {
        log.info("支付结果查询: {}", JSON.toJSONString(request));
        if (request == null) {
            throw new BusinessException("支付结果查询参数不能为空");
        }
        if (StrUtil.isBlank(request.getTransactionId()) && StrUtil.isBlank(request.getOrderNo())) {
            throw new BusinessException("支付结果查询参数不能为空");
        }
        return this.queryPayResult(request);
    }

    /**
     * 支付结果查询
     *
     * @param request 请求参数
     * @return 支付结果
     */
    protected abstract QueryPayResultResponse queryPayResult(QueryPayResultRequest request);
}
