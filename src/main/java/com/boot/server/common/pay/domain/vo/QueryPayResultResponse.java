package com.boot.server.common.pay.domain.vo;

import lombok.Data;

/**
 * QueryPayResultResponse
 *
 * <AUTHOR> 2025/5/4 10:33
 */
@Data
public class QueryPayResultResponse {
    private Boolean payResult;

    public static QueryPayResultResponse success() {
        QueryPayResultResponse queryPayResultResponse = new QueryPayResultResponse();
        queryPayResultResponse.setPayResult(true);
        return queryPayResultResponse;
    }


    public static QueryPayResultResponse failure() {
        QueryPayResultResponse queryPayResultResponse = new QueryPayResultResponse();
        queryPayResultResponse.setPayResult(false);
        return queryPayResultResponse;
    }
}
