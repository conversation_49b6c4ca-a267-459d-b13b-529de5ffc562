package com.boot.server.common.pay.domain.vo;

import com.boot.server.common.pay.IPayResponse;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * WxPushOrderResponse
 * 查看 com.ijpay.core.kit.WxPayKit#appPrepayIdCreateSign
 *
 * <AUTHOR> 2025/5/3 23:42
 */
@Data
public class WxPushOrderResponse implements IPayResponse {
    private String appId;
    private String prepayId;
    @JsonProperty("package")
    private String packageName;
    private String nonceStr;
    private String timeStamp;
    private String signType;
    private String paySign;

    public static WxPushOrderResponse mapToWxPushOrderResponse(Map<String, String> map) {
        WxPushOrderResponse wxPushOrderResponse = new WxPushOrderResponse();
        wxPushOrderResponse.setAppId(map.get("appId"));
        wxPushOrderResponse.setPackageName(map.get("package"));
        wxPushOrderResponse.setNonceStr(map.get("nonceStr"));
        wxPushOrderResponse.setTimeStamp(map.get("timeStamp"));
        wxPushOrderResponse.setSignType(map.get("signType"));
        wxPushOrderResponse.setPaySign(map.get("paySign"));
        return wxPushOrderResponse;
    }
}
