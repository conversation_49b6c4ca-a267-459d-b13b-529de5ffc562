package com.boot.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Reportresponse
 *
 * <AUTHOR> 2025/5/16 14:24
 */
@Data
public class ReportResponse {
    // 繁育机构
    private String breederName;
    //  宠物名
    private String petName;
    // 宠物编号
    private String petNo;
    // 宠物乳名
    private String petRuName;
    // 品种
    private String breedName;
    // 检测项目
    private String testProject;
    // 检测编号
    private String kitsNo;
    // 性别
    private String genderName;
    // 检测结果
    private List<Item> items;


    @Data
    public static class Item {
        private Long id;
        private String group;
        // 项目名称
        private String projectName;
        // 点位
        private String locusName;
        // 结果
        private String result;
        // 排序
        private Integer sort;
        private String coat;
        // 解读结果
        private String resultInterpretation;
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime testDate;
        private String kitNo;

    }
}
