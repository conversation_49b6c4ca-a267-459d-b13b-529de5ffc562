package com.boot.server.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * QueryReportResponse
 *
 * <AUTHOR> 2025/7/18 17:25
 */
@Data
public class QueryReportResponse {
    @JSONField(name = "PageIndex")
    @JsonProperty("PageIndex")
    private Integer pageIndex = 1;
    @JSONField(name = "PageSize")
    @JsonProperty("PageSize")
    private Integer pageSize = 20;
    @JSONField(name = "RowCount")
    @JsonProperty("RowCount")
    private Integer rowCount = 1;
    @JSONField(name = "Data")
    @JsonProperty("Data")
    private List<ReportDataResponse> data;

    @Data
    public static class ReportDataResponse {
        @JSONField(name = "OrderId")
        @JsonProperty("OrderId")
        private String orderId;
        @JSONField(name = "OrderNo")
        @JsonProperty("OrderNo")
        private String orderNo;
        @JSONField(name = "PetNumber")
        @JsonProperty("PetNumber")
        private String petNumber;
        @JSONField(name = "PetName")
        @JsonProperty("PetName")
        private String petName;
        @JSONField(name = "OrderProductId")
        @JsonProperty("OrderProductId")
        private String orderProductId;
        @JSONField(name = "ProductId")
        @JsonProperty("ProductId")
        private String productId;
        @JSONField(name = "ProductName")
        @JsonProperty("ProductName")
        private String productName;
        @JSONField(name = "TestState")
        @JsonProperty("TestState")
        private Integer testState;
        @JSONField(name = "ReportNumber")
        @JsonProperty("ReportNumber")
        private String reportNumber;
        @JSONField(name = "SampleNumber")
        @JsonProperty("SampleNumber")
        private String sampleNumber;
        @JSONField(name = "SampleType")
        @JsonProperty("SampleType")
        private Integer sampleType;
        @JSONField(name = "TestEndTime")
        @JsonProperty("TestEndTime")
        private String testEndTime;
        @JSONField(name = "TestItemId")
        @JsonProperty("TestItemId")
        private String testItemId;
        @JSONField(name = "TestResult")
        @JsonProperty("TestResult")
        private String testResult;
        @JSONField(name = "TestResultList")
        @JsonProperty("TestResultList")
        private List<TestResultList> testResultList;
        @JSONField(name = "TestResultCN")
        @JsonProperty("TestResultCN")
        private String testResultCN;
        @JSONField(name = "ReportImgCn")
        @JsonProperty("ReportImgCn")
        private String reportImgCn;
        @JSONField(name = "ReportImgCnList")
        @JsonProperty("ReportImgCnList")
        private List<String> reportImgCnList;
        @JSONField(name = "IsPackage")
        @JsonProperty("IsPackage")
        private String isPackage;
        @JSONField(name = "DeviceId")
        @JsonProperty("DeviceId")
        private String deviceId;
    }

    @Data
    public static class TestResultList {
        private String projectAffiliation;
        private String testingItem;
        private String locusName;
        private String mutationStatus;
        private String modeOfInheritance;
        // 报告图片地址
        private String reportUrl;
    }
}
