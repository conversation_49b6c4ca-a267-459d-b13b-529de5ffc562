package com.boot.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * OrderDetailResponse
 *
 * <AUTHOR> 2025/5/11 21:29
 */
@Data
public class OrderDetailResponse {
    // 商品明细ID
    private Long id;
    // 商品标题
    private String title;
    // 商品图片
    private String image;
    // 商品价格
    private BigDecimal amount;
    // 商品ID
    private Long goodsId;
    // 剩余可用数量
    private Integer stock;
    // 订单号
    private String orderNo;
    // 购买日期
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;
}
