package com.boot.server.dto;

import com.boot.server.entity.AppAutoReportTaskEntity;
import lombok.Data;

/**
 * SampleResponse
 *
 * <AUTHOR> 2025/7/16 18:10
 */
@Data
public class SampleResponse {
    /**
     * 机构
     */
    private String organization;
    /**
     * 宠物编号
     */
    private String petNo;
    /**
     * 宠物性别
     */
    private String petSex;
    /**
     * 检测编号
     */
    private String deviceId;
    /**
     * 宠物品种
     */
    private String petBreed;


    public static SampleResponse toSampleResponse(AppAutoReportTaskEntity entity) {
        SampleResponse response = new SampleResponse();
        response.setOrganization(entity.getOrganization());
        response.setPetNo(entity.getPetNo());
        response.setPetSex(entity.getPetSex());
        response.setDeviceId(entity.getKitsNo());
        response.setPetBreed(entity.getPetBreed());
        return response;
    }
}
