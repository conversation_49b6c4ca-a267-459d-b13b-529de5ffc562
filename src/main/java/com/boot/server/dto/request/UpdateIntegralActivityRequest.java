package com.boot.server.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;

/**
 * 更新积分活动请求
 *
 * <AUTHOR> 2025/7/23 13:30
 */
@Data
public class UpdateIntegralActivityRequest {
    
    /**
     * 主键ID
     */
    @NotNull(message = "ID不能为空")
    private Long id;

    /**
     * 兑换码
     */
    @NotBlank(message = "兑换码不能为空")
    private String activityNo;
    
    /**
     * 奖励积分
     */
    @NotNull(message = "奖励积分不能为空")
    @Min(value = 1, message = "奖励积分必须大于0")
    private Integer integral;
    
    /**
     * 过期时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "过期时间不能为空")
    private LocalDateTime exprTime;
    
    /**
     * 限制人数0不限制
     */
    @Min(value = 0, message = "限制人数不能小于0")
    private Integer limitNum = 0;
    
    /**
     * 活动描述
     */
    private String remark;
}
