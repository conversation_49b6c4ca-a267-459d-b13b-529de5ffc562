package com.boot.server.dto.request;

import com.boot.server.common.dto.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * OrderListDto
 *
 * <AUTHOR> 2025/5/17 15:54
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class AdminOrderListRequest extends PageRequest {
    private String orderNo;
    // 收货人
    private String name;
    // 支付状态
    private Integer payStatus;
    // 订单状态
    private Integer orderStatus;
    // 物流试剂盒单号
    private String logistics;
    // 用户 ID
    private Long userId;
}
