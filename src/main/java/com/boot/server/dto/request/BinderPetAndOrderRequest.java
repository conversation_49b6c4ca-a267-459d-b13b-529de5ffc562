package com.boot.server.dto.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * BinderPetAndOrderRequest
 *
 * <AUTHOR> 2025/5/11 22:31
 */
@Data
public class BinderPetAndOrderRequest {
    // 订单明细ID
    private Long orderDetailId;
    // 宠物ID
    @NotNull(message = "请选择宠物")
    private Long petId;
    // 试剂盒编号(扫码获取)
    @NotNull(message = "请扫描试剂盒绑定")
    private String kitsNo;
}
