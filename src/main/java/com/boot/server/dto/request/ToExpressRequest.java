package com.boot.server.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * ToExpressResquest
 *
 * <AUTHOR> 2025/5/12 17:23
 */
@Data
public class ToExpressRequest {
    // 寄件人姓名
    @NotBlank(message = "寄件人姓名不能为空")
    private String name;
    // 寄件人电话
    @NotBlank(message = "寄件人电话不能为空")
    private String tel;
    // 寄件人详细地址
    @NotBlank(message = "寄件人详细地址不能为空")
    private String address;
    // 宠物ID
    @NotNull(message = "绑定数据不能为空")
    private Long petId;
}
