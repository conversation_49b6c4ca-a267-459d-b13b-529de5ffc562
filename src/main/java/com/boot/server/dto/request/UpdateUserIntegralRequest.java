package com.boot.server.dto.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * UpdateUserIntegralRequest
 *
 * <AUTHOR> 2025/6/6 10:14
 */
@Data
public class UpdateUserIntegralRequest {
    // 用户 ID
    @NotNull(message = "用户 ID 不能为空")
    private Long userId;
    // 变更值
    @NotNull(message = "变更值不能为空")
    private Long value;
    // 描述
    @NotNull(message = "备注不能为空")
    private String remark;
}
