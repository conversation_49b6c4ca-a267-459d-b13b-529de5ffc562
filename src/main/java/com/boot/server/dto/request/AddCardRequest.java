package com.boot.server.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * AddCardRequest
 *
 * <AUTHOR> 2025/5/5 11:01
 */
@Data
public class AddCardRequest {
    /**
     * 商品ID
     */
    @NotNull(message = "商品不能为空")
    private Long goodsId;
    /**
     * 规格
     */
    @NotBlank(message = "请选择商品规格")
    private String spec;
    /**
     * 数量
     */
    @NotNull(message = "请选择商品数量")
    @Positive(message = "商品数量错误")
    private Integer quantity;

}
