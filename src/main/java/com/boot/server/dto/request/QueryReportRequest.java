package com.boot.server.dto.request;

import com.alibaba.fastjson2.annotation.JSONField;
import com.boot.server.common.dto.BaseSign;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * QueryReportRequest
 *
 * <AUTHOR> 2025/7/18 16:40
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QueryReportRequest extends BaseSign {
    @JsonProperty("OrderNo")
    @JSONField(name = "OrderNo")
    private String orderNo;
}
