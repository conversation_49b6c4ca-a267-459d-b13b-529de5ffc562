package com.boot.server.dto.request;

import com.boot.server.common.dto.PageRequest;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 积分活动管理查询请求
 *
 * <AUTHOR> 2025/7/23 13:30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminIntegralActivityRequest extends PageRequest {

    /**
     * 活动名称（模糊查询）
     */
    private String remark;
    /**
     * 兑换码
     */
    private String activityNo;
    /**
     * 奖励积分
     */
    private Integer integral;

    /**
     * 限制人数
     */
    private Integer limitNum;

    /**
     * 过期时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exprTimeStart;

    /**
     * 过期时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exprTimeEnd;
}
