package com.boot.server.dto;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.boot.server.common.express.dto.RouterResponse;
import lombok.Data;

import java.util.List;

/**
 * OrderRouterResponse
 *
 * <AUTHOR> 2025/5/13 13:05
 */
@Data
public class OrderRouterResponse {
    // 顺丰单号
    private String logistics;
    // 物流信息
    private List<RouterResponse> routers;
    // 是否存在物流信息
    public Boolean getHasRouters() {
        return CollectionUtils.isNotEmpty(routers);
    }
}
