package com.boot.server.dto;

import lombok.Data;

/**
 * AdminBinderListResponse
 *
 * <AUTHOR> 2025/5/18 17:23
 */
@Data
public class AdminBinderListResponse {
    private Long id;
    // 宠物名
    private String petName;
    // 宠物乳名
    private String petRuName;
    // 宠物状态
    private Integer detectStatus;
    // 宠物 ID
    private String petId;
    // 宠物类型
    private String varietiesName;
    // 套餐品种
    private String orderVarietiesName;
    // 宠物性别
    private String genderName;
    // 宠物头像
    private String avatar;
    // 试剂盒编号
    private String kitsNo;
    // 物流单号
    private String logistics;
    // 是否已有证书
    private Integer isCertificate;
    // 是否已有报告
    private Integer isReport;
    // 证书图片多张使用,分割
    private String certImages;
}
