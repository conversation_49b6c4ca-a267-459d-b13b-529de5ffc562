package com.boot.server.dto.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 导入结果响应
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
public class ImportResultResponse {
    
    /**
     * 成功导入数量
     */
    private Integer successCount = 0;
    
    /**
     * 失败数量
     */
    private Integer failCount = 0;
    
    /**
     * 错误信息列表
     */
    private List<String> errorMessages = new ArrayList<>();
    
    /**
     * 添加错误信息
     */
    public void addError(String error) {
        this.errorMessages.add(error);
        this.failCount++;
    }
    
    /**
     * 增加成功数量
     */
    public void addSuccess() {
        this.successCount++;
    }
    
    /**
     * 是否有错误
     */
    public boolean hasError() {
        return failCount > 0;
    }
}
