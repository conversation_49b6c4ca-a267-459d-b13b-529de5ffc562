package com.boot.server.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 积分活动管理响应
 *
 * <AUTHOR> 2025/7/23 13:30
 */
@Data
public class AdminIntegralActivityResponse {
    
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 兑换码
     */
    private String activityNo;
    
    /**
     * 奖励积分
     */
    private Integer integral;
    
    /**
     * 过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exprTime;
    
    /**
     * 限制人数0不限制
     */
    private Integer limitNum;
    
    /**
     * 已使用次数
     */
    private Integer usedCount;
    
    /**
     * 活动描述
     */
    private String remark;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 创建用户
     */
    private Long createUser;
    
    /**
     * 更新用户
     */
    private Long updateUser;
}
