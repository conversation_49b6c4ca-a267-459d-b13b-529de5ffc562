package com.boot.server.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Data;

/**
 * AutoReportDetailResponse
 *
 * <AUTHOR> 2025/7/19 22:58
 */
@Data
public class AutoReportDetailResponse {
    private String item;
    private String position;
    private String result;
    private Integer sort;
    // 宠物类别
    private String coat;
    // 解读结构
    @JSONField(name = "result_interpretation")
    private String resultInterpretation;
    @JSONField(name = "P_result")
    private String pResult;
    /**
     * 报告链接
     */
    private String reportUrl;
}
