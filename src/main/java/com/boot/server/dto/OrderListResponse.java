package com.boot.server.dto;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * OrderListResponse
 *
 * <AUTHOR> 2025/5/10 12:33
 */
@Data
public class OrderListResponse {
    // 订单ID
    private Long id;
    // 订单编号
    private String orderNo;
    // 支付状态
    private Integer payStatus;
    // 订单状态
    private Integer orderStatus;
    // 订单金额
    private BigDecimal orderAmount;
    // 订单原价
    private BigDecimal sourceAmount;
    // 支付金额
    private BigDecimal payAmount;
    // 订单明细
    private List<GoodsResponse> goodsList;
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;

    // 订单 title
    public String getTitle() {
        if (CollectionUtils.isNotEmpty(goodsList)) {
            String title = goodsList.get(0).getTitle();
            if (goodsList.size() > 1) {
                return title + "等";
            }
            return title;
        }
        return null;
    }
    // 订单图片
    public String getImage() {
        if (CollectionUtils.isNotEmpty(goodsList)) {
            return goodsList.get(0).getImage();
        }
        return null;
    }
}
