package com.boot.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * CardListResponse
 *
 * <AUTHOR> 2025/5/5 11:10
 */
@Data
public class CartListResponse {
    // 购物车ID
    private String id;
    // 标题
    private String title;
    // 图片
    private String image;
    // 描述
    private String description;
    // 现价
    private BigDecimal amount;
    // 原价
    private BigDecimal sourceAmount;
    // 规格 多个规则使用,分割
    private String specsName;
    private String specs;
    // 商品状态
    private Integer status;
    // 加入购物车数量
    private Integer number;
    // 套餐类型
    private Integer goodsType;
    // 递增价格
    private BigDecimal incrPrice;
    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime createTime;
}
