package com.boot.server.dto.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 线下渠道订单Excel导入DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
public class OfflineOrderImportDto {
    
    /**
     * 试剂盒编号
     */
    @ExcelProperty(value = "试剂盒编号", index = 0)
    private String kitsNo;
    
    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 1)
    private String goodsName;
    
    /**
     * 商品规格名称
     */
    @ExcelProperty(value = "商品规格", index = 2)
    private String goodsSpecsName;
}
