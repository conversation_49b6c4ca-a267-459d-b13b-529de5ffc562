package com.boot.server.dto;

import lombok.Data;

/**
 * 用户参加积分活动响应
 *
 * <AUTHOR> 2025/7/23 14:30
 */
@Data
public class JoinIntegralActivityResponse {

    /**
     * 是否成功参加活动
     */
    private Boolean success;

    /**
     * 获得的积分
     */
    private Integer integral;

    /**
     * 活动描述
     */
    private String remark;

    /**
     * 消息提示
     */
    private String message;

    /**
     * 成功响应
     *
     * @param integral 获得的积分
     * @param remark   活动描述
     * @return 响应对象
     */
    public static JoinIntegralActivityResponse success(Integer integral, String remark) {
        JoinIntegralActivityResponse response = new JoinIntegralActivityResponse();
        response.setSuccess(true);
        response.setIntegral(integral);
        response.setRemark(remark);
        response.setMessage("恭喜您成功参加活动，获得 " + integral + " 积分！");
        return response;
    }
}
