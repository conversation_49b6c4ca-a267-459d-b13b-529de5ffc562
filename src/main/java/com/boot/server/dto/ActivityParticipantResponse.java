package com.boot.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 活动参加人员响应
 *
 * <AUTHOR> 2025/7/23 16:00
 */
@Data
public class ActivityParticipantResponse {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户头像
     */
    private String avatar;
    
    /**
     * 用户昵称
     */
    private String nickName;
    
    /**
     * 获得的积分
     */
    private Integer integral;
    
    /**
     * 参加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime joinTime;
}
