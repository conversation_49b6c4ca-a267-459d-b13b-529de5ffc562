package com.boot.server.config;

import com.boot.server.config.interceptor.AuthInterceptor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置类
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class WebMvcConfig implements WebMvcConfigurer {

    private final AuthInterceptor authInterceptor;
    private final FileUploadConfig fileUploadConfig;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册认证拦截器，拦截所有 /api/** 请求，但排除登录接口
        registry.addInterceptor(authInterceptor)
                .addPathPatterns("/api/**")
                .excludePathPatterns(
                        "/open/**",
                        "/api/open/**",
                        "/api/admin/login",
                        fileUploadConfig.getAccessUrlPrefix() + "/pdf/**"
                );
    }

    /**
     * 配置静态资源映射
     * 使前端可以通过域名+接口路径访问资源
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置文件访问路径映射
        registry.addResourceHandler(fileUploadConfig.getAccessUrlPrefix() + "/**")
                .addResourceLocations("file:" + fileUploadConfig.getBasePath() + "/");
        // 配置文件访问路径映射
        registry.addResourceHandler("/cert/**")
                .addResourceLocations("file:" + fileUploadConfig.getTemplate());
    }
}
