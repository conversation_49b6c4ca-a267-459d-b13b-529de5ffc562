package com.boot.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "file.upload")
public class FileUploadConfig {

    /**
     * 上传文件保存的基础路径
     */
    private String basePath;

    /**
     * 允许上传的文件类型，逗号分隔
     */
    private String allowTypes;

    /**
     * 最大文件大小，单位MB
     */
    private Integer maxSize;

    /**
     * 资源访问URL前缀
     */
    private String accessUrlPrefix;
    /**
     * 报告文件路径
     */
    private String reportPath;

    /**
     * 证书模板路径
     */
    private String template;

    /**
     * OSS 文件获取接口
     */
    private String ossGetPath;

    /**
     * OSS 环境目录
     */
    private String env;
}
