package com.boot.server.config.interceptor;

import com.boot.server.common.constant.JwtConstant;
import com.boot.server.common.exception.UnauthorizedException;
import com.boot.server.common.util.AppCacheUtil;
import com.boot.server.config.JwtConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 认证拦截器
 * 用于验证请求中的JWT令牌并提取用户信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthInterceptor implements HandlerInterceptor {

    private final JwtConfig jwtConfig;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        log.info("请求方式: {} 地址: {}", request.getMethod(), request.getRequestURI());
        // 获取请求头中的token
        String token = getTokenFromRequest(request);
        
        // 如果没有token，抛出未授权异常
        if (!StringUtils.hasText(token)) {
            throw new UnauthorizedException("未登录或登录已过期");
        }

        // 验证token有效性
        if (!jwtConfig.parseTokenAndCheckExpiration(token)) {
            throw new UnauthorizedException("无效的登录凭证");
        }
        
        try {
            // 从token中获取用户ID
            Long userId = getUserIdFromToken(token);
            
            // 将用户ID设置到请求属性中，方便后续使用
            request.setAttribute("userId", userId);
            return true;
        } catch (Exception e) {
            log.error("Token验证失败", e);
            throw new UnauthorizedException("登录验证失败");
        }
    }
    
    /**
     * 从请求头中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader(JwtConstant.TOKEN_HEADER);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(JwtConstant.TOKEN_PREFIX)) {
            return bearerToken.substring(JwtConstant.TOKEN_PREFIX.length());
        }
        return null;
    }
    
    /**
     * 从token中获取用户ID
     */
    private Long getUserIdFromToken(String token) {
        try {
            return jwtConfig.getUserIdFromToken(token);
        } catch (Exception e) {
            log.error("从token中获取用户ID失败", e);
            throw new UnauthorizedException("无效的登录凭证");
        }
    }
}
