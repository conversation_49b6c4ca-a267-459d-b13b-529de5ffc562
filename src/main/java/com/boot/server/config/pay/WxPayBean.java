package com.boot.server.config.pay;

import com.ijpay.wxpay.WxPayApiConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * <p>IJPay 让支付触手可及，封装了微信支付、支付宝支付、银联支付常用的支付方式以及各种常用的接口。</p>
 * <p>不依赖任何第三方 mvc 框架，仅仅作为工具使用简单快速完成支付模块的开发，可轻松嵌入到任何系统里。 </p>
 * <p>微信配置 Bean</p>
 *
 * <AUTHOR>
 */
@Data
@Slf4j
@Component
@ConfigurationProperties(prefix = "wxpay")
public class WxPayBean {
    /**
     * 应用编号
     */
    private String appId;
    /**
     * 商户号
     */
    private String mchId;
    /**
     * apiKey
     */
    private String partnerKey;
    /**
     * 证书路径
     */
    private String certPath;
    /**
     * 支付回调地址
     */
    private String payNotify;
    /**
     * 订单前缀
     */
    private String orderNoPrefix;

    @PostConstruct
    public void init() {
        log.info("微信配置初始化完成 支付回调地址: {}", payNotify);
    }

    /**
     * 构建微信支付配置
     *
     * @return 微信支付配置
     */
    public WxPayApiConfig getWxPayApiConfig() {
        return WxPayApiConfig.builder()
                .appId(this.getAppId())
                .mchId(this.getMchId())
                .partnerKey(this.getPartnerKey())
                .certPath(this.getCertPath())
                .build();
    }
}
