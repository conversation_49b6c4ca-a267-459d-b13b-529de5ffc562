package com.boot.server.config;

import cn.hutool.core.util.RandomUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.Map;

/**
 * JWT 配置类
 */
@Component
public class JwtConfig {

    @Value("${jwt.secret:petServerSecretKey}")
    private String secret;

    // 默认7天单位秒
    @Value("${jwt.expiration:604800}")
    private long expiration;

    /**
     * 生成 JWT token
     *
     * @param claims 自定义信息
     * @return JWT token
     */
    public String generateToken(Map<String, Object> claims) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes());
        return Jwts.builder()
                .setClaims(claims)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expiration * 1000))
                .signWith(key, SignatureAlgorithm.HS256)
                .compact();
    }

    /**
     * 解析 JWT token
     *
     * @param token JWT token
     * @return 自定义信息
     */
    public Claims parseToken(String token) {
        SecretKey key = Keys.hmacShaKeyFor(secret.getBytes());
        return Jwts.parserBuilder()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 验证 JWT token 是否有效
     *
     * @param token JWT token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            parseToken(token);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 从 JWT token 中获取用户 ID
     *
     * @param token JWT token
     * @return 用户 ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = parseToken(token);
        return claims.get("userId", Long.class);
    }

    /**
     * 解析 JWT token 并校验是否过期
     *
     * @param token JWT token
     * @return 如果 token 有效且未过期返回 Claims 对象，否则返回 null
     */
    public Boolean parseTokenAndCheckExpiration(String token) {
        try {
            Claims claims = parseToken(token);
            Date expiration = claims.getExpiration();
            if (expiration != null && expiration.before(new Date())) {
                return false; // token 已过期
            }
            return true;
        } catch (Exception e) {
            return false; // token 无效
        }
    }

    public static void main(String[] args) {
        System.out.println(RandomUtil.randomString(32));
    }
}
