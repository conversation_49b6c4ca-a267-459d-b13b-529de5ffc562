package com.boot.server.config;

import com.boot.server.common.constant.SysConstant;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

/**
 * 文件上传配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "app")
public class AppConfig {

    /**
     * 当前环境
     */
    private String env = SysConstant.DEV;
    /**
     * 应用环境
     */
    private String appEnv = SysConstant.APP_DEV;
    /**
     * 购买积分比例
     */
    private Long buyIntegralRate = 1L;
    /**
     * 消费积分比例
     */
    private Long consumptionIntegralRate = 10L;
    /**
     * 30天内购买分享人增积分
     */
    private Long thirtyDaysShareIntegral = 300L;
    /**
     * 首次购买金额
     */
    private BigDecimal firstBuyAmount = new BigDecimal("99");
    /**
     * 邀请注册邀请人增积分
     */
    private Long inviteIntegral = 200L;
    /**
     * 新用户注册赠送积分
     */
    private Long newUserIntegral = 1000L;

    /**
     * 邀请新用户奖励次数
     */
    private Integer inviteRewardCount = 10;

    /**
     * 签名密钥
     */
    private String signKey = "03Dlkl2yVfbqcF6O5mS88doxEmXWuIGR";

    /**
     * 默认宠物头像
     */
    private String defaultPetAvatar = "/api/file/system/default_cat_avatar.png";

    /**
     * 小程序海报分享页面
     */
    private String shareAppletPath = "pages/home/<USER>";

    /**
     * 小程序分享码文件存储路径
     */
    private String shareCodeFilePath;

    /**
     * 小程序分享码文件访问路径
     */
    private String shareCodeFileUrl = "/api/file/share-code";
}
