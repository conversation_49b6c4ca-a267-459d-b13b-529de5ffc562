package com.boot.server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 文件上传配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "express")
public class ExpressConfig {

    /**
     * 域名
     */
    private String base;
    /**
     * 顾客编码
     */
    private String appId;

    /**
     * 校验码
     */
    private String appSecret;

    /**
     * 月结卡号
     */
    private String monthlyCard;
}
