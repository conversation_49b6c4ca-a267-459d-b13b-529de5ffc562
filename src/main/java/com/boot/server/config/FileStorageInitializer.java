package com.boot.server.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;

/**
 * 文件存储初始化器
 * 确保文件上传目录存在
 */
@Component
public class FileStorageInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(FileStorageInitializer.class);

    @Autowired
    private FileUploadConfig fileUploadConfig;

    @Override
    public void run(String... args) {
        // 创建文件上传基础目录
        File uploadDir = new File(fileUploadConfig.getBasePath());
        if (!uploadDir.exists()) {
            if (uploadDir.mkdirs()) {
                logger.info("创建文件上传目录成功: {}", fileUploadConfig.getBasePath());
            } else {
                logger.error("创建文件上传目录失败: {}", fileUploadConfig.getBasePath());
            }
        } else {
            logger.info("文件上传目录已存在: {}", fileUploadConfig.getBasePath());
        }
    }
}
