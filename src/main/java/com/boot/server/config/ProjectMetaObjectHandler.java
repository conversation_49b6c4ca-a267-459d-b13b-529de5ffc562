package com.boot.server.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.boot.server.common.util.AuthUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * ProjectMetaObjectHandler
 *
 * <AUTHOR> 2025/5/3 16:11
 */
@Slf4j
@Component
public class ProjectMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        if (AuthUtil.isAuthenticated()) {
            Long currentUserId = AuthUtil.getCurrentUserId();
            this.strictInsertFill(metaObject, "createUser", Long.class, currentUserId);
            this.strictInsertFill(metaObject, "updateUser", Long.class, currentUserId);
        }
        this.strictInsertFill(metaObject, "createTime", LocalDateTime.class, LocalDateTime.now());
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (AuthUtil.isAuthenticated()) {
            Long currentUserId = AuthUtil.getCurrentUserId();
            this.strictInsertFill(metaObject, "updateUser", Long.class, currentUserId);
        }
        this.strictUpdateFill(metaObject, "updateTime", LocalDateTime.class, LocalDateTime.now());
    }
}
