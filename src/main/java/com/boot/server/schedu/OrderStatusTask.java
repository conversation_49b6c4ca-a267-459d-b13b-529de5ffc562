package com.boot.server.schedu;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boot.server.entity.AppOrdersEntity;
import com.boot.server.enums.PayStatusEnum;
import com.boot.server.repository.AppOrdersRepository;
import com.boot.server.service.AppOrdersService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OrderStatusTask
 *
 * <AUTHOR> 2025/6/5 18:18
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderStatusTask {
    private final AppOrdersRepository appOrdersRepository;
    private final AppOrdersService appOrdersService;

    /**
     * 定时任务取消订单 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void orderToCancel() {
        LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(30);
        List<AppOrdersEntity> list = appOrdersRepository.list(
                Wrappers.<AppOrdersEntity>lambdaQuery()
                        .eq(AppOrdersEntity::getPayStatus, PayStatusEnum.TO_PAY.getCode())
                        // 查询未支付订单且创建时间超过30分钟的订单
                        .le(AppOrdersEntity::getCreateTime, localDateTime)
        );
        if (CollectionUtils.isNotEmpty(list)) {
            log.info("定时任务取消订单，本次处理数量: {}", list.size());
            for (AppOrdersEntity appOrders : list) {
                Boolean cancel = appOrdersService.cancel(appOrders.getOrderNo(), true);
                if (!cancel) {
                    log.info("订单号: {} 取消失败", appOrders.getOrderNo());
                }
            }
        }
    }
}
