package com.boot.server.schedu;

import cn.hutool.core.thread.ThreadUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boot.server.common.constant.SysConstant;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.entity.AppAutoReportTaskEntity;
import com.boot.server.repository.AppAutoReportTaskRepository;
import com.boot.server.service.ReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * ReportTask
 *
 * <AUTHOR> 2025/5/16 11:06
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ReportTask {
    private final AppAutoReportTaskRepository appAutoReportTaskRepository;
    private final ReportService reportService;

    /**
     * 定时任务生成证书 每分钟
     */
    @Scheduled(fixedRate = 60000)
    public void handleReport() {
        log.info("开始执行自动化报告定时任务...");
        String date = LocalDateTime.now().plusMinutes(-20).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        // 先查询是否有超过 20分钟还没有执行完成的任务
        List<AppAutoReportTaskEntity> failureTasks = appAutoReportTaskRepository.list(
                Wrappers.<AppAutoReportTaskEntity>lambdaQuery()
                        .eq(AppAutoReportTaskEntity::getStatus, SysConstant.AUTO_REPORT_PROCESS)
                        .lt(AppAutoReportTaskEntity::getUpdateTime, date)
                        .orderByAsc(AppAutoReportTaskEntity::getId)
                        .last("limit 1")
        );
        if (CollectionUtils.isNotEmpty(failureTasks)) {
            log.warn("发现超过20分钟未完成的自动化报告任务，任务ID: {}", failureTasks.get(0).getId());
            // 将这些任务状态更新为处理失败
            for (AppAutoReportTaskEntity task : failureTasks) {
                appAutoReportTaskRepository.updateReportStatus(task.getId(), SysConstant.AUTO_REPORT_FAIL);
                log.info("任务ID: {} 状态更新为处理完成", task.getId());
            }
        }

        List<AppAutoReportTaskEntity> entityList = appAutoReportTaskRepository.list(
                Wrappers.<AppAutoReportTaskEntity>lambdaQuery()
                        .in(AppAutoReportTaskEntity::getStatus, 0, 3)
                        .orderByAsc(AppAutoReportTaskEntity::getId)
                        .last("limit 5")
        );
        if (CollectionUtils.isEmpty(entityList)) {
            log.info("没有待处理的自动化报告任务");
            return;
        }
        // 处理自动化报告任务
        log.info("处理自动化报告任务，数量: {}", entityList.size());
        final CountDownLatch cdl = new CountDownLatch(entityList.size());
        for (AppAutoReportTaskEntity entity : entityList) {
            ThreadUtil.execute(() -> processReportTask(entity, cdl));
        }
        try {
            cdl.await();
        } catch (InterruptedException e) {
            log.error("自动化报告任务处理被中断", e);
        }
    }

    private void processReportTask(AppAutoReportTaskEntity entity, CountDownLatch cdl) {
        try {
            // 更新任务状态为处理中
            appAutoReportTaskRepository.updateReportStatus(entity.getId(), SysConstant.AUTO_REPORT_PROCESS);
            reportService.handleOtherSystemReport(entity);
            // 更新任务状态为处理成功
            appAutoReportTaskRepository.updateReportStatus(entity.getId(), SysConstant.AUTO_REPORT_DONE);
        } catch (Exception e) {
            appAutoReportTaskRepository.updateReportStatus(entity.getId(), SysConstant.AUTO_REPORT_FAIL);
            log.error("处理自动化报告任务失败，任务ID: {}, 错误信息: {}", entity.getId(), e.getMessage(), e);
        } finally {
            cdl.countDown();
        }
    }

    public void retry(AppAutoReportTaskEntity entity) {
        try {
            reportService.handleOtherSystemReport(entity);
        } catch (Exception e) {
            log.error("处理自动化报告任务失败，任务ID: {}, 错误信息: {}", entity.getId(), e.getMessage(), e);
            throw new BusinessException("报告生成失败");
        }
    }

}
