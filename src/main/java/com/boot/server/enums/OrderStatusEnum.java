package com.boot.server.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * PayStatusEnum
 * 订单状态0 待支付 1已支付 2待收件 3部分使用 4已使用
 *
 * <AUTHOR> 2025/5/10 16:19
 */
@Getter
@RequiredArgsConstructor
public enum OrderStatusEnum {
    TO_PAY(0, "待支付"),
    SUCCESS(1, "已支付"),
    PART_USED(2, "部分使用"),
    USED(3, "已使用"),
    ;
    private final Integer code;
    private final String name;
}
