package com.boot.server.enums;

import cn.hutool.core.util.StrUtil;
import com.boot.server.dto.TextPosition;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * TitlePostion
 *
 * <AUTHOR> 2025/7/16 12:33
 */
@Getter
@RequiredArgsConstructor
public enum TitlePosition {
    // 繁育机构
    ORG(500, 432),
    // 检测编号
    REPORT_NO(1060, 432),
    // 宠物编号
    PET_NO(500, 495),
    // 宠物品种
    PET_CLASSIFY(1060, 495),
    // 性别
    SEX(500, 555),
    // 检测项目
    PROJECT(500, 615),
    ;
    private final Integer x;
    private final Integer y;

    public TextPosition buildPosition(String text) {
        return new TextPosition(StrUtil.blankToDefault(text,"无"), this.getX(), this.getY());
    }
}
