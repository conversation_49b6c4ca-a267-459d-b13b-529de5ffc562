package com.boot.server.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * DetectStatusEnum
 * 检测状态: 0待检测 1待收件 2检测中 3已检测 4已绑定
 *
 * <AUTHOR> 2025/5/11 21:22
 */
@Getter
@RequiredArgsConstructor
public enum DetectStatusEnum {
    NONE(0, "待检测"),
    HANDLE(1, "待收件"),
    PROCESS(2, "检测中"),
    DONE(3, "已检测"),
    BIND(4, "已绑定"),
    ;
    private final Integer code;
    private final String name;
}
