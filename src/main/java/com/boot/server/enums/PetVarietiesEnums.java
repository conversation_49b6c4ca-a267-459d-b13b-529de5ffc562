package com.boot.server.enums;

import com.boot.server.common.dto.ClassifyDto;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * PetVarietiesEnums
 *
 * <AUTHOR> 2025/5/9 15:06
 */
@Getter
@RequiredArgsConstructor
public enum PetVarietiesEnums {
    A(1, "英国短毛猫"),
    B(2, "美国短毛猫"),
    C(3, "苏格兰折耳猫"),
    D(4, "波斯猫"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        for (PetVarietiesEnums value : PetVarietiesEnums.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }
}
