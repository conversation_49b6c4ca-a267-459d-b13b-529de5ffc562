package com.boot.server.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.boot.server.dto.excel.OfflineOrderImportDto;
import com.boot.server.dto.response.ImportResultResponse;
import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppGoodsSpecsEntity;
import com.boot.server.entity.AppOfflineOrderEntity;
import com.boot.server.repository.AppGoodsRepository;
import com.boot.server.repository.AppGoodsSpecsRepository;
import com.boot.server.repository.AppOfflineOrderRepository;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 线下渠道订单Excel导入监听器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
public class OfflineOrderImportListener implements ReadListener<OfflineOrderImportDto> {

    private final AppOfflineOrderRepository appOfflineOrderRepository;
    private final AppGoodsRepository appGoodsRepository;
    private final AppGoodsSpecsRepository appGoodsSpecsRepository;
    private final ImportResultResponse importResult;

    /**
     * 批量处理的数据
     */
    private final List<AppOfflineOrderEntity> cachedDataList = new ArrayList<>();

    /**
     * 批量处理阈值
     */
    private static final int BATCH_COUNT = 100;

    public OfflineOrderImportListener(AppOfflineOrderRepository appOfflineOrderRepository,
                                       AppGoodsRepository appGoodsRepository,
                                       AppGoodsSpecsRepository appGoodsSpecsRepository,
                                       ImportResultResponse importResult) {
        this.appOfflineOrderRepository = appOfflineOrderRepository;
        this.appGoodsRepository = appGoodsRepository;
        this.appGoodsSpecsRepository = appGoodsSpecsRepository;
        this.importResult = importResult;
    }

    @Override
    public void invoke(OfflineOrderImportDto data, AnalysisContext context) {
        log.info("解析到一条数据：{}", data);

        // 获取当前行号
        int rowIndex = context.readRowHolder().getRowIndex() + 1;

        // 数据校验
        String validationError = validateData(data, rowIndex);
        if (StrUtil.isNotBlank(validationError)) {
            importResult.addError(validationError);
            return;
        }

        try {
            // 转换为实体对象
            AppOfflineOrderEntity entity = convertToEntity(data, rowIndex);
            if (entity != null) {
                cachedDataList.add(entity);
            }

            // 达到批量处理阈值，执行批量保存
            if (cachedDataList.size() >= BATCH_COUNT) {
                saveData();
            }
        } catch (Exception e) {
            log.error("处理第{}行数据时发生异常", rowIndex, e);
            importResult.addError(String.format("第%d行：%s", rowIndex, e.getMessage()));
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 保存剩余数据
        saveData();
        log.info("所有数据解析完成！");
    }

    /**
     * 数据校验
     */
    private String validateData(OfflineOrderImportDto data, int rowIndex) {
        if (StrUtil.isBlank(data.getKitsNo())) {
            return String.format("第%d行：试剂盒编号不能为空", rowIndex);
        }

        if (StrUtil.isBlank(data.getGoodsName())) {
            return String.format("第%d行：商品名称不能为空", rowIndex);
        }

        if (StrUtil.isBlank(data.getGoodsSpecsName())) {
            return String.format("第%d行：商品规格不能为空", rowIndex);
        }

        // 检查试剂盒编号是否已存在
        AppOfflineOrderEntity existingOrder = appOfflineOrderRepository.getByKitsNo(data.getKitsNo());
        if (existingOrder != null) {
            return String.format("第%d行：试剂盒编号[%s]已存在", rowIndex, data.getKitsNo());
        }

        return null;
    }

    /**
     * 转换为实体对象
     */
    private AppOfflineOrderEntity convertToEntity(OfflineOrderImportDto data, int rowIndex) {
        // 根据商品名称查询商品
        AppGoodsEntity goods = appGoodsRepository.getByTitle(data.getGoodsName());
        if (goods == null) {
            importResult.addError(String.format("第%d行：商品[%s]不存在", rowIndex, data.getGoodsName()));
            return null;
        }

        // 根据商品ID和规格名称查询规格
        AppGoodsSpecsEntity goodsSpecs = appGoodsSpecsRepository.getByGoodsIdAndTitle(goods.getId(), data.getGoodsSpecsName());
        if (goodsSpecs == null) {
            importResult.addError(String.format("第%d行：商品[%s]的规格[%s]不存在", rowIndex, data.getGoodsName(), data.getGoodsSpecsName()));
            return null;
        }

        // 创建线下订单实体
        AppOfflineOrderEntity entity = new AppOfflineOrderEntity();
        entity.setKitsNo(data.getKitsNo());
        entity.setGoodsId(goods.getId());
        entity.setGoodsSpecs(goodsSpecs.getId().toString());
        entity.setGoodsSpecsName(data.getGoodsSpecsName());
        entity.setStatus(0); // 默认未使用状态

        return entity;
    }

    /**
     * 批量保存数据
     */
    private void saveData() {
        if (cachedDataList.isEmpty()) {
            return;
        }

        try {
            boolean result = appOfflineOrderRepository.saveBatch(cachedDataList);
            if (result) {
                importResult.setSuccessCount(importResult.getSuccessCount() + cachedDataList.size());
                log.info("批量保存{}条数据成功", cachedDataList.size());
            } else {
                importResult.addError(String.format("批量保存%d条数据失败", cachedDataList.size()));
            }
        } catch (Exception e) {
            log.error("批量保存数据失败", e);
            importResult.addError(String.format("批量保存数据失败：%s", e.getMessage()));
        } finally {
            cachedDataList.clear();
        }
    }
}
