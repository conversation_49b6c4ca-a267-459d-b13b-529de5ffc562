package com.boot.server.rule;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.boot.server.dto.request.ReportDto;
import com.boot.server.entity.AppAutoReportRuleEntity;
import com.boot.server.repository.AppAutoReportRuleRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 血型、毛长
 * 检测项目、检测结果（取值 coat）
 *
 * <AUTHOR> 2025/7/17 12:51
 */
@Component
@RequiredArgsConstructor
public class CoatRule implements IRule {
    private final AppAutoReportRuleRepository appAutoReportRuleRepository;

    public static final String LONG_HAIR = "毛长";
    public static final String BLOOD_TYPE = "血型";

    @Override
    public boolean isMatch(String group) {
        return Arrays.asList(BLOOD_TYPE, LONG_HAIR).contains(group);
    }

    @Override
    public List<List<List<String>>> run(ReportDto.Group group) {
        List<ReportDto.ReportDetail> items = group.getItems();
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }
        List<AppAutoReportRuleEntity> appAutoReportRuleEntities = appAutoReportRuleRepository.selectByGroup(group.getGroup());
        Map<String, String> ruleMap = appAutoReportRuleEntities.stream().collect(Collectors.toMap(AppAutoReportRuleEntity::getItem, AppAutoReportRuleEntity::getInterpretation));
        ArrayList<List<List<String>>> result = new ArrayList<>();
        for (ReportDto.ReportDetail item : items) {
            String resultInterpretation = ruleMap.get(item.getItem());
            if (StrUtil.isBlank(resultInterpretation)) {
                continue;
            }
            List<List<String>> lists = getTitle("检测项目", "检测结果");
            lists.add(Arrays.asList(item.getItem(), item.getCoat()));
            // 换行
            lists.add(Arrays.asList("", ""));
            lists.add(Collections.singletonList("INTERPRETATION:解读："));
            List<String> resultInterpretationArray = StrUtil.split(resultInterpretation, "\n");
            for (int i = 0; i < resultInterpretationArray.size(); i++) {
                lists.add(Collections.singletonList("CONTENT:" + (i + 1) + ". " + resultInterpretationArray.get(i).replace("{coat}", item.getCoat())));
                lists.add(Collections.singletonList("CONTENT: "));
            }
            result.add(lists);
        }
        return result;
    }
}


