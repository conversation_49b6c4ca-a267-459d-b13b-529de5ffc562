package com.boot.server.rule.combination;

import cn.hutool.core.util.StrUtil;
import com.boot.server.dto.request.ReportDto;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 胱氨酸尿症
 *
 * <AUTHOR> 2025/7/17 14:51
 */
@Component
public class RuleNiaoSuanZheng implements CombinationRule {
    public static final String PROJECT_NAME = "胱氨酸尿症";

    @Override
    public boolean isMatch(String item) {
        return StrUtil.equals(PROJECT_NAME, item);
    }


    @Override
    public List<String> run(List<ReportDto.ReportDetail> detailList) {
        // 若5个位点结果均为N/N
        boolean nnAllMatch = detailList.stream().allMatch(row -> StrUtil.equals(row.getResult(), "N/N"));
        if (nnAllMatch) {
            return Arrays.asList(
                    "本次检测涵盖了与胱氨酸尿症相关的多个位点，这些位点均没有检出致病突变，不会直接导致检测个体患病。",
                    "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
            );
        }
        Map<String, String> resultMap = detailList.stream()
                .collect(Collectors.toMap(ReportDto.ReportDetail::getPosition, ReportDto.ReportDetail::getResult));
        // 若位点：c.1175C>T或者c.706G>A或者c.881T>A出现了N/P或者P/P
        List<String> rule1 = Arrays.asList("N/P", "P/P");
        if (rule1.contains(resultMap.get("c.1175C>T")) || rule1.contains(resultMap.get("c.706G>A")) || rule1.contains(resultMap.get("c.881T>A"))) {
            return Arrays.asList(
                    "本次检测涵盖了与胱氨酸尿症相关的多个位点，其中部分点位出现了致病突变，有可能性导致检测个体罹患该疾病，需要密切关注。",
                    "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
            );
        }
        // 若位点：c.1175C>T或者c.706G>A或者c.881T>A均为N/N，但是其余位点出现了P/P
        if (StrUtil.equalsIgnoreCase(resultMap.get("c.1175C>T"), "N/N") &&
                StrUtil.equalsIgnoreCase(resultMap.get("c.706G>A"), "N/N") &&
                StrUtil.equalsIgnoreCase(resultMap.get("c.881T>A"), "N/N")
        ) {
            // 其余点位出现了 P/P
            long ppCount = detailList.stream()
                    .filter(row -> StrUtil.equalsIgnoreCase(row.getResult(), "P/P") &&
                            !StrUtil.equalsIgnoreCase(row.getPosition(), "c.1175C>T") &&
                            !StrUtil.equalsIgnoreCase(row.getPosition(), "c.706G>A") &&
                            !StrUtil.equalsIgnoreCase(row.getPosition(), "c.881T>A")
                    ).count();
            if (ppCount > 0) {
                return Arrays.asList(
                        "本次检测涵盖了与胱氨酸尿症相关的多个位点，其中部分点位出现了致病突变，有可能性导致检测个体罹患该疾病，需要密切关注。",
                        "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
                );
            }
        }
        // 若位点：c.1175C>T或者c.706G>A或者c.881T>A均为N/N，但是其余位点未出现P/P
        if (StrUtil.equalsIgnoreCase(resultMap.get("c.1175C>T"), "N/N") &&
                StrUtil.equalsIgnoreCase(resultMap.get("c.706G>A"), "N/N") &&
                StrUtil.equalsIgnoreCase(resultMap.get("c.881T>A"), "N/N")
        ) {
            // 其余位点未出现P/P
            long ppCount = detailList.stream()
                    .filter(row -> StrUtil.equalsIgnoreCase(row.getResult(), "P/P") &&
                            !StrUtil.equalsIgnoreCase(row.getPosition(), "c.1175C>T") &&
                            !StrUtil.equalsIgnoreCase(row.getPosition(), "c.706G>A") &&
                            !StrUtil.equalsIgnoreCase(row.getPosition(), "c.881T>A")
                    ).count();
            if (ppCount == 0) {
                return Arrays.asList(
                        "本次检测涵盖了与胱氨酸尿症相关的多个位点，这些位点均没有检出致病突变，不会直接导致检测个体患病。",
                        "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
                );
            }
        }
        return Collections.emptyList();
    }
}
