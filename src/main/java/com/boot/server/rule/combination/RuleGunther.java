package com.boot.server.rule.combination;

import cn.hutool.core.util.StrUtil;
import com.boot.server.dto.request.ReportDto;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 先天性红细胞生成性卟啉症
 *
 * <AUTHOR> 2025/7/17 14:51
 */
@Component
public class RuleGunther implements CombinationRule {
    public static final String PROJECT_NAME = "先天性红细胞生成性卟啉症";

    @Override
    public boolean isMatch(String item) {
        return StrUtil.equals(PROJECT_NAME, item);
    }

    @Override
    public List<String> run(List<ReportDto.ReportDetail> detailList) {
        // 若2个位点结果均为N/N
        boolean nnAllMatch = detailList.stream().allMatch(row -> StrUtil.equals(row.getResult(), "N/N"));

        // 若2个位点结果不均为N/N，且2个位点结果均不为P/P
        boolean nnNoneMatch = detailList.stream().noneMatch(row -> StrUtil.equals(row.getResult(), "N/N"));
        boolean ppNoneMatch = detailList.stream().noneMatch(row -> StrUtil.equals(row.getResult(), "P/P"));
        if (nnAllMatch || (nnNoneMatch && ppNoneMatch)) {
            return Arrays.asList(
                    "本次检测涵盖了与先天性红细胞生成性卟啉症相关的多个位点，这些位点均没有检出致病突变，不会直接导致检测个体患病。",
                    "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
            );
        }
        // 若2个位点结果中任一位点出现P/P（解读1中的N为几个P/P位点）
        long ppCount = detailList.stream().filter(row -> StrUtil.equals(row.getResult(), "P/P")).count();
        if (ppCount > 0) {
            return Arrays.asList(
                    "本次检测涵盖了与先天性红细胞生成性卟啉症相关的多个位点，其中" + ppCount + "个隐性遗传类型的点位出现了2个致病突变，有较高可能性导致检测个体罹患该疾病，需要密切关注。",
                    "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
            );
        }
        return Collections.emptyList();
    }
}
