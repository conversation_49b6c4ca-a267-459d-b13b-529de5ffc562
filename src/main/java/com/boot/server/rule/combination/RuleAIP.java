package com.boot.server.rule.combination;

import cn.hutool.core.util.StrUtil;
import com.boot.server.dto.request.ReportDto;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 急性间歇性卟啉症
 *
 * <AUTHOR> 2025/7/17 14:51
 */
@Component
public class RuleAIP implements CombinationRule {
    public static final String PROJECT_NAME = "急性间歇性卟啉症";

    @Override
    public boolean isMatch(String item) {
        return StrUtil.equals(PROJECT_NAME, item);
    }

    @Override
    public List<String> run(List<ReportDto.ReportDetail> detailList) {
        // 若4个位点结果均为N/N
        boolean nnAllMatch = detailList.stream().allMatch(row -> StrUtil.equals(row.getResult(), "N/N"));
        if (nnAllMatch) {
            return Arrays.asList(
                    "本次检测涵盖了与急性间歇性卟啉症相关的多个位点，这些位点均没有检出致病突变，不会直接导致检测个体患病。",
                    "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
            );
        }
        // 若4个位点结果中任一位点出现N/P或者P/P（解读1中的N为几个N/P或者P/P位点）
        long ppCount = detailList.stream().filter(row -> StrUtil.equals(row.getResult(), "P/P")).count();
        long npCount = detailList.stream().filter(row -> StrUtil.equals(row.getResult(), "N/P")).count();
        if (npCount > 0) {
            return Arrays.asList(
                    "本次检测涵盖了与急性间歇性卟啉症相关的多个位点，其中" + npCount + "个显性遗传类型的点位出现了致病突变，有较高可能性导致检测个体罹患该疾病，需要密切关注。",
                    "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
            );
        }
        if (ppCount > 0) {
            return Arrays.asList(
                    "本次检测涵盖了与急性间歇性卟啉症相关的多个位点，其中" + ppCount + "个显性遗传类型的点位出现了致病突变，有较高可能性导致检测个体罹患该疾病，需要密切关注。",
                    "针对隐性遗传的风险位点，出现2个突变会产生直接致病的可能性；针对非隐性遗传的风险位点，出现不少于1个突变会产生直接致病的可能性。"
            );
        }
        return Collections.emptyList();
    }
}
