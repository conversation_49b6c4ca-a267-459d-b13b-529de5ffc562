package com.boot.server.admin.controller;

import com.boot.server.common.result.Result;
import com.boot.server.dto.IndexResponse;
import com.boot.server.service.AdminIndexService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * AdminIndexController
 *
 * <AUTHOR> 2025/5/19 11:20
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/index")
public class AdminIndexController {
    private final AdminIndexService adminIndexService;
    /**
     * 近 7 日商品销量
     */
    @GetMapping("/goodsSale")
    public Result<List<IndexResponse>> goodsSale() {
        return Result.success(adminIndexService.goodsSale());
    }
}
