package com.boot.server.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.result.Result;
import com.boot.server.common.util.ValidationUtil;
import com.boot.server.dto.ActivityParticipantResponse;
import com.boot.server.dto.AdminIntegralActivityResponse;
import com.boot.server.dto.request.ActivityParticipantRequest;
import com.boot.server.dto.request.AdminIntegralActivityRequest;
import com.boot.server.dto.request.CreateIntegralActivityRequest;
import com.boot.server.dto.request.UpdateIntegralActivityRequest;
import com.boot.server.service.AppIntegralActivityService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 积分活动管理控制器
 *
 * <AUTHOR> 2025/7/23 13:15
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/integral-activity")
public class AdminIntegralActivityController {

    private final AppIntegralActivityService appIntegralActivityService;

    /**
     * 分页查询积分活动列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    @GetMapping("/selectList")
    public Result<Page<AdminIntegralActivityResponse>> selectList(AdminIntegralActivityRequest request) {
        return Result.success(appIntegralActivityService.selectAdminPage(request));
    }

    /**
     * 根据ID查询积分活动详情
     *
     * @param id 积分活动ID
     * @return 积分活动详情
     */
    @GetMapping("/detail/{id}")
    public Result<AdminIntegralActivityResponse> getById(@PathVariable Long id) {
        return Result.success(appIntegralActivityService.getById(id));
    }

    /**
     * 创建积分活动
     *
     * @param request 创建请求
     * @return 是否成功
     */
    @PostMapping("/create")
    public Result<Boolean> create(@RequestBody CreateIntegralActivityRequest request) {
        ValidationUtil.validate(request);
        return Result.boolResult(appIntegralActivityService.create(request));
    }

    /**
     * 更新积分活动
     *
     * @param request 更新请求
     * @return 是否成功
     */
    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody UpdateIntegralActivityRequest request) {
        ValidationUtil.validate(request);
        return Result.boolResult(appIntegralActivityService.update(request));
    }

    /**
     * 删除积分活动
     *
     * @param id 积分活动ID
     * @return 是否成功
     */
    @DeleteMapping("/delete/{id}")
    public Result<Boolean> delete(@PathVariable Long id) {
        return Result.boolResult(appIntegralActivityService.delete(id));
    }

    /**
     * 查询活动参加人员列表
     *
     * @param request 查询请求
     * @return 参加人员列表
     */
    @GetMapping("/participants")
    public Result<Page<ActivityParticipantResponse>> getActivityParticipants(ActivityParticipantRequest request) {
        ValidationUtil.validate(request);
        return Result.success(appIntegralActivityService.selectActivityParticipants(request));
    }
}
