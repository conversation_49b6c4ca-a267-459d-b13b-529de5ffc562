package com.boot.server.admin.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.result.Result;
import com.boot.server.dto.AutoReportDetailResponse;
import com.boot.server.dto.AutoReportResponse;
import com.boot.server.dto.request.AutoReportRequest;
import com.boot.server.entity.AppAutoReportTaskEntity;
import com.boot.server.repository.AppAutoReportTaskRepository;
import com.boot.server.schedu.ReportTask;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * AdminAutoReportController
 *
 * <AUTHOR> 2025/7/19 22:48
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/admin/autoReport")
public class AdminAutoReportController {
    private final AppAutoReportTaskRepository appAutoReportTaskRepository;
    private final ReportTask reportTask;

    @GetMapping("/selectList")
    public Result<Page<AutoReportResponse>> selectAdminPage(AutoReportRequest request) {
        return Result.success(appAutoReportTaskRepository.selectAdminPage(request));
    }


    @GetMapping("/details")
    public Result<List<AutoReportDetailResponse>> selectDetails(final Integer id) {
        return Result.success(appAutoReportTaskRepository.selectDetails(id));
    }

    @PostMapping("/retry")
    public Result<Boolean> retry(final Integer id) {
        AppAutoReportTaskEntity task = appAutoReportTaskRepository.getById(id);
        BusinessException.throwIfNull(task, "任务不存在");
        reportTask.retry(task);
        return Result.boolResult(true);
    }

}
