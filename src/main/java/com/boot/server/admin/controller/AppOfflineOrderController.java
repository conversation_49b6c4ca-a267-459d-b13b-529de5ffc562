package com.boot.server.admin.controller;


import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.result.Result;
import com.boot.server.common.util.ValidationUtil;
import com.boot.server.dto.excel.OfflineOrderImportDto;
import com.boot.server.dto.request.AdminOfflineRequest;
import com.boot.server.dto.response.ImportResultResponse;
import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppOfflineOrderEntity;
import com.boot.server.listener.OfflineOrderImportListener;
import com.boot.server.repository.AppGoodsRepository;
import com.boot.server.repository.AppGoodsSpecsRepository;
import com.boot.server.repository.AppOfflineOrderRepository;
import com.boot.server.service.AppOfflineOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * 线下渠道订单表(AppOfflineOrder)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-11 11:10:23
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/appOfflineOrder")
@RequiredArgsConstructor
public class AppOfflineOrderController {
    /**
     * 服务对象
     */
    private final AppOfflineOrderService appOfflineOrderService;
    private final AppOfflineOrderRepository appOfflineOrderRepository;
    private final AppGoodsRepository appGoodsRepository;
    private final AppGoodsSpecsRepository appGoodsSpecsRepository;

    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody AppOfflineOrderEntity appOfflineOrder) {
        ValidationUtil.validate(appOfflineOrder);
        String kitsNo = appOfflineOrder.getKitsNo();
        long count = appOfflineOrderRepository.count(
                Wrappers.<AppOfflineOrderEntity>lambdaQuery()
                        .eq(AppOfflineOrderEntity::getKitsNo, kitsNo)
        );
        BusinessException.throwIf(count > 0, "该试剂盒编号已存在，请检查后重新输入");
        boolean save = appOfflineOrderRepository.save(appOfflineOrder);
        return Result.boolResult(save);
    }


    @DeleteMapping("/delete")
    public Result<Boolean> delete(@RequestParam final Long id) {
        boolean removed = appOfflineOrderRepository.removeById(id);
        return Result.boolResult(removed);
    }


    @GetMapping("/page")
    public Result<Page<AppOfflineOrderEntity>> selectOfflineOrderPage(AdminOfflineRequest request) {
        return Result.success(appOfflineOrderRepository.selectOfflineOrderPage(request));
    }

    @GetMapping("/goods/list")
    public Result<List<AppGoodsEntity>> selectGoodsList() {
        List<AppGoodsEntity> goodsList = appGoodsRepository.list(
                Wrappers.<AppGoodsEntity>lambdaQuery()
                        .eq(AppGoodsEntity::getStatus, 1)
                        .orderByAsc(AppGoodsEntity::getSort)
        );
        return Result.success(goodsList);
    }

    @GetMapping("/goods/{id}")
    public Result<AppGoodsEntity> selectById(@PathVariable Integer id) {
        AppGoodsEntity goods = appGoodsRepository.selectById(id);
        return Result.success(goods);
    }

    /**
     * Excel导入线下渠道订单
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @PostMapping("/import")
    public Result<ImportResultResponse> importOfflineOrders(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.failed("上传文件不能为空");
        }

        // 检查文件类型
        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
            return Result.failed("请上传Excel文件（.xlsx或.xls格式）");
        }

        ImportResultResponse importResult = new ImportResultResponse();

        try {
            // 创建监听器
            OfflineOrderImportListener listener = new OfflineOrderImportListener(
                    appOfflineOrderRepository,
                    appGoodsRepository,
                    appGoodsSpecsRepository,
                    importResult
            );

            // 读取Excel文件
            EasyExcel.read(file.getInputStream(), OfflineOrderImportDto.class, listener)
                    .sheet()
                    .headRowNumber(1) // 第一行为表头
                    .doRead();

            log.info("Excel导入完成，成功：{}条，失败：{}条", importResult.getSuccessCount(), importResult.getFailCount());

            if (importResult.hasError()) {
                return Result.success(importResult, "导入完成，但存在部分错误");
            } else {
                return Result.success(importResult, "导入成功");
            }

        } catch (IOException e) {
            log.error("读取Excel文件失败", e);
            return Result.failed("读取Excel文件失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("导入过程中发生异常", e);
            return Result.failed("导入失败：" + e.getMessage());
        }
    }

    /**
     * 下载Excel导入模板
     *
     * @param response HTTP响应
     */
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("线下渠道订单导入模板", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=" + fileName + ".xlsx");
            // 创建模板数据
            List<OfflineOrderImportDto> templateData = new ArrayList<>();

            // 单个规格示例
            OfflineOrderImportDto example1 = new OfflineOrderImportDto();
            example1.setKitsNo("KIT001");
            example1.setGoodsName("示例商品名称");
            example1.setGoodsSpecsName("示例规格名称");
            templateData.add(example1);

            // 多个规格示例
            OfflineOrderImportDto example2 = new OfflineOrderImportDto();
            example2.setKitsNo("KIT002");
            example2.setGoodsName("示例商品名称");
            example2.setGoodsSpecsName("规格1，规格2，规格3");
            templateData.add(example2);

            // 写入Excel
            EasyExcel.write(response.getOutputStream(), OfflineOrderImportDto.class)
                    .sheet("线下渠道订单")
                    .doWrite(templateData);
        } catch (IOException e) {
            log.error("下载模板失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}

