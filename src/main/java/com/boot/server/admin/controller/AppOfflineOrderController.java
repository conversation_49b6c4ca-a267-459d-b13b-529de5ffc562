package com.boot.server.admin.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.result.Result;
import com.boot.server.common.util.ValidationUtil;
import com.boot.server.dto.request.AdminOfflineRequest;
import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppOfflineOrderEntity;
import com.boot.server.repository.AppGoodsRepository;
import com.boot.server.repository.AppOfflineOrderRepository;
import com.boot.server.service.AppOfflineOrderService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线下渠道订单表(AppOfflineOrder)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-11 11:10:23
 */
@RestController
@RequestMapping("/api/admin/appOfflineOrder")
@RequiredArgsConstructor
public class AppOfflineOrderController {
    /**
     * 服务对象
     */
    private final AppOfflineOrderService appOfflineOrderService;
    private final AppOfflineOrderRepository appOfflineOrderRepository;
    private final AppGoodsRepository appGoodsRepository;

    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody AppOfflineOrderEntity appOfflineOrder) {
        ValidationUtil.validate(appOfflineOrder);
        String kitsNo = appOfflineOrder.getKitsNo();
        long count = appOfflineOrderRepository.count(
                Wrappers.<AppOfflineOrderEntity>lambdaQuery()
                        .eq(AppOfflineOrderEntity::getKitsNo, kitsNo)
        );
        BusinessException.throwIf(count > 0, "该试剂盒编号已存在，请检查后重新输入");
        boolean save = appOfflineOrderRepository.save(appOfflineOrder);
        return Result.boolResult(save);
    }


    @DeleteMapping("/delete")
    public Result<Boolean> delete(@RequestParam final Long id) {
        boolean removed = appOfflineOrderRepository.removeById(id);
        return Result.boolResult(removed);
    }


    @GetMapping("/page")
    public Result<Page<AppOfflineOrderEntity>> selectOfflineOrderPage(AdminOfflineRequest request) {
        return Result.success(appOfflineOrderRepository.selectOfflineOrderPage(request));
    }

    @GetMapping("/goods/list")
    public Result<List<AppGoodsEntity>> selectGoodsList() {
        List<AppGoodsEntity> goodsList = appGoodsRepository.list(
                Wrappers.<AppGoodsEntity>lambdaQuery()
                        .eq(AppGoodsEntity::getStatus, 1)
                        .orderByAsc(AppGoodsEntity::getSort)
        );
        return Result.success(goodsList);
    }

    @GetMapping("/goods/{id}")
    public Result<AppGoodsEntity> selectById(@PathVariable Integer id) {
        AppGoodsEntity goods = appGoodsRepository.selectById(id);
        return Result.success(goods);
    }
}

