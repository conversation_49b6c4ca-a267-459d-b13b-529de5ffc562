package com.boot.server.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.dto.ActivityParticipantResponse;
import com.boot.server.dto.request.ActivityParticipantRequest;
import com.boot.server.entity.AppIntegralActivityUserEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 积分活动参加用户(AppIntegralActivityUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-23 13:14:58
 */
public interface AppIntegralActivityUserMapper extends BaseMapper<AppIntegralActivityUserEntity> {

    /**
     * 分页查询活动参加人员列表
     *
     * @param page    分页参数
     * @param request 查询请求
     * @return 分页结果
     */
    Page<ActivityParticipantResponse> selectActivityParticipants(
            @Param("page") Page<ActivityParticipantResponse> page,
            @Param("request") ActivityParticipantRequest request
    );
}

