package com.boot.server.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.dto.CartListResponse;
import com.boot.server.entity.AppShoppingCartEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 购物车(AppShoppingCart)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-05 10:58:30
 */
public interface AppShoppingCartMapper extends BaseMapper<AppShoppingCartEntity> {

    Page<CartListResponse> selectPageUserCart(@Param("userId") Long currentUserId, Page<CartListResponse> page);

    Boolean updateNum(@Param("id") Long id, @Param("value") Integer value, @Param("userId") Long userId);

    int updateCartQuantity(@Param("id") Long id, @Param("number") Long number, @Param("userId") Long userId);
}

