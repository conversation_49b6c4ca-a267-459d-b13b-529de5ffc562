package com.boot.server.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.dto.request.AdminOfflineRequest;
import com.boot.server.entity.AppOfflineOrderEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 线下渠道订单表(AppOfflineOrder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-11 11:10:23
 */
public interface AppOfflineOrderMapper extends BaseMapper<AppOfflineOrderEntity> {

    Page<AppOfflineOrderEntity> selectOfflineOrderPage(Page<AppOfflineOrderEntity> page, @Param("request") AdminOfflineRequest request);
}

