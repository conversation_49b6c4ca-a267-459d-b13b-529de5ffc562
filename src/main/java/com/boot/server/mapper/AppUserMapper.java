package com.boot.server.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boot.server.entity.AppUserEntity;
import org.apache.ibatis.annotations.Param;

/**
 * 用户表(AppUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-02 22:12:30
 */
public interface AppUserMapper extends BaseMapper<AppUserEntity> {
    /**
     * 更新用户积分
     *
     * @param userId   用户 ID
     * @param integral 积分操作
     * @return 更新的行数
     */
    int updateIntegral(@Param("userId") final Long userId, @Param("integral") final Long integral);

}

