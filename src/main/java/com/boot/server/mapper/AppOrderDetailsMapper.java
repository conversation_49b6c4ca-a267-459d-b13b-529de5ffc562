package com.boot.server.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.dto.AdminBinderListResponse;
import com.boot.server.dto.OrderDetailResponse;
import com.boot.server.dto.request.AdminBinderListRequest;
import com.boot.server.entity.AppOrderDetailsEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户订单明细(AppOrderDetails)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
public interface AppOrderDetailsMapper extends BaseMapper<AppOrderDetailsEntity> {

    List<OrderDetailResponse> selectListPayedAndNotUsed(Long currentUserId);

    Page<AdminBinderListResponse> selectOrderBinderList(Page<AdminBinderListResponse> page, @Param("request") AdminBinderListRequest request);

    List<AppOrderDetailsEntity> selectListByOrderNo(@Param("orderNo") String orderNo);
}

