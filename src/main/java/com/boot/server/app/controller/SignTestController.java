package com.boot.server.app.controller;

import com.boot.server.common.aop.annotation.SignVerify;
import com.boot.server.common.result.R;
import com.boot.server.dto.request.QueryReportRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 验签测试Controller
 * 演示如何使用@SignVerify注解
 *
 * <AUTHOR>
 * @date 2023-12-23
 */
@RestController
@RequestMapping("/open/api/sign")
@Slf4j
public class SignTestController {

    /**
     * 简单的验签测试
     * 使用默认配置
     */
    @PostMapping("/test1")
    @SignVerify
    public R<String> testSignVerify1(@RequestBody QueryReportRequest queryReportRequest) {
        log.info("验签成功，处理业务逻辑");
        return R.success("验签成功，业务处理完成");
    }

    /**
     * 自定义验签配置
     * 指定有效期和错误消息
     */
    @PostMapping("/test2")
    @SignVerify(
            validPeriod = 10 * 60 * 1000, // 10分钟有效期
            errorMessage = "自定义验签失败消息"
    )
    public R<String> testSignVerify2(@RequestBody Map<String, Object> requestData) {
        log.info("自定义验签配置成功，处理业务逻辑");
        return R.success("自定义验签成功，业务处理完成");
    }
} 