package com.boot.server.app.controller;


import com.boot.server.common.result.Result;
import com.boot.server.dto.GoodsSpecsDetailResponse;
import com.boot.server.service.AppGoodsSpecsDetailService;
import com.boot.server.repository.AppGoodsSpecsDetailRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 规格详情表(AppGoodsSpecsDetail)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-22 23:38:23
 */
@RestController
@RequestMapping("/open/api/appGoodsSpecsDetail")
@RequiredArgsConstructor
public class AppGoodsSpecsDetailController {
    /**
     * 服务对象
     */
    private final AppGoodsSpecsDetailService appGoodsSpecsDetailService;
    private final AppGoodsSpecsDetailRepository appGoodsSpecsDetailRepository;

    /**
     * 获取规格明细(品种套餐)
     * @param specsId 规格ID
     * @return 规格明细
     */
    @GetMapping("/detail")
    public Result<List<GoodsSpecsDetailResponse>> getSpecsDetailList(@RequestParam Integer specsId) {
        List<GoodsSpecsDetailResponse> list = appGoodsSpecsDetailService.getSpecsDetailList(specsId);
        return Result.success(list);
    }
}

