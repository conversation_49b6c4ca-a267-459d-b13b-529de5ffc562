package com.boot.server.app.controller;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.result.Result;
import com.boot.server.common.util.ValidationUtil;
import com.boot.server.dto.CalcOrderResponse;
import com.boot.server.dto.OrderListResponse;
import com.boot.server.dto.OrderRouterResponse;
import com.boot.server.dto.PushOrderResponse;
import com.boot.server.dto.request.CalcOrderRequest;
import com.boot.server.dto.request.OrderListRequest;
import com.boot.server.repository.AppOrdersRepository;
import com.boot.server.repository.ExpressHelper;
import com.boot.server.service.AppOrdersService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 用户订单表(AppOrders)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@RestController
@RequestMapping("/api/appOrders")
@RequiredArgsConstructor
public class AppOrdersController {
    /**
     * 服务对象
     */
    private final AppOrdersService appOrdersService;
    private final AppOrdersRepository appOrdersRepository;
    private final ExpressHelper expressHelper;

    // 计算订单信息不生成订单
    @PostMapping("/calcOrder")
    public Result<CalcOrderResponse> selectCalcOrder(@RequestBody CalcOrderRequest calcOrderRequest) {
        ValidationUtil.validate(calcOrderRequest);
        CalcOrderResponse response = appOrdersService.selectCalcOrder(calcOrderRequest);
        return Result.success(response);
    }


    // 生成订单 返回 订单号
    @PostMapping("/pushOrder")
    public Result<PushOrderResponse> pushOrder(@RequestBody CalcOrderResponse calcOrderResponse) {
        ValidationUtil.validate(calcOrderResponse);
        return Result.success(appOrdersService.pushOrder(calcOrderResponse));
    }


    // 订单详情订单号
    @GetMapping("/getOrder")
    public Result<CalcOrderResponse> getOrder(@RequestParam String orderNo) {
        return Result.success(appOrdersService.selectOrderByNo(orderNo));
    }


    // 我的订单列表
    @GetMapping("/my/orders")
    public Result<Page<OrderListResponse>> pageOrders(OrderListRequest orderListRequest) {
        return Result.success(appOrdersService.pageOrders(orderListRequest));
    }


    @PostMapping("cancel")
    public Result<Boolean> cancel(@RequestParam String orderNo) {
        return Result.boolResult(appOrdersService.cancel(orderNo, false));
    }


    @GetMapping("/express/routers")
    public Result<OrderRouterResponse> routers(@RequestParam String logistics) {
        return Result.success(expressHelper.queryRoutes(logistics));
    }

}

