package com.boot.server.app.controller;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.dto.ClassifyDto;
import com.boot.server.common.dto.PageRequest;
import com.boot.server.common.result.Result;
import com.boot.server.config.AppConfig;
import com.boot.server.entity.AppPetEntity;
import com.boot.server.repository.AppPetClassifyRepository;
import com.boot.server.repository.AppPetRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 爱宠车(AppPet)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-09 13:56:14
 */
@RestController
@RequestMapping("/api/appPet")
@RequiredArgsConstructor
public class AppPetController {
    private final AppPetRepository appPetRepository;
    private final AppPetClassifyRepository appPetClassifyRepository;
    private final AppConfig appConfig;

    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody AppPetEntity appPetEntity) {
        if (StrUtil.isBlank(appPetEntity.getAvatar())) {
            appPetEntity.setAvatar(appConfig.getDefaultPetAvatar());
        }
        return Result.success(appPetRepository.addAppPet(appPetEntity));
    }

    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody AppPetEntity appPetEntity) {
        return Result.success(appPetRepository.updateById(appPetEntity));
    }

    @GetMapping("/detail")
    public Result<AppPetEntity> detail(@RequestParam Long id) {
        return Result.success(appPetRepository.selectDetailById(id));
    }

    @GetMapping("/classify")
    public Result<List<ClassifyDto<Long>>> selectClassifyList() {
        return Result.success(appPetClassifyRepository.selectClassifyList());
    }

    @GetMapping("/page")
    public Result<Page<AppPetEntity>> page(PageRequest pageRequest) {
        return Result.success(appPetRepository.selectUserPet(pageRequest));
    }

    @GetMapping("/selectNoneDetectCat")
    public Result<List<AppPetEntity>> selectNoneDetectCat() {
        return Result.success(appPetRepository.selectNoneDetectCat());
    }

}

