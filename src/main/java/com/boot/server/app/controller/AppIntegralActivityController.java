package com.boot.server.app.controller;

import com.boot.server.common.result.Result;
import com.boot.server.common.util.ValidationUtil;
import com.boot.server.dto.JoinIntegralActivityResponse;
import com.boot.server.dto.request.JoinIntegralActivityRequest;
import com.boot.server.service.AppIntegralActivityService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 用户端积分活动控制器
 *
 * <AUTHOR> 2025/7/23 14:45
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/integral-activity")
public class AppIntegralActivityController {

    private final AppIntegralActivityService appIntegralActivityService;

    /**
     * 用户参加积分活动
     *
     * @param request 参加活动请求
     * @return 参加结果
     */
    @PostMapping("/join")
    public Result<JoinIntegralActivityResponse> joinActivity(@RequestBody JoinIntegralActivityRequest request) {
        ValidationUtil.validate(request);
        JoinIntegralActivityResponse response = appIntegralActivityService.joinActivity(request);
        return Result.success(response);
    }
}
