package com.boot.server.app.controller;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.result.Result;
import com.boot.server.common.util.PageUtil;
import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppGoodsSpecsEntity;
import com.boot.server.repository.AppGoodsRepository;
import com.boot.server.repository.AppGoodsSpecsDetailRepository;
import com.boot.server.repository.AppGoodsSpecsRepository;
import com.boot.server.service.AppGoodsSpecsDetailService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 商品表(AppGoods)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-04 18:40:00
 */
@RestController
@RequestMapping("/open/api/goods")
public class AppGoodsController {
    /**
     * 服务对象
     */
    @Resource
    private AppGoodsRepository appGoodsRepository;
    @Resource
    private AppGoodsSpecsRepository appGoodsSpecsRepository;
    @Resource
    private AppGoodsSpecsDetailService appGoodsSpecsDetailService;

    @GetMapping("/list")
    public Result<Page<AppGoodsEntity>> selectPage(Integer pageNum, Integer pageSize) {
        Page<AppGoodsEntity> page = PageUtil.getPage(pageNum, pageSize);
        Page<AppGoodsEntity> paged = appGoodsRepository.page(
                page,
                Wrappers.<AppGoodsEntity>lambdaQuery()
                        .eq(AppGoodsEntity::getStatus, 1)
                        .orderByAsc(AppGoodsEntity::getSort)
        );
        return Result.success(paged);
    }

    @GetMapping("/index")
    public Result<List<AppGoodsEntity>> selectIndexGoods() {
        List<AppGoodsEntity> result = appGoodsRepository.list(
                Wrappers.<AppGoodsEntity>lambdaQuery()
                        .eq(AppGoodsEntity::getStatus, 1)
                        .eq(AppGoodsEntity::getIndexShow, 1)
                        .orderByAsc(AppGoodsEntity::getSort)
                        .last("limit 4")
        );
        return Result.success(result);
    }

    @GetMapping("/{id}")
    public Result<AppGoodsEntity> selectById(@PathVariable Integer id) {
        AppGoodsEntity goods = appGoodsRepository.selectById(id);
        return Result.success(goods);
    }

    @PutMapping("/clearCache")
    public Result<Boolean> clear() {
        appGoodsSpecsDetailService.clearCache();
        return Result.boolResult(true);
    }

}

