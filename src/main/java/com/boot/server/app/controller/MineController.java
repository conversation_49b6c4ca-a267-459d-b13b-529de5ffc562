package com.boot.server.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.result.Result;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.common.util.PageUtil;
import com.boot.server.dto.CartListResponse;
import com.boot.server.dto.MineResponse;
import com.boot.server.dto.OrderListResponse;
import com.boot.server.dto.request.OrderListRequest;
import com.boot.server.repository.AppOrdersRepository;
import com.boot.server.repository.AppShoppingCartRepository;
import com.boot.server.service.AppOrdersService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * Mine<PERSON>ontroller
 *
 * <AUTHOR> 2025/5/11 11:13
 */
@RestController
@RequestMapping("/api/mine")
@RequiredArgsConstructor
public class MineController {
    private final AppOrdersService appOrdersService;
    private final AppShoppingCartRepository appShoppingCartRepository;

    @GetMapping("/cartAndOrders")
    public Result<MineResponse> mine() {
        Long currentUserId = AuthUtil.getCurrentUserId();
        Page<CartListResponse> cartListResponsePage = PageUtil.getPage(1, 2);
        Page<CartListResponse> cartListResponse = appShoppingCartRepository.selectPageUserCart(currentUserId, cartListResponsePage);
        OrderListRequest orderListRequest = new OrderListRequest();
        orderListRequest.setPageSize(2);
        Page<OrderListResponse> orderListResponse = appOrdersService.pageOrders(orderListRequest);
        MineResponse mineResponse = new MineResponse();
        mineResponse.setCartListResponse(cartListResponse.getRecords());
        mineResponse.setOrderListResponse(orderListResponse.getRecords());
        return Result.success(mineResponse);
    }
}
