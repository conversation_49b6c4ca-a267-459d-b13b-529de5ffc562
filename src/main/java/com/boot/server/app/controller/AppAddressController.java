package com.boot.server.app.controller;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.dto.PageRequest;
import com.boot.server.common.result.Result;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.entity.AppAddressEntity;
import com.boot.server.repository.AppAddressRepository;
import com.boot.server.service.AppAddressService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * 地址表(AppAddress)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-09 16:26:12
 */
@RestController
@RequestMapping("/api/appAddress")
@RequiredArgsConstructor
public class AppAddressController {
    /**
     * 服务对象
     */
    private final AppAddressService appAddressService;
    private final AppAddressRepository appAddressRepository;

    @PostMapping("/add")
    public Result<Boolean> add(@RequestBody final AppAddressEntity appAddress) {
        return Result.success(appAddressRepository.insert(appAddress));
    }


    @GetMapping("/detail")
    public Result<AppAddressEntity> detail(@RequestParam final Long id) {
        return Result.success(appAddressRepository.getById(id));
    }

    @PutMapping("/update")
    public Result<Boolean> update(@RequestBody final AppAddressEntity appAddress) {
        return Result.success(appAddressRepository.update(appAddress));
    }

    @DeleteMapping("/deleteByIds")
    public Result<Boolean> update(@RequestParam final Long[] ids) {
        return Result.success(
                appAddressRepository.remove(
                        Wrappers.<AppAddressEntity>lambdaQuery()
                                .in(AppAddressEntity::getId, Arrays.asList(ids))
                                .eq(AppAddressEntity::getCreateUser, AuthUtil.getCurrentUserId())
                )
        );
    }


    @PutMapping("/setDefault")
    public Result<Boolean> setDefault(@RequestParam final Long id) {
        return Result.success(appAddressRepository.setDefault(id));
    }

    @GetMapping("/queryDefault")
    public Result<AppAddressEntity> queryDefault() {
        AppAddressEntity appAddress = appAddressRepository.getOne(
                Wrappers.<AppAddressEntity>lambdaQuery()
                        .eq(AppAddressEntity::getCreateUser, AuthUtil.getCurrentUserId())
                        .eq(AppAddressEntity::getIsDefault, 1)
        );
        return Result.success(appAddress);
    }

    @GetMapping("/page")
    public Result<Page<AppAddressEntity>> page(PageRequest pageRequest) {
        Page<AppAddressEntity> page = pageRequest.getPage();
        LambdaQueryWrapper<AppAddressEntity> queryWrapper = Wrappers.<AppAddressEntity>lambdaQuery()
                .eq(AppAddressEntity::getCreateUser, AuthUtil.getCurrentUserId());
        return Result.success(appAddressRepository.page(page, queryWrapper));
    }

}

