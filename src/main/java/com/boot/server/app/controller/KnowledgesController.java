package com.boot.server.app.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boot.server.common.result.Result;
import com.boot.server.entity.AppKnowledgesEntity;
import com.boot.server.repository.AppKnowledgesRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * BannerController
 *
 * <AUTHOR> 2025/5/3 16:48
 */
@RestController
@RequestMapping("/open/api/knowledges")
@RequiredArgsConstructor
public class KnowledgesController {
    private final AppKnowledgesRepository appKnowledgesRepository;

    @GetMapping("/list")
    public Result<List<AppKnowledgesEntity>> selectBanner() {
        List<AppKnowledgesEntity> entityList = appKnowledgesRepository.list(
                Wrappers.<AppKnowledgesEntity>lambdaQuery()
                        .eq(AppKnowledgesEntity::getStatus, 1)
                        .orderByAsc(AppKnowledgesEntity::getSort)
                        .last("limit 3")
        );
        return Result.success(entityList);
    }
}
