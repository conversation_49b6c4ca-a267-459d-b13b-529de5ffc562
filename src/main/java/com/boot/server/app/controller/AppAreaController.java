package com.boot.server.app.controller;


import com.boot.server.common.result.Result;
import com.boot.server.dto.TreeAreaResponse;
import com.boot.server.repository.AppAreaRepository;
import com.boot.server.service.AppAreaService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 省市县(AppArea)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-09 16:35:42
 */
@RestController
@RequestMapping("/open/api/appArea")
@RequiredArgsConstructor
public class AppAreaController {

    /**
     * 服务对象
     */
    private final AppAreaService appAreaService;
    private final AppAreaRepository appAreaRepository;

    @GetMapping("/tree")
    public Result<List<TreeAreaResponse>> selectAreaTreeList() {
        return Result.success(appAreaRepository.selectAreaTreeList());
    }
}

