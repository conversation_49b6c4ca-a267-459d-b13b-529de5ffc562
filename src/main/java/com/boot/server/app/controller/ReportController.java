package com.boot.server.app.controller;

import com.boot.server.common.result.Result;
import com.boot.server.dto.ReportResponse;
import com.boot.server.dto.UploadReportResponse;
import com.boot.server.dto.request.UploadReportRequest;
import com.boot.server.service.ReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件上传控制器
 */
@Slf4j
@RestController
@RequestMapping("/open/api/report")
@RequiredArgsConstructor
public class ReportController {
    private final ReportService reportService;

    /**
     * 上传文件
     *
     * @param request 文件夹路径参数
     * @return 文件访问路径
     */
    @PostMapping("/upload")
    public Result<UploadReportResponse> uploadReport(@RequestBody UploadReportRequest request) {
        try {
            return Result.success(reportService.uploadReport(request));
        } catch (Exception e) {
            log.error("上报数据错误", e);
            return Result.failed("报告上传失败: " + e.getMessage());
        }
    }

    /**
     * 自动化报告生成
     *
     * @param request 文件夹路径参数
     * @return 文件访问路径
     */
    @PostMapping("/system/upload")
    public Result<UploadReportResponse> uploadOtherSystemReport(@RequestBody UploadReportRequest request) {
        try {
            return Result.success(reportService.uploadOtherSystemReport(request));
        } catch (Exception e) {
            log.error("上报数据错误", e);
            return Result.failed("报告上传失败: " + e.getMessage());
        }
    }

    /**
     * 获取报告详情
     *
     * @param binderId 报告ID
     * @deprecated 使用 {@link #getReportDetailByPetId(Long)} 替代
     */
    @GetMapping("/detail")
    @Deprecated
    public Result<ReportResponse> detail(@RequestParam("id") Long binderId) {
        return Result.success(reportService.getDetailById(binderId));
    }


    /**
     * 获取报告详情
     *
     * @param petId 宠物ID
     */
    @GetMapping("/reportDetailByPetId")
    public Result<ReportResponse> getReportDetailByPetId(@RequestParam("id") Long petId) {
        return Result.success(reportService.getReportDetailByPetId(petId));
    }

    /**
     * 创建证书
     *
     * @param detailIds 报告明细ID
     * @param petId     宠物ID
     */
    @PostMapping("/createCert/{petId}")
    public Result<List<String>> createCert(@PathVariable final Long petId, @RequestBody List<String> detailIds) {
        return Result.success(reportService.createCert(detailIds, petId));
    }
}
