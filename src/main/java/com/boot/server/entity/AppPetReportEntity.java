package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 试剂盒报告结果(AppPetReport)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-14 16:41:58
 */
@Data
@Accessors(chain = true)
@TableName("app_pet_report")
@EqualsAndHashCode(callSuper = true)
public class AppPetReportEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 726487739750590940L;
    /**
     * 宠物ID
     */
    @TableField("pet_id")
    private Long petId;
    /**
     * 试剂盒编号
     */
    @TableField("kits_no")
    private String kitsNo;
    /**
     * 报告结果
     */
    @TableField("report")
    private String report;
    /**
     * 订单详情ID
     */
    @TableField("order_detail_id")
    private Long orderDetailId;
    /**
     * 处理状态0未处理 1已处理
     */
    @TableField("status")
    private Integer status;
}

