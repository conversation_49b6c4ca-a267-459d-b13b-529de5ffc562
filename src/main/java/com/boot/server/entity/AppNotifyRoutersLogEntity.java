package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 顺丰物流路由通知记录表(AppNotifyRoutersLog)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-13 16:03:26
 */
@Data
@Accessors(chain = true)
@TableName("app_notify_routers_log")
@EqualsAndHashCode(callSuper = true)
public class AppNotifyRoutersLogEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -39856067547017349L;
    /**
     * 物流单号
     */
    @TableField("logistics")
    private String logistics;
    /**
     * 通知消息
     */
    @TableField("body")
    private String body;
}

