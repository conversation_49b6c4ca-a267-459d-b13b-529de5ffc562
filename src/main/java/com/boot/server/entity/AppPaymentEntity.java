package com.boot.server.entity;


import java.math.BigDecimal;
import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 订单支付表(AppPayment)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Data
@Accessors(chain = true)
@TableName("app_payment")
@EqualsAndHashCode(callSuper = true)
public class AppPaymentEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -35871668115255985L;
    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;
    /**
     * 支付渠道交易 ID
     */
    @TableField("transaction_id")
    private String transactionId;
    /**
     * 预付款ID
     */
    @TableField("prepay_id")
    private String prepayId;
    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;
    /**
     * 状态；1-待支付 2-支付成功 3-支付失败
     */
    @TableField("status")
    private Integer status;
}

