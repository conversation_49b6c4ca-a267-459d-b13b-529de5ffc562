package com.boot.server.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.server.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 爱宠车(AppPet)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-09 13:53:35
 */
@Data
@Accessors(chain = true)
@TableName("app_pet")
@EqualsAndHashCode(callSuper = true)
public class AppPetEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 581174931362078756L;
    /**
     * 宠物编号
     */
    @TableField("pet_no")
    private String petNo;
    /**
     * 宠物名称
     */
    @TableField("pet_name")
    private String petName;
    /**
     * 宠物乳名名称
     */
    @TableField("pet_ru_name")
    private String petRuName;
    /**
     * 品种
     */
    @TableField("varieties")
    private String varieties;

    /**
     * 出生日期
     */
    @TableField("birth_day")
    private LocalDate birthDay;
    /**
     * 性别(1雌性 2雄性)
     */
    @TableField("gender")
    private Integer gender;

    public String getGenderName() {
        return Objects.equals(gender, 1) ? "雌性" : "雄性";
    }

    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;
    /**
     * 是否绝育 1是 0否
     */
    @TableField("sterilization")
    private Integer sterilization;
    /**
     * 检测状态: 0待检测 1待收件 2检测中 3已检测 4已绑定
     */
    @TableField("detect_status")
    private Integer detectStatus;

    @TableField(exist = false)
    private Integer hasReport;

    /**
     * 顺丰物流单号
     */
    @TableField(exist = false)
    private List<String> logistics;

    /**
     * 品种名称
     */
    public String getVarietiesName() {
        return this.getVarieties();
    }
}

