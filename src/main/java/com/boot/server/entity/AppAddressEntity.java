package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 地址表(AppAddress)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-09 16:26:11
 */
@Data
@Accessors(chain = true)
@TableName("app_address")
@EqualsAndHashCode(callSuper = true)
public class AppAddressEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 831914726428698641L;
    /**
     * 姓名
     */
    @TableField("name")
    private String name;
    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;
    /**
     * 省份
     */
    @TableField("province")
    private String province;
    /**
     * 市
     */
    @TableField("city")
    private String city;
    /**
     * 区
     */
    @TableField("district")
    private String district;
    /**
     * 详细地址
     */
    @TableField("address")
    private String address;
    /**
     * 是否默认 1默认 0 非默认
     */
    @TableField("is_default")
    private Integer isDefault;

    public String getAddressInfo() {
        return province + city + district + address;
    }
}

