package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 自动化报告明细(AppAutoReportDetail)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-18 10:59:50
 */
@Data
@Accessors(chain = true)
@TableName("app_auto_report_detail")
@EqualsAndHashCode(callSuper = true)
public class AppAutoReportDetailEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -27661152933648694L;
    /**
     * 任务ID
     */
    @TableField("task_id")
    private Integer taskId;
    /**
     * 项目
     */
    @TableField("item")
    private String item;
    /**
     * 位点
     */
    @TableField("position")
    private String position;
    /**
     * 突变情况
     */
    @TableField("result")
    private String result;
    /**
     * 突变情况
     */
    @TableField("img")
    private String img;
}

