package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 订单宠物绑定记录(AppPetBindOrder)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-12 17:37:33
 */
@Data
@Accessors(chain = true)
@TableName("app_pet_bind_order")
@EqualsAndHashCode(callSuper = true)
public class AppPetBindOrderEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -29131118742921083L;
    /**
     * 绑定编号
     */
    @TableField("binder_no")
    private String binderNo;
    /**
     * 订单ID
     */
    @TableField("order_id")
    private Long orderId;
    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;
    /**
     * 订单明细ID
     */
    @TableField("order_detail_id")
    private Long orderDetailId;
    /**
     * 宠物ID
     */
    @TableField("pet_id")
    private Long petId;
    /**
     * 试剂盒编号
     */
    @TableField("kits_no")
    private String kitsNo;
    /**
     * 物流单号
     */
    @TableField("logistics")
    private String logistics;
    /**
     * 品种套餐冗余品种
     */
    @TableField("varieties")
    private String varieties;
    /**
     * 寄件人姓名
     */
    @TableField("sender_name")
    private String senderName;
    /**
     * 寄件人电话
     */
    @TableField("sender_tel")
    private String senderTel;
    /**
     * 寄件人详细地址
     */
    @TableField("sender_address")
    private String senderAddress;
    /**
     * 物流状态 0未回寄 1处理中 2已取件
     */
    @TableField("status")
    private Integer status;
    /**
     * 有报告
     */
    @TableField("is_report")
    private Integer isReport;
    /**
     * 有证书
     */
    @TableField("is_certificate")
    private Integer isCertificate;
    /**
     * 证书文件名
     */
    @TableField("cert_images")
    private String certImages;
}

