package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 购物车(AppShoppingCart)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-05 10:58:29
 */
@Data
@Accessors(chain = true)
@TableName("app_shopping_cart")
@EqualsAndHashCode(callSuper = true)
public class AppShoppingCartEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 282854579531181627L;
    /**
     * 商品表
     */
    @TableField("goods_id")
    private Long goodsId;
    /**
     * 规格数据
     */
    @TableField("specs")
    private String specs;
    /**
     * 数量
     */
    @TableField("num")
    private Integer num;
    /**
     * 商品类型 0普通套餐 1随心选
     */
    @TableField("goods_type")
    private Integer goodsType;
}

