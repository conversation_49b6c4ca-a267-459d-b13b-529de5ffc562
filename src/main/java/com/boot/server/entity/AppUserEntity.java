package com.boot.server.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.server.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户表(AppUser)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-02 22:12:30
 */
@Data
@Accessors(chain = true)
@TableName("app_user")
@EqualsAndHashCode(callSuper = true)
public class AppUserEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 761711953793105232L;

    /**
     * 分享码
     */
    @TableField("share_uid")
    private String shareUid;

    /**
     * 邀请人分享码
     */
    @TableField("invite_share_code")
    private String inviteShareCode;
    /**
     * 分享码路径
     */
    @TableField("share_code_file")
    private String shareCodeFile;
    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;
    /**
     * 昵称
     */
    @TableField("nick_name")
    private String nickName;
    /**
     * 微信用户openId
     */
    @TableField("open_id")
    private String openId;
    /**
     * 微信用户uniId
     */
    @TableField("uni_id")
    private String uniId;
    /**
     * 积分
     */
    @TableField("integral")
    private Long integral;
    /**
     * 用户手机号
     */
    @TableField("phone")
    private String phone;
    /**
     * 是否第一次已消费
     */
    @TableField("first_shop")
    private Integer firstShop;
}

