package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 发货仓地址表(AppDepotAddress)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-13 11:07:57
 */
@Data
@Accessors(chain = true)
@TableName("app_depot_address")
@EqualsAndHashCode(callSuper = true)
public class AppDepotAddressEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 527301316098839922L;
    /**
     * 姓名
     */
    @TableField("name")
    private String name;
    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;
    /**
     * 省份
     */
    @TableField("province")
    private String province;
    /**
     * 市
     */
    @TableField("city")
    private String city;
    /**
     * 区
     */
    @TableField("district")
    private String district;
    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 是否默认使用
     */
    @TableField("is_default")
    private Integer isDefault;
}

