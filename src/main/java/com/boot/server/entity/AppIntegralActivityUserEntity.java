package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 积分活动参加用户(AppIntegralActivityUser)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-23 13:14:58
 */
@Data
@Accessors(chain = true)
@TableName("app_integral_activity_user")
@EqualsAndHashCode(callSuper = true)
public class AppIntegralActivityUserEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -55284794484993855L;
    /**
     * 活动ID
     */
    @TableField("activity_id")
    private Integer activityId;
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Integer userId;
    /**
     * 奖励积分
     */
    @TableField("integral")
    private Integer integral;
}

