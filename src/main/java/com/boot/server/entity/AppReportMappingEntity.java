package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 映射表(AppReportMapping)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-16 11:54:18
 */
@Data
@Accessors(chain = true)
@TableName("app_report_mapping")
@EqualsAndHashCode(callSuper = true)
public class AppReportMappingEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -84355431078697088L;
    /**
     * 商品ID
     */
    @TableField("goods_id")
    private Integer goodsId;
    /**
     * 商品ID
     */
    @TableField("specs_id")
    private Integer specsId;
    /**
     * 字段名
     */
    @TableField("report_name")
    private String reportName;
}

