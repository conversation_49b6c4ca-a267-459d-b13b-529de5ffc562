package com.boot.server.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.server.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 自动化报告规则(AppAutoReportRule)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-17 13:54:29
 */
@Data
@Accessors(chain = true)
@TableName("app_auto_report_rule")
@EqualsAndHashCode(callSuper = true)
public class AppAutoReportRuleEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 660515090292482622L;
    /**
     * 分组
     */
    @TableField("group_name")
    private String groupName;
    /**
     * 项目
     */
    @TableField("item")
    private String item;
    /**
     * 基因型
     */
    @TableField("result")
    private String result;
    /**
     * 解读
     */
    @TableField("interpretation")
    private String interpretation;
}

