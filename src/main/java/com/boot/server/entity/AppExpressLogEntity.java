package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 顺丰接口请求记录(AppExpressLog)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-11 15:45:09
 */
@Data
@Accessors(chain = true)
@TableName("app_express_log")
@EqualsAndHashCode(callSuper = true)
public class AppExpressLogEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -75045553058634849L;
    /**
     * 服务 CODE
     */
    @TableField("service_code")
    private String serviceCode;
    /**
     * 服务名称
     */
    @TableField("service_name")
    private String serviceName;
    /**
     * 请求参数
     */
    @TableField("request_body")
    private String requestBody;
    /**
     * 响应参数
     */
    @TableField("response_body")
    private String responseBody;
}

