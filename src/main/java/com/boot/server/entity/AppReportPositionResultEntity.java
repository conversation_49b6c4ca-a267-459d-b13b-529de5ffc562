package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 位点检测结果(AppReportPositionResult)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-22 10:37:11
 */
@Data
@Accessors(chain = true)
@TableName("app_report_position_result")
@EqualsAndHashCode(callSuper = true)
public class AppReportPositionResultEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 364499385655631259L;
    /**
     * 报告ID
     */
    @TableField("report_id")
    private Long reportId;
    /**
     * 点位
     */
    @TableField("position")
    private String position;
    /**
     * 结果
     */
    @TableField("result")
    private String result;
}

