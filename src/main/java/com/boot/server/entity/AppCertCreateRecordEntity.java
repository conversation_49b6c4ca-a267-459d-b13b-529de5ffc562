package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 证书创建记录表(AppCertCreateRecord)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-23 14:34:53
 */
@Data
@Accessors(chain = true)
@TableName("app_cert_create_record")
@EqualsAndHashCode(callSuper = true)
public class AppCertCreateRecordEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -25384176919283708L;
    /**
     * 证书存放路径
     */
    @TableField("images")
    private String images;
    /**
     * 宠物ID
     */
    @TableField("pet_id")
    private Long petId;
}

