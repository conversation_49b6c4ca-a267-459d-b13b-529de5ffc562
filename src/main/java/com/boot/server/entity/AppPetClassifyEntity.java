package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 宠物分类表(AppPetClassify)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-18 09:50:03
 */
@Data
@Accessors(chain = true)
@TableName("app_pet_classify")
@EqualsAndHashCode(callSuper = true)
public class AppPetClassifyEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 477035661745838125L;
    /**
     * 宠物分类名称
     */
    @TableField("title")
    private String title;
    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
    /**
     * 状态 0 不显示 1 显示
     */
    @TableField("status")
    private Integer status;
}

