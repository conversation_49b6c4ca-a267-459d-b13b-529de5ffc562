package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 品种套餐指标映射(AppReportCatClassifyMapping)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-21 00:30:54
 */
@Data
@Accessors(chain = true)
@TableName("app_report_cat_classify_mapping")
@EqualsAndHashCode(callSuper = true)
public class AppReportCatClassifyMappingEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 174441591661844525L;
    /**
     * 宠物分类ID
     */
    @TableField("classify_id")
    private Integer classifyId;
    /**
     * 宠物分类中文名
     */
    @TableField("classify_name")
    private String classifyName;
    /**
     * 指标名称
     */
    @TableField("report_name")
    private String reportName;
}

