package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 商品规格表(AppGoodsSpecs)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-04 20:26:02
 */
@Data
@Accessors(chain = true)
@TableName("app_goods_specs")
@EqualsAndHashCode(callSuper = true)
public class AppGoodsSpecsEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 325195731185936282L;
    /**
     * 商品表
     */
    @TableField("goods_id")
    private Integer goodsId;
    /**
     * 规格
     */
    @TableField("title")
    private String title;
    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
    /**
     * 状态 0 不显示 1 显示
     */
    @TableField("status")
    private Integer status;

    /**
     * 分类ID
     */
    @TableField("classify_id")
    private Long classifyId;
}

