package com.boot.server.entity;


import java.time.LocalDateTime;
import java.util.Date;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 积分明细记录(AppUserIntegralRecord)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-05 18:14:01
 */
@Data
@Accessors(chain = true)
@TableName("app_user_integral_record")
@EqualsAndHashCode(callSuper = true)
public class AppUserIntegralRecordEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -32397095605118526L;
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    /**
     * 积分值正数为+积分，负数为-积分
     */
    @TableField("value")
    private Long value;

    /**
     * 操作类型 1用户 2系统操作
     */
    @TableField("op_type")
    private Integer opType;
    /**
     * 积分过期时间
     */
    @TableField("expr_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime exprTime;
}

