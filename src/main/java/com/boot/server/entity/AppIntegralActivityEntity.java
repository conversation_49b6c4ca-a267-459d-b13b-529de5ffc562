package com.boot.server.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.server.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 积分活动(AppIntegralActivity)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-23 13:14:58
 */
@Data
@Accessors(chain = true)
@TableName("app_integral_activity")
@EqualsAndHashCode(callSuper = true)
public class AppIntegralActivityEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -53779808808915665L;
    /**
     * 兑换码
     */
    @TableField("activity_no")
    private String activityNo;
    /**
     * 奖励积分
     */
    @TableField("integral")
    private Integer integral;
    /**
     * 过期时间
     */
    @TableField("expr_time")
    private LocalDateTime exprTime;
    /**
     * 限制人数0不限制
     */
    @TableField("limit_num")
    private Integer limitNum;
    /**
     * 已使用次数
     */
    @TableField("used_count")
    private Integer usedCount;
}

