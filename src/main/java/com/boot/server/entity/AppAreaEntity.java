package com.boot.server.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.server.dto.TreeAreaResponse;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 省市县(AppArea)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-09 16:35:41
 */
@Data
@Accessors(chain = true)
@TableName("app_area")
public class AppAreaEntity implements Serializable {
    private static final long serialVersionUID = -85585499515244657L;
    /**
     * 名称
     */
    @TableId("id")
    private Integer id;
    /**
     * 名称
     */
    @TableField("name")
    private String name;
    /**
     * 父级id
     */
    @TableField("parent_id")
    private Integer parentId;
    /**
     * 父级名称
     */
    @TableField("parent_name")
    private String parentName;
    /**
     * 祖级列表
     */
    @TableField("ancestors")
    private String ancestors;
    /**
     * 排序值
     */
    @TableField("order_num")
    private String orderNum;

    public TreeAreaResponse toTreeArea() {
        TreeAreaResponse treeArea = new TreeAreaResponse();
        treeArea.setValue(this.id);
        treeArea.setText(this.name);
        return treeArea;
    }
}

