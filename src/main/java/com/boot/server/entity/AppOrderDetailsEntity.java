package com.boot.server.entity;


import java.math.BigDecimal;
import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 用户订单明细(AppOrderDetails)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Data
@Accessors(chain = true)
@TableName("app_order_details")
@EqualsAndHashCode(callSuper = true)
public class AppOrderDetailsEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -16612827778923691L;
    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;
    /**
     * 商品ID
     */
    @TableField("goods_id")
    private Long goodsId;
    /**
     * 商品规格ID
     */
    @TableField("goods_specs")
    private String goodsSpecs;
    /**
     * 商品规格中文
     */
    @TableField("goods_specs_name")
    private String goodsSpecsName;
    /**
     * 分类规格Map快照
     */
    @TableField("goods_specs_classify_map")
    private String goodsSpecsClassifyMap;
    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;
    /**
     * 原价金额
     */
    @TableField("source_amount")
    private BigDecimal sourceAmount;
    /**
     * 标题
     */
    @TableField("title")
    private String title;
    /**
     * 描述
     */
    @TableField("description")
    private String description;
    /**
     * 商品图片
     */
    @TableField("image")
    private String image;
    /**
     * 购买数量
     */
    @TableField("number")
    private Integer number;
    /**
     * 已使用 0 否 1是
     */
    @TableField("used")
    private Integer used;
    /**
     * 未使用数量
     */
    @TableField("stock")
    private Integer stock;

    @TableField(exist = false)
    private Integer goodsType;
}

