package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 试剂盒报告(AppPetReportDetail)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-14 16:41:58
 */
@Data
@Accessors(chain = true)
@TableName("app_pet_report_detail")
@EqualsAndHashCode(callSuper = true)
public class AppPetReportDetailEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 129870183513387248L;
    /**
     * 宠物ID
     */
    @TableField("pet_id")
    private Long petId;
    /**
     * 报告 ID
     */
    @TableField("report_id")
    private Long reportId;
    /**
     * 试剂盒编号
     */
    @TableField("kits_no")
    private String kitsNo;

    /**
     * 分组
     */
    @TableField("`group`")
    private String group;
    /**
     * 检测项目
     */
    @TableField("item")
    private String item;
    /**
     * 位点名称
     */
    @TableField("position")
    private String position;
    /**
     * 突变情况
     */
    @TableField("result")
    private String result;
    /**
     * 排序
     */
    @TableField("sort_no")
    private Integer sortNo;
    /**
     * 宠物类别
     */
    @TableField("coat")
    private String coat;
    /**
     * 解读结果
     */
    @TableField("result_interpretation")
    private String resultInterpretation;
}

