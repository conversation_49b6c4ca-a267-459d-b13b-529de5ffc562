package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 规格详情表(AppGoodsSpecsDetail)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-22 23:38:23
 */
@Data
@Accessors(chain = true)
@TableName("app_goods_specs_detail")
@EqualsAndHashCode(callSuper = true)
public class AppGoodsSpecsDetailEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -64689526754936282L;
    /**
     * 规格ID
     */
    @TableField("specs_id")
    private Integer specsId;
    /**
     * 分类
     */
    @TableField("classify_name")
    private String classifyName;
    /**
     * 项目
     */
    @TableField("item")
    private String item;
}

