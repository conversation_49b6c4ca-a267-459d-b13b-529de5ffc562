package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 管理用户表(AppAdminUser)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-17 11:24:33
 */
@Data
@Accessors(chain = true)
@TableName("app_admin_user")
@EqualsAndHashCode(callSuper = true)
public class AppAdminUserEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 851157932844925786L;
    /**
     * 头像
     */
    @TableField("avatar")
    private String avatar;
    /**
     * 昵称
     */
    @TableField("nick_name")
    private String nickName;
    /**
     * 用户名
     */
    @TableField("username")
    private String username;
    /**
     * 密码(md5)
     */
    @TableField("password")
    private String password;
}

