package com.boot.server.entity;


import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.time.LocalDate;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 用户订单表(AppOrders)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Data
@Accessors(chain = true)
@TableName("app_orders")
@EqualsAndHashCode(callSuper = true)
public class AppOrdersEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -37202835451433487L;
    /**
     * 订单编号
     */
    @TableField("order_no")
    private String orderNo;
    /**
     * 姓名
     */
    @TableField("name")
    private String name;
    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;
    /**
     * 省份
     */
    @TableField("province")
    private String province;
    /**
     * 市
     */
    @TableField("city")
    private String city;
    /**
     * 区
     */
    @TableField("district")
    private String district;
    /**
     * 详细地址
     */
    @TableField("address")
    private String address;
    /**
     * 订单原金额
     */
    @TableField("order_source_amount")
    private BigDecimal orderSourceAmount;
    /**
     * 订单金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;
    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;
    /**
     * 消耗积分
     */
    @TableField("integral")
    private Long integral;
    /**
     * 积分抵扣金额
     */
    @TableField("integral_amount")
    private BigDecimal integralAmount;
    /**
     * 支付状态0 待支付 1已支付 2已取消
     */
    @TableField("pay_status")
    private Integer payStatus;
    /**
     * 订单状态0 待支付 1已支付 2待收件 3部分使用 4已使用
     */
    @TableField("order_status")
    private Integer orderStatus;
    /**
     * 支付时间
     */
    @TableField("pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;
    /**
     * 取消类型 1: 用户取消 2: 系统取消
     */
    @TableField("cancel_type")
    private Integer cancelType;
    /**
     * 取消时间
     */
    @TableField("cancel_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelTime;

    /**
     * 物流单号
     */
    @TableField("logistics")
    private String logistics;
}

