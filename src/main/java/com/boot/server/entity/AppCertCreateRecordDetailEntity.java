package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 证书创建记录明细表(AppCertCreateRecordDetail)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-23 14:34:53
 */
@Data
@Accessors(chain = true)
@TableName("app_cert_create_record_detail")
@EqualsAndHashCode(callSuper = true)
public class AppCertCreateRecordDetailEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -46043772105592725L;
    /**
     * 记录ID
     */
    @TableField("record_id")
    private Long recordId;
    /**
     * 检测项目
     */
    @TableField("item")
    private String item;
    /**
     * 位点名称
     */
    @TableField("position")
    private String position;
    /**
     * 突变情况
     */
    @TableField("result")
    private String result;
}

