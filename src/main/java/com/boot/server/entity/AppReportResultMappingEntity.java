package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 报告结果映射表(AppReportResultMapping)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-28 12:35:02
 */
@Data
@Accessors(chain = true)
@TableName("app_report_result_mapping")
@EqualsAndHashCode(callSuper = true)
public class AppReportResultMappingEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 899458950418687793L;
    /**
     * 同步结果
     */
    @TableField("source_result")
    private String sourceResult;
    /**
     * 位点
     */
    @TableField("position")
    private String position;
    /**
     * 映射结果
     */
    @TableField("mapping_result")
    private String mappingResult;
}

