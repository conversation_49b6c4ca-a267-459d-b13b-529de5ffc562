package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 商品规格分类表(AppGoodsSpecsClassify)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-18 09:50:02
 */
@Data
@Accessors(chain = true)
@TableName("app_goods_specs_classify")
@EqualsAndHashCode(callSuper = true)
public class AppGoodsSpecsClassifyEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -39727943978943935L;
    /**
     * 标题
     */
    @TableField("title")
    private String title;
    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
    /**
     * 状态 0 不显示 1 显示
     */
    @TableField("status")
    private Integer status;
}

