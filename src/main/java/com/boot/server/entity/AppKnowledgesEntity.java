package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 知识列表(AppKnowledges)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-04 17:24:51
 */
@Data
@Accessors(chain = true)
@TableName("app_knowledges")
@EqualsAndHashCode(callSuper = true)
public class AppKnowledgesEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 812450531545530300L;
    /**
     * 标题
     */
    @TableField("title")
    private String title;
    /**
     * 描述
     */
    @TableField("`desc`")
    private String desc;
    /**
     * 跳转链接
     */
    @TableField("link")
    private String link;
    /**
     * 0 公众号
     */
    @TableField("link_type")
    private Integer linkType;
    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
    /**
     * 状态 0 不显示 1 显示
     */
    @TableField("status")
    private Integer status;
}

