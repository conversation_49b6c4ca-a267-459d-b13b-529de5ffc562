package com.boot.server.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.server.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 线下渠道订单表(AppOfflineOrder)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-11 11:10:23
 */
@Data
@Accessors(chain = true)
@TableName("app_offline_order")
@EqualsAndHashCode(callSuper = true)
public class AppOfflineOrderEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 572512754808455245L;
    /**
     * 试剂盒编号
     */
    @TableField("kits_no")
    @NotBlank(message = "试剂盒编号不能为空")
    private String kitsNo;
    /**
     * 对应商品ID
     */
    @TableField("goods_id")
    @NotNull(message = "商品ID不能为空")
    private Long goodsId;
    /**
     * 是否使用
     */
    @TableField("status")
    private Integer status;

    /**
     * 商品规格
     */
    @TableField("goods_specs")
    private String goodsSpecs;

    /**
     * 商品规格
     */
    @TableField("goods_specs_name")
    private String goodsSpecsName;

    /**
     * 商品名称
     */
    @TableField(exist = false)
    private String goodsName;

}

