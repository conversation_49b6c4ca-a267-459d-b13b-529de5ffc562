package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 轮播表(AppBanners)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-17 13:55:28
 */
@Data
@Accessors(chain = true)
@TableName("app_banners")
@EqualsAndHashCode(callSuper = true)
public class AppBannersEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 114900665699454179L;
    /**
     * 图片
     */
    @TableField("banner")
    private String banner;
    /**
     * 跳转链接
     */
    @TableField("link")
    private String link;
    /**
     * 轮播类型1 banner图 2 广告位
     */
    @TableField("type")
    private Integer type;
    /**
     * 0 公众号 1 page页 2tab页
     */
    @TableField("link_type")
    private Integer linkType;
    /**
     * 是否启用
     */
    @TableField("status")
    private Integer status;
    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;
}

