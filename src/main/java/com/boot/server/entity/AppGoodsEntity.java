package com.boot.server.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boot.server.common.entity.BaseEntity;
import com.boot.server.dto.GoodsResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品表(AppGoods)表实体类
 *
 * <AUTHOR>
 * @since 2025-05-04 18:39:59
 */
@Data
@Accessors(chain = true)
@TableName("app_goods")
@EqualsAndHashCode(callSuper = true)
public class AppGoodsEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -62141201240297298L;
    /**
     * 标题
     */
    @TableField("title")
    private String title;
    /**
     * 描述
     */
    @TableField("description")
    private String description;
    /**
     * 商品图片
     */
    @TableField("image")
    private String image;
    /**
     * 详情图片
     */
    @TableField("w_image")
    private String wImage;
    /**
     * 宽图
     */
    @TableField("detail_image")
    private String detailImage;
    /**
     * 原价
     */
    @TableField("source_amount")
    private BigDecimal sourceAmount;
    /**
     * 现价
     */
    @TableField("amount")
    private BigDecimal amount;
    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 可选规格数量
     */
    @TableField("select_specs")
    private Integer selectSpecs;

    /**
     * 首页展示
     */
    @TableField("index_show")
    private Integer indexShow;
    /**
     * 状态 0 不显示 1 显示
     */
    @TableField("status")
    private Integer status;
    /**
     * 回寄用户名
     */
    @TableField("contact")
    private String contact;
    /**
     * 回寄用户手机号
     */
    @TableField("tel")
    private String tel;
    /**
     * 回寄详细地址
     */
    @TableField("address")
    private String address;
    /**
     * 商品套餐类型 0 普通 1 随心选 2品种套餐
     */
    @TableField("goods_type")
    private Integer goodsType;
    /**
     * 商品套餐类型为1 随心选的时候每多选择一项的价格
     */
    @TableField("incr_price")
    private BigDecimal incrPrice;
    /**
     * 商品套餐类型为1 随心选的时候每多选择一项的原价格
     */
    @TableField("source_incr_price")
    private BigDecimal sourceIncrPrice;

    /**
     * 商品规格
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private LinkedHashMap<String, List<AppGoodsSpecsEntity>> specs;

    public GoodsResponse toResponse(Integer number) {
        GoodsResponse goodsResponse = new GoodsResponse();
        goodsResponse.setId(getId());
        goodsResponse.setTitle(title);
        goodsResponse.setDescription(description);
        goodsResponse.setImage(image);
        goodsResponse.setAmount(this.amount);
        goodsResponse.setSourceAmount(this.sourceAmount);
        goodsResponse.setNumber(number);
        return goodsResponse;
    }
}

