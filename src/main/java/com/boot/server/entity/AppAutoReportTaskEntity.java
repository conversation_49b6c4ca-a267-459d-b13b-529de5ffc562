package com.boot.server.entity;


import java.util.Date;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.boot.server.common.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

/**
 * 自动化报告任务(AppAutoReportTask)表实体类
 *
 * <AUTHOR>
 * @since 2025-07-18 10:59:50
 */
@Data
@Accessors(chain = true)
@TableName("app_auto_report_task")
@EqualsAndHashCode(callSuper = true)
public class AppAutoReportTaskEntity extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -43063312956068936L;
    /**
     * 试剂盒编号/检测编号
     */
    @TableField("kits_no")
    private String kitsNo;
    /**
     * 机构名称
     */
    @TableField("organization")
    private String organization;
    /**
     * 宠物编号
     */
    @TableField("pet_no")
    private String petNo;
    /**
     * 宠物性别
     */
    @TableField("pet_sex")
    private String petSex;
    /**
     * 宠物品种
     */
    @TableField("pet_breed")
    private String petBreed;
    /**
     * 报告JSON
     */
    @TableField("report_json")
    private String reportJson;
    /**
     * 任务状态 0 未处理 1处理中 2处理完成 3 处理失败
     */
    @TableField("status")
    private Integer status;
}

