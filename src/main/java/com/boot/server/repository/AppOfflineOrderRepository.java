package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.dto.request.AdminOfflineRequest;
import com.boot.server.entity.AppOfflineOrderEntity;
import com.boot.server.mapper.AppOfflineOrderMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 线下渠道订单表(AppOfflineOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-11 11:10:23
 */
@Service
public class AppOfflineOrderRepository extends ServiceImpl<AppOfflineOrderMapper, AppOfflineOrderEntity> {

    public Page<AppOfflineOrderEntity> selectOfflineOrderPage(AdminOfflineRequest request) {
        Page<AppOfflineOrderEntity> page = request.getPage();
        return this.baseMapper.selectOfflineOrderPage(page, request);
    }


    public AppOfflineOrderEntity getByKitsNo(String kitsNo) {
        return this.getOne(
                Wrappers.<AppOfflineOrderEntity>lambdaQuery()
                        .eq(AppOfflineOrderEntity::getKitsNo, kitsNo)
        );
    }

    public List<AppOfflineOrderEntity> listByKitsNo(List<String> offlineKitsNos) {
        return this.list(
                Wrappers.<AppOfflineOrderEntity>lambdaQuery()
                        .in(AppOfflineOrderEntity::getKitsNo, offlineKitsNos)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateStatus(final String kitsNo, final Integer status) {
        return this.update(
                Wrappers.<AppOfflineOrderEntity>lambdaUpdate()
                        .set(AppOfflineOrderEntity::getStatus, status)
                        .eq(AppOfflineOrderEntity::getKitsNo, kitsNo)
        );
    }
}

