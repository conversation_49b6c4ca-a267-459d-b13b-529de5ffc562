package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.entity.AppReportMappingEntity;
import com.boot.server.mapper.AppReportMappingMapper;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 映射表(AppReportMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-16 11:54:19
 */
@Service
public class AppReportMappingRepository extends ServiceImpl<AppReportMappingMapper, AppReportMappingEntity> {

    public List<AppReportMappingEntity> findByGoodsId(Long goodsId) {
        return this.list(
                Wrappers.<AppReportMappingEntity>lambdaQuery()
                        .eq(AppReportMappingEntity::getGoodsId, goodsId)
        );
    }


    public List<AppReportMappingEntity> selectByByIds(Collection<Long> goodsIds) {
        return this.list(
                Wrappers.<AppReportMappingEntity>lambdaQuery()
                        .in(AppReportMappingEntity::getGoodsId, goodsIds)
        );
    }
}

