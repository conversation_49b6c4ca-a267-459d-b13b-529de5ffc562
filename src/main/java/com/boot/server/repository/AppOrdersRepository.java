package com.boot.server.repository;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.util.BeanUtil;
import com.boot.server.dto.AdminOrderListResponse;
import com.boot.server.dto.request.AdminOrderListRequest;
import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppOrdersEntity;
import com.boot.server.enums.OrderStatusEnum;
import com.boot.server.enums.PayStatusEnum;
import com.boot.server.mapper.AppOrdersMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 用户订单表(AppOrders)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Service
public class AppOrdersRepository extends ServiceImpl<AppOrdersMapper, AppOrdersEntity> {

    public AppOrdersEntity getByOrderNo(String orderNo) {
        return this.getOne(
                Wrappers.<AppOrdersEntity>lambdaQuery()
                        .eq(AppOrdersEntity::getOrderNo, orderNo)
                        .last("for update")
        );
    }

    public void wxPaySuccess(String orderNo) {
        AppOrdersEntity appOrders = getByOrderNo(orderNo);
        if (Objects.equals(appOrders.getPayStatus(), PayStatusEnum.TO_PAY.getCode())) {
            appOrders.setPayStatus(PayStatusEnum.SUCCESS.getCode());
            appOrders.setOrderStatus(OrderStatusEnum.SUCCESS.getCode());
            this.updateById(appOrders);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateOrderStatus(String orderNo, OrderStatusEnum orderStatus) {
        AppOrdersEntity appOrders = getByOrderNo(orderNo);
        appOrders.setOrderStatus(orderStatus.getCode());
        BusinessException.throwIf(!this.updateById(appOrders), "更新订单状态失败");
    }

    public Page<AdminOrderListResponse> selectOrderList(AdminOrderListRequest request) {
        Page<AppOrdersEntity> page = request.getPage();
        Page<AppOrdersEntity> result = this.page(
                page,
                Wrappers.<AppOrdersEntity>lambdaQuery()
                        .eq(StrUtil.isNotBlank(request.getOrderNo()), AppOrdersEntity::getOrderNo, request.getOrderNo())
                        .like(StrUtil.isNotBlank(request.getName()), AppOrdersEntity::getName, request.getName())
                        .eq(Objects.nonNull(request.getOrderStatus()), AppOrdersEntity::getOrderStatus, request.getOrderStatus())
                        .eq(Objects.nonNull(request.getPayStatus()), AppOrdersEntity::getPayStatus, request.getPayStatus())
                        .eq(StrUtil.isNotBlank(request.getLogistics()), AppOrdersEntity::getLogistics, request.getLogistics())
                        .eq(Objects.nonNull(request.getUserId()), AppOrdersEntity::getCreateUser, request.getUserId())
                        .orderByDesc(AppOrdersEntity::getCreateTime)

        );
        Page<AdminOrderListResponse> adminOrderListResponsePage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<AppOrdersEntity> entityList = result.getRecords();
        List<AdminOrderListResponse> appOrdersEntityList = BeanUtil.copyToList(entityList, AdminOrderListResponse.class);
        adminOrderListResponsePage.setRecords(appOrdersEntityList);
        return adminOrderListResponsePage;
    }

    /**
     * 用户是否已经下过单（未支付也算）
     */
    public boolean hasFirstOrder(Long userId) {
        return this.count(Wrappers.<AppOrdersEntity>lambdaQuery()
                .eq(AppOrdersEntity::getCreateUser, userId)
                .in(AppOrdersEntity::getPayStatus, PayStatusEnum.SUCCESS.getCode())
        ) <= 0;
    }
}

