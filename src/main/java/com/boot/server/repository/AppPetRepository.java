package com.boot.server.repository;


import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.dto.PageRequest;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.dto.request.AdminPetListRequest;
import com.boot.server.entity.AppPetBindOrderEntity;
import com.boot.server.entity.AppPetEntity;
import com.boot.server.enums.DetectStatusEnum;
import com.boot.server.mapper.AppPetMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 爱宠车(AppPet)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 13:53:35
 */
@Service
@RequiredArgsConstructor
public class AppPetRepository extends ServiceImpl<AppPetMapper, AppPetEntity> {
    @Lazy
    @Resource
    private AppPetBindOrderRepository appPetBindOrderRepository;

    @Lazy
    @Resource
    private AppPetReportRepository appPetReportRepository;

    public Page<AppPetEntity> selectUserPet(PageRequest pageRequest) {
        Page<AppPetEntity> page = pageRequest.getPage();
        return this.baseMapper.selectPage(
                page,
                Wrappers.<AppPetEntity>lambdaQuery()
                        .eq(AppPetEntity::getCreateUser, AuthUtil.getCurrentUserId())
                        .orderByDesc(AppPetEntity::getCreateTime)
        );
    }

    public AppPetEntity selectById(Long id) {
        return this.getById(id);
    }


    public AppPetEntity selectDetailById(Long id) {
        AppPetEntity appPet = this.getById(id);
        Boolean hasReport = appPetReportRepository.hasReportByPetId(appPet.getId());
        appPet.setHasReport(BooleanUtil.toInteger(hasReport));
        // 查询最近一次的顺丰快递号
        List<AppPetBindOrderEntity> list = appPetBindOrderRepository.list(
                Wrappers.<AppPetBindOrderEntity>lambdaQuery()
                        .eq(AppPetBindOrderEntity::getPetId, appPet.getId())
                        .eq(AppPetBindOrderEntity::getStatus, DetectStatusEnum.HANDLE.getCode())
                        .orderByDesc(AppPetBindOrderEntity::getId)
        );
        if (CollectionUtils.isNotEmpty(list)) {
            List<String> collect = list.stream().map(AppPetBindOrderEntity::getLogistics).collect(Collectors.toList());
            appPet.setLogistics(collect);
        }
        return appPet;
    }

    /**
     * 查询未检测的宠物列表
     *
     * @return 宠物列表
     */
    public List<AppPetEntity> selectNoneDetectCat() {
        return this.baseMapper.selectList(
                Wrappers.<AppPetEntity>lambdaQuery()
                        .eq(AppPetEntity::getCreateUser, AuthUtil.getCurrentUserId())
                        .orderByDesc(AppPetEntity::getCreateTime)
        );
    }

    /**
     * 待检测状态到待已绑定
     *
     * @param id 宠物主键
     * @return 更新结果
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatusToBind(final Long id) {
        AppPetEntity appPet = this.getById(id);
        appPet.setDetectStatus(DetectStatusEnum.BIND.getCode());
        return this.updateById(appPet);
    }


    /**
     * 绑定状态到待收货
     *
     * @param id 宠物主键
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusToHandle(final Long id) {
        AppPetEntity appPet = this.getById(id);
        appPet.setDetectStatus(DetectStatusEnum.HANDLE.getCode());
        this.updateById(appPet);
    }

    /**
     * 检测机构收货成功待检测
     *
     * @param id 宠物主键
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusToProcess(final Long id) {
        AppPetEntity appPet = this.getById(id);
        appPet.setDetectStatus(DetectStatusEnum.PROCESS.getCode());
        this.updateById(appPet);
    }

    /**
     * 检测完成
     *
     * @param id 宠物主键
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusToDone(final Long id) {
        AppPetEntity appPet = this.getById(id);
        appPet.setDetectStatus(DetectStatusEnum.DONE.getCode());
        this.updateById(appPet);
    }

    public List<AppPetEntity> selectListByIds(List<Long> petIds) {
        return this.listByIds(petIds);
    }

    /**
     * 管理员查询宠物列表
     *
     * @param adminPetListRequest 查询条件
     * @return 宠物列表
     */
    public Page<AppPetEntity> selectAdminPetPage(AdminPetListRequest adminPetListRequest) {
        Page<AppPetEntity> page = adminPetListRequest.getPage();
        return this.baseMapper.selectPage(
                page,
                Wrappers.<AppPetEntity>lambdaQuery()
                        .eq(StrUtil.isNotBlank(adminPetListRequest.getPetName()), AppPetEntity::getPetName, adminPetListRequest.getPetName())
                        .eq(StrUtil.isNotBlank(adminPetListRequest.getPetNo()), AppPetEntity::getPetNo, adminPetListRequest.getPetNo())
                        .eq(Objects.nonNull(adminPetListRequest.getUserId()), AppPetEntity::getCreateUser, adminPetListRequest.getUserId())
                        .orderByDesc(AppPetEntity::getCreateTime)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean addAppPet(AppPetEntity appPetEntity) {
        this.save(appPetEntity);
        appPetEntity.setPetNo(String.format("SZ%05d", appPetEntity.getId()));
        return this.updateById(appPetEntity);
    }
}

