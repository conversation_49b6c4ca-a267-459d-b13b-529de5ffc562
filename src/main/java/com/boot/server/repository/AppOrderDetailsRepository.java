package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.dto.PageRequest;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.dto.AdminBinderListResponse;
import com.boot.server.dto.request.AdminBinderListRequest;
import com.boot.server.entity.AppOrderDetailsEntity;
import com.boot.server.enums.OrderStatusEnum;
import com.boot.server.mapper.AppOrderDetailsMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 用户订单明细(AppOrderDetails)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Service
@RequiredArgsConstructor
public class AppOrderDetailsRepository extends ServiceImpl<AppOrderDetailsMapper, AppOrderDetailsEntity> {
    private final AppOrdersRepository appOrdersRepository;

    public List<AppOrderDetailsEntity> listByOrderNo(String orderNo) {
        return this.list(
                Wrappers.<AppOrderDetailsEntity>lambdaQuery()
                        .eq(AppOrderDetailsEntity::getOrderNo, orderNo)
        );
    }


    public String getGoodsName(final String orderNo) {
        List<AppOrderDetailsEntity> entityList = listByOrderNo(orderNo);
        AppOrderDetailsEntity details = entityList.get(0);
        if (entityList.size() > 1) {
            return details.getTitle() + "等套餐";
        }
        return details.getTitle();
    }

    public Map<String, List<AppOrderDetailsEntity>> listByOrderNos(List<String> orderNos) {
        List<AppOrderDetailsEntity> entityList = this.list(
                Wrappers.<AppOrderDetailsEntity>lambdaQuery()
                        .in(AppOrderDetailsEntity::getOrderNo, orderNos)
        );
        if (CollectionUtils.isNotEmpty(entityList)) {
            return entityList.stream().collect(Collectors.groupingBy(AppOrderDetailsEntity::getOrderNo));
        }
        return Collections.emptyMap();
    }

    public List<AppOrderDetailsEntity> selectListByOrderNo(String orderNo) {
        return baseMapper.selectListByOrderNo(orderNo);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStock(final Long detailId) {
        AppOrderDetailsEntity orderDetails = this.getById(detailId);
        Integer stock = orderDetails.getStock();
        BusinessException.throwIf(stock < 1, "无法使用此商品");
        Integer stockVal = stock - 1;
        orderDetails.setStock(stockVal);
        if (Objects.equals(stockVal, 0)) {
            orderDetails.setUsed(1);
        }
        // 更新订单状态 已使用/部分使用
        this.updateById(orderDetails);
        List<AppOrderDetailsEntity> orderDetailsEntityList = selectListByOrderNo(orderDetails.getOrderNo());
        boolean allMatch = orderDetailsEntityList.stream().allMatch(row -> Objects.equals(row.getUsed(), 1));
        if (allMatch) {
            appOrdersRepository.updateOrderStatus(orderDetails.getOrderNo(), OrderStatusEnum.USED);
        } else {
            appOrdersRepository.updateOrderStatus(orderDetails.getOrderNo(), OrderStatusEnum.PART_USED);
        }
        return true;
    }

    public Page<AdminBinderListResponse> selectOrderBinderList(AdminBinderListRequest request) {
        Page<AdminBinderListResponse> page = request.getPage();
        return this.baseMapper.selectOrderBinderList(page,request);
    }
}

