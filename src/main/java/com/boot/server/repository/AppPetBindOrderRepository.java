package com.boot.server.repository;


import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.dto.request.UpdateVarietiesRequest;
import com.boot.server.entity.AppPetBindOrderEntity;
import com.boot.server.enums.PetBindOrderStatusEnum;
import com.boot.server.mapper.AppPetBindOrderMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 订单宠物绑定记录(AppPetBindOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-11 22:29:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppPetBindOrderRepository extends ServiceImpl<AppPetBindOrderMapper, AppPetBindOrderEntity> {

    private final AppNotifyRoutersLogRepository appNotifyRoutersLogRepository;
    private final AppPetRepository appPetRepository;

    @Transactional(rollbackFor = Exception.class)
    public void handling(JSONObject jsonObject, boolean isSaveDb) {
        JSONArray jsonArray = jsonObject.getJSONObject("Body").getJSONArray("WaybillRoute");
        log.info("jsonArray:{}", jsonArray.toJSONString());
        JSONObject router = jsonArray.getJSONObject(0);
        // 运单号
        String logistics = router.getString("mailno");
        if (isSaveDb) {
            appNotifyRoutersLogRepository.save(logistics, jsonObject.toJSONString());
        }
        log.info("mailno:{}", logistics);
        String opCode = router.getString("opCode");
        log.info("opCode:{} {}", opCode, Objects.equals(opCode, "80"));
        // 80 代表已签收
        // opCode编号说明: https://open.sf-express.com/developSupport/734349?activeIndex=589678
        if (Objects.equals(opCode, "80")) {
            // 绑定记录状态变更为已收件,宠物状态变更为 检测中
            AppPetBindOrderEntity byLogistics = getByLogistics(logistics);
            if (Objects.isNull(byLogistics) || Objects.equals(byLogistics.getStatus(), PetBindOrderStatusEnum.DONE.getCode())) {
                log.warn("为找到对应绑定信息或已处理: {} {}", logistics, byLogistics.toString());
                return;
            }
            // 已取件
            byLogistics.setStatus(PetBindOrderStatusEnum.DONE.getCode());
            updateById(byLogistics);
            // 设置宠物状态
            appPetRepository.updateStatusToProcess(byLogistics.getPetId());
        }

    }

    public AppPetBindOrderEntity getByLogistics(String logistics) {
        return this.getOne(
                Wrappers.<AppPetBindOrderEntity>lambdaQuery()
                        .eq(AppPetBindOrderEntity::getLogistics, logistics)
        );
    }

    public AppPetBindOrderEntity getByKitsNo(String kitsNo) {
        return this.getOne(
                Wrappers.<AppPetBindOrderEntity>lambdaQuery()
                        .eq(AppPetBindOrderEntity::getKitsNo, kitsNo)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateVarieties(UpdateVarietiesRequest request) {
        return this.update(
                Wrappers.<AppPetBindOrderEntity>lambdaUpdate()
                        .eq(AppPetBindOrderEntity::getId, request.getId())
                        .set(AppPetBindOrderEntity::getVarieties, request.getVarieties())
        );
    }
}

