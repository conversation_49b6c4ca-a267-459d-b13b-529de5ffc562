package com.boot.server.repository;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.entity.AppUserIntegralRecordEntity;
import com.boot.server.mapper.AppUserIntegralRecordMapper;
import com.boot.server.mapper.AppUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 积分明细记录(AppUserIntegralRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-05 18:14:01
 */
@Service
@RequiredArgsConstructor
public class AppUserIntegralRecordRepository extends ServiceImpl<AppUserIntegralRecordMapper, AppUserIntegralRecordEntity> {
    private final AppUserMapper appUserMapper;

    @Transactional(rollbackFor = Exception.class)
    public void addIntegralRecord(final Long userId, final Long integral, final String remark, final Integer opType, LocalDateTime exprTime) {
        AppUserIntegralRecordEntity record = new AppUserIntegralRecordEntity();
        record.setUserId(userId);
        record.setValue(integral);
        record.setExprTime(exprTime);
        record.setOpType(opType);
        record.setRemark(remark);
        this.save(record);
        // 更新用户积分
        int updated = appUserMapper.updateIntegral(userId, integral);
        if (updated <= 0) {
            throw new BusinessException("更新用户积分失败，用户ID：" + userId + "，积分：" + integral);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void addIntegralRecord(final Long userId, final Long integral, final String remark) {
        this.addIntegralRecord(userId, integral, remark, 1, null);
    }


    @Transactional(rollbackFor = Exception.class)
    public void addExprTimeIntegralRecord(final Long userId, final Long integral, final String remark) {
        LocalDateTime exprTime = LocalDateTime.now().plusDays(30);
        this.addIntegralRecord(userId, integral, remark, 1, exprTime);
    }
}

