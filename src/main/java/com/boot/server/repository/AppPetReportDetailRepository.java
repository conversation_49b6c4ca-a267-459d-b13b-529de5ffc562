package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.entity.AppPetReportDetailEntity;
import com.boot.server.mapper.AppPetReportDetailMapper;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 试剂盒报告(AppPetReportDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14 16:41:58
 */
@Service
public class AppPetReportDetailRepository extends ServiceImpl<AppPetReportDetailMapper, AppPetReportDetailEntity> {

    public List<AppPetReportDetailEntity> listByReportIds(Collection<Long> reportIds) {
        return super.list(
                Wrappers.<AppPetReportDetailEntity>lambdaQuery()
                        .in(AppPetReportDetailEntity::getReportId, reportIds)
                        .orderByAsc(AppPetReportDetailEntity::getCreateTime)
        );
    }

    public List<AppPetReportDetailEntity> listByReportId(Long reportId) {
        return super.list(
                Wrappers.<AppPetReportDetailEntity>lambdaQuery()
                        .eq(AppPetReportDetailEntity::getReportId, reportId)
        );
    }


    public List<AppPetReportDetailEntity> selectListByPetId(final Long petId) {
        return baseMapper.selectListByPetId(petId);
    }
}

