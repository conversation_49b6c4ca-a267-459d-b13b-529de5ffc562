package com.boot.server.repository;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.dto.TreeAreaResponse;
import com.boot.server.entity.AppAreaEntity;
import com.boot.server.mapper.AppAreaMapper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 省市县(AppArea)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 16:35:42
 */
@Service
public class AppAreaRepository extends ServiceImpl<AppAreaMapper, AppAreaEntity> {
    private static List<AppAreaEntity> areaList = Collections.emptyList();

    public List<TreeAreaResponse> selectAreaTreeList() {
        // 获取所有地区数据
        if (areaList.isEmpty()) {
            areaList = this.list();
        }
        // 按父ID分组，方便后续查找子节点
        Map<Integer, List<AppAreaEntity>> areaMap = areaList.stream().collect(Collectors.groupingBy(AppAreaEntity::getParentId));
        // 获取顶级地区（parentId=0的地区）
        List<AppAreaEntity> pArea = areaList.stream()
                .filter(area -> Objects.equals(area.getParentId(), 0))
                .collect(Collectors.toList());
        // 构建树形结构，初始祖先集合为空
        return buildTree(pArea, areaMap, new HashSet<>());
    }


    /**
     * 构建树形结构
     *
     * @param areaList  当前层级的地区列表
     * @param areaMap   按父ID分组的地区映射
     * @param ancestors 当前分支的祖先ID集合，用于防止循环引用
     * @return 树形结构列表
     */
    public List<TreeAreaResponse> buildTree(List<AppAreaEntity> areaList, Map<Integer, List<AppAreaEntity>> areaMap, Set<Integer> ancestors) {
        return areaList.stream()
                .filter(Objects::nonNull) // 过滤空值
                .map(row -> createTreeAreaResponse(row, areaMap, ancestors))
                .collect(Collectors.toList());
    }

    /**
     * 创建树形节点
     *
     * @param row       当前地区节点
     * @param areaMap   按父ID分组的地区映射
     * @param ancestors 当前分支的祖先ID集合
     * @return 树形节点
     */
    private TreeAreaResponse createTreeAreaResponse(AppAreaEntity row, Map<Integer, List<AppAreaEntity>> areaMap, Set<Integer> ancestors) {
        // 将地区对象转换为树形节点
        TreeAreaResponse TreeAreaResponse = row.toTreeArea();

        // 创建新的祖先集合，包含当前节点ID
        Set<Integer> currentAncestors = new HashSet<>(ancestors);
        currentAncestors.add(row.getId());

        // 获取当前节点的子节点
        List<AppAreaEntity> children = areaMap.get(row.getId());
        if (children != null && !children.isEmpty()) {
            // 过滤掉会导致循环引用的子节点（即子节点ID在祖先集合中已存在）
            List<AppAreaEntity> safeChildren = children.stream()
                    .filter(child -> !ancestors.contains(child.getId()))
                    .collect(Collectors.toList());

            // 如果有安全的子节点，则递归构建子树
            if (!safeChildren.isEmpty()) {
                List<TreeAreaResponse> TreeAreaResponses = buildTree(safeChildren, areaMap, currentAncestors);
                TreeAreaResponse.setChildren(TreeAreaResponses);
            }
        }
        return TreeAreaResponse;
    }
}

