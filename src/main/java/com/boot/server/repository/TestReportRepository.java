package com.boot.server.repository;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.boot.server.dto.QueryReportResponse;
import com.boot.server.mapper.TestReportMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * TestReportRepository
 *
 * <AUTHOR> 2025/7/18 17:37
 */
@Component
@RequiredArgsConstructor
public class TestReportRepository {
    private final TestReportMapper testReportMapper;

    /**
     * 根据订单号查询检测报告
     * @param orderNo 订单号
     * @return 检测报告数据
     */
    public QueryReportResponse.ReportDataResponse getReport(String orderNo) {
        return testReportMapper.selectByOrderId(orderNo);
    }

}
