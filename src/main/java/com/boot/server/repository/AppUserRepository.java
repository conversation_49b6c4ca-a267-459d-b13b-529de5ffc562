package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.entity.AppUserEntity;
import com.boot.server.mapper.AppUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户表(AppUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-02 22:12:30
 */
@Service
public class AppUserRepository extends ServiceImpl<AppUserMapper, AppUserEntity> {

    public String getCurrentUserOpenId() {
        AppUserEntity appUser = this.getById(AuthUtil.getCurrentUserId());
        return appUser.getOpenId();
    }

    public boolean isFirstShop(final Long userId) {
        AppUserEntity appUser = this.getById(userId);
        return appUser.getFirstShop() == 1;
    }

    /**
     * 标记用户首次消费 赠送积分使用
     *
     * @param userId 用户ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFirstShop(final Long userId) {
        this.update(
                Wrappers.<AppUserEntity>lambdaUpdate()
                        .set(AppUserEntity::getFirstShop, 1)
                        .eq(AppUserEntity::getId, userId)
        );
    }

    /**
     * 按照分享码获取分享的用户信息
     * @param shareCode 分享码
     * @return 用户信息
     */
    public AppUserEntity getByShareCode(String shareCode) {
        return this.getOne(
                Wrappers.<AppUserEntity>lambdaQuery()
                        .eq(AppUserEntity::getShareUid, shareCode)
        );
    }

    /**
     * 按照邀请人分享码获取用户信息
     * @param inviteShareCode 邀请人分享码
     * @return 用户信息
     */
    public AppUserEntity getByInviteShareCode(String inviteShareCode) {
        return this.getOne(
                Wrappers.<AppUserEntity>lambdaQuery()
                        .eq(AppUserEntity::getInviteShareCode, inviteShareCode)
        );
    }

    /**
     * 统计通过某个分享码注册的用户数量
     *
     * @param shareCode 分享码
     * @return 邀请用户数量
     */
    public long countByInviteShareCode(String shareCode) {
        return this.count(Wrappers.<AppUserEntity>lambdaQuery()
                .eq(AppUserEntity::getInviteShareCode, shareCode));
    }
}

