package com.boot.server.repository;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.entity.AppNotifyRoutersLogEntity;
import com.boot.server.mapper.AppNotifyRoutersLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 顺丰物流路由通知记录表(AppNotifyRoutersLog)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-13 16:03:27
 */
@Service
public class AppNotifyRoutersLogRepository extends ServiceImpl<AppNotifyRoutersLogMapper, AppNotifyRoutersLogEntity> {


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void save(final String logistics, final String body) {
        AppNotifyRoutersLogEntity routersLog = new AppNotifyRoutersLogEntity();
        routersLog.setLogistics(logistics);
        routersLog.setBody(body);
        this.save(routersLog);
    }
}

