package com.boot.server.repository;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.util.BeanUtil;
import com.boot.server.dto.AdminIntegralActivityResponse;
import com.boot.server.dto.request.AdminIntegralActivityRequest;
import com.boot.server.entity.AppIntegralActivityEntity;
import com.boot.server.mapper.AppIntegralActivityMapper;
import org.springframework.stereotype.Service;

/**
 * 积分活动(AppIntegralActivity)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-23 13:14:58
 */
@Service
public class AppIntegralActivityRepository extends ServiceImpl<AppIntegralActivityMapper, AppIntegralActivityEntity> {

    /**
     * 分页查询积分活动列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    public Page<AdminIntegralActivityResponse> selectAdminPage(AdminIntegralActivityRequest request) {
        Page<AppIntegralActivityEntity> page = request.getPage();
        LambdaQueryWrapper<AppIntegralActivityEntity> wrapper = Wrappers.<AppIntegralActivityEntity>lambdaQuery()
                .like(StrUtil.isNotBlank(request.getRemark()), AppIntegralActivityEntity::getRemark, request.getRemark())
                .eq(request.getIntegral() != null, AppIntegralActivityEntity::getIntegral, request.getIntegral())
                .like(StrUtil.isNotBlank(request.getActivityNo()), AppIntegralActivityEntity::getActivityNo, request.getActivityNo())
                .eq(request.getLimitNum() != null, AppIntegralActivityEntity::getLimitNum, request.getLimitNum())
                .ge(request.getExprTimeStart() != null, AppIntegralActivityEntity::getExprTime, request.getExprTimeStart())
                .le(request.getExprTimeEnd() != null, AppIntegralActivityEntity::getExprTime, request.getExprTimeEnd())
                .orderByDesc(AppIntegralActivityEntity::getId);
        Page<AppIntegralActivityEntity> entityPage = this.page(page, wrapper);
        // 转换为响应对象
        Page<AdminIntegralActivityResponse> responsePage = new Page<>(entityPage.getCurrent(), entityPage.getSize(), entityPage.getTotal());
        responsePage.setRecords(BeanUtil.copyToList(entityPage.getRecords(), AdminIntegralActivityResponse.class));
        return responsePage;
    }

    /**
     * 检查兑换码是否已存在
     *
     * @param activityNo 兑换码
     * @return 是否存在
     */
    public boolean existsByActivityNo(String activityNo) {
        return this.count(Wrappers.<AppIntegralActivityEntity>lambdaQuery()
                .eq(AppIntegralActivityEntity::getActivityNo, activityNo)) > 0;
    }

    /**
     * 检查兑换码是否已存在（排除指定ID）
     *
     * @param activityNo 兑换码
     * @param excludeId  排除的ID
     * @return 是否存在
     */
    public boolean existsByActivityNoAndIdNot(String activityNo, Long excludeId) {
        return this.count(Wrappers.<AppIntegralActivityEntity>lambdaQuery()
                .eq(AppIntegralActivityEntity::getActivityNo, activityNo)
                .ne(AppIntegralActivityEntity::getId, excludeId)) > 0;
    }

    /**
     * 根据兑换码查询积分活动
     *
     * @param activityNo 兑换码
     * @return 积分活动实体
     */
    public AppIntegralActivityEntity getByActivityNo(String activityNo) {
        return this.getOne(Wrappers.<AppIntegralActivityEntity>lambdaQuery()
                .eq(AppIntegralActivityEntity::getActivityNo, activityNo));
    }

    /**
     * 增加活动已使用次数
     *
     * @param activityId 活动ID
     * @return 是否成功
     */
    public boolean incrementUsedCount(Long activityId) {
        return this.update(Wrappers.<AppIntegralActivityEntity>lambdaUpdate()
                .setSql("used_count = used_count + 1")
                .eq(AppIntegralActivityEntity::getId, activityId));
    }
}

