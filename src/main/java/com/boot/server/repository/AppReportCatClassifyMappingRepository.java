package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.mapper.AppReportCatClassifyMappingMapper;
import com.boot.server.entity.AppReportCatClassifyMappingEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 品种套餐指标映射(AppReportCatClassifyMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-21 00:30:55
 */
@Service
public class AppReportCatClassifyMappingRepository extends ServiceImpl<AppReportCatClassifyMappingMapper, AppReportCatClassifyMappingEntity> {

    public List<AppReportCatClassifyMappingEntity> getByName(final String name) {
        return this.list(
                Wrappers.<AppReportCatClassifyMappingEntity>lambdaQuery()
                        .eq(AppReportCatClassifyMappingEntity::getClassifyName, name)
        );
    }
}

