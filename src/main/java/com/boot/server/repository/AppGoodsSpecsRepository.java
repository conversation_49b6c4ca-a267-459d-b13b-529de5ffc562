package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.entity.AppGoodsSpecsClassifyEntity;
import com.boot.server.entity.AppGoodsSpecsEntity;
import com.boot.server.mapper.AppGoodsSpecsMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品规格表(AppGoodsSpecs)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-04 20:26:02
 */
@Service
@RequiredArgsConstructor
public class AppGoodsSpecsRepository extends ServiceImpl<AppGoodsSpecsMapper, AppGoodsSpecsEntity> {
    private final AppGoodsSpecsClassifyRepository appGoodsSpecsClassifyRepository;

    public Map<Long, String> getMapName(final List<Long> list) {
        List<AppGoodsSpecsEntity> appGoodsSpecsEntities = this.listByIds(list);
        HashMap<Long, String> hashMap = new HashMap<>();
        for (AppGoodsSpecsEntity appGoodsSpecsEntity : appGoodsSpecsEntities) {
            hashMap.put(appGoodsSpecsEntity.getId(), appGoodsSpecsEntity.getTitle());
        }
        return hashMap;
    }

    public Map<Long, AppGoodsSpecsEntity> getMapObject(final List<Long> list) {
        List<AppGoodsSpecsEntity> appGoodsSpecsEntities = this.listByIds(list);
        HashMap<Long, AppGoodsSpecsEntity> hashMap = new HashMap<>();
        for (AppGoodsSpecsEntity appGoodsSpecsEntity : appGoodsSpecsEntities) {
            hashMap.put(appGoodsSpecsEntity.getId(), appGoodsSpecsEntity);
        }
        return hashMap;
    }

    /**
     * 获取不同分组的不同分类
     * @param list 商品规格ID集合
     * @return 结果
     */
    public Map<Long, String> getSpecsClassifyMap(final List<Long> list) {
        List<AppGoodsSpecsEntity> appGoodsSpecsEntities = this.listByIds(list);
        if (CollectionUtils.isNotEmpty(appGoodsSpecsEntities)) {
            List<Long> classifyIds = appGoodsSpecsEntities.stream().map(AppGoodsSpecsEntity::getClassifyId).collect(Collectors.toList());
            List<AppGoodsSpecsClassifyEntity> appGoodsSpecsClassifyEntities = appGoodsSpecsClassifyRepository.listByIds(classifyIds);
            if (CollectionUtils.isNotEmpty(appGoodsSpecsClassifyEntities)) {
                Map<Long, String> stringMap = appGoodsSpecsClassifyEntities.stream().collect(Collectors.toMap(AppGoodsSpecsClassifyEntity::getId, AppGoodsSpecsClassifyEntity::getTitle));
                HashMap<Long, String> result = new HashMap<>();
                for (AppGoodsSpecsEntity appGoodsSpecsEntity : appGoodsSpecsEntities) {
                    Long classifyId = appGoodsSpecsEntity.getClassifyId();
                    String classifyName = stringMap.get(classifyId);
                    result.put(appGoodsSpecsEntity.getId(), classifyName);
                }
                return result;
            }
        }
        return Collections.emptyMap();
    }
}

