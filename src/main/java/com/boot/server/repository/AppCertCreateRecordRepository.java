package com.boot.server.repository;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.entity.AppCertCreateRecordDetailEntity;
import com.boot.server.entity.AppPetReportDetailEntity;
import com.boot.server.mapper.AppCertCreateRecordMapper;
import com.boot.server.entity.AppCertCreateRecordEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 证书创建记录表(AppCertCreateRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-23 14:34:53
 */
@Service
public class AppCertCreateRecordRepository extends ServiceImpl<AppCertCreateRecordMapper, AppCertCreateRecordEntity> {

    @Resource
    private AppCertCreateRecordDetailRepository appCertCreateRecordDetailRepository;

    @Transactional(rollbackFor = Exception.class)
    public void saveRecord(AppCertCreateRecordEntity certCreateRecordEntity, List<AppPetReportDetailEntity> detailEntityList) {
        boolean save = this.save(certCreateRecordEntity);
        BusinessException.throwIf(!save, "证书生成失败");
        List<AppCertCreateRecordDetailEntity> detailEntities = new ArrayList<>();
        for (AppPetReportDetailEntity entity : detailEntityList) {
            AppCertCreateRecordDetailEntity detail = new AppCertCreateRecordDetailEntity();
            detail.setRecordId(certCreateRecordEntity.getId());
            detail.setItem(entity.getItem());
            detail.setPosition(entity.getPosition());
            detail.setResult(entity.getResult());
            detailEntities.add(detail);
        }
        boolean saveBatch = appCertCreateRecordDetailRepository.saveBatch(detailEntities);
        BusinessException.throwIf(!saveBatch, "证书生成失败");
    }
}

