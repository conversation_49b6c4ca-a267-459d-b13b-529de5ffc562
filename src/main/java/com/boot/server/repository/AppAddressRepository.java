package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.entity.AppAddressEntity;
import com.boot.server.mapper.AppAddressMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 地址表(AppAddress)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 16:26:12
 */
@Service
public class AppAddressRepository extends ServiceImpl<AppAddressMapper, AppAddressEntity> {

    public AppAddressEntity getDefault() {
        return getOne(
                Wrappers.<AppAddressEntity>lambdaQuery()
                        .eq(AppAddressEntity::getIsDefault, 1)
                        .eq(AppAddressEntity::getCreateUser, AuthUtil.getCurrentUserId())
        );
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(AppAddressEntity appAddressEntity) {
        this.save(appAddressEntity);
        Integer isDefault = appAddressEntity.getIsDefault();
        if (Objects.equals(isDefault, 1)) {
            setDefault(appAddressEntity.getId());
        }
        return true;
    }

   @Transactional(rollbackFor = Exception.class)
    public Boolean update(AppAddressEntity appAddressEntity) {
        this.updateById(appAddressEntity);
        Integer isDefault = appAddressEntity.getIsDefault();
        if (Objects.equals(isDefault, 1)) {
            setDefault(appAddressEntity.getId());
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean setDefault(final Long id) {
        this.update(
                Wrappers.<AppAddressEntity>lambdaUpdate()
                        .eq(AppAddressEntity::getCreateUser, AuthUtil.getCurrentUserId())
                        .set(AppAddressEntity::getIsDefault, 0)
        );
        AppAddressEntity appAddressEntity = new AppAddressEntity();
        appAddressEntity.setId(id);
        appAddressEntity.setIsDefault(1);
        return this.updateById(appAddressEntity);
    }
}

