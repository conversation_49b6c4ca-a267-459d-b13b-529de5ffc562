package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.express.dto.ExpAddress;
import com.boot.server.common.express.enums.ContactTypeEnum;
import com.boot.server.entity.AppDepotAddressEntity;
import com.boot.server.mapper.AppDepotAddressMapper;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 发货仓地址表(AppDepotAddress)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-13 11:07:57
 */
@Service
public class AppDepotAddressRepository extends ServiceImpl<AppDepotAddressMapper, AppDepotAddressEntity> {

    public AppDepotAddressEntity getDefaultAddress() {
        AppDepotAddressEntity depotAddress = getOne(
                Wrappers.<AppDepotAddressEntity>lambdaQuery()
                        .eq(AppDepotAddressEntity::getIsDefault, 1)
        );
        BusinessException.throwIf(Objects.isNull(depotAddress), "系统配置错误，仓库地址不存在");
        return depotAddress;
    }

    public ExpAddress getDefaultAddressToExpAddress(ContactTypeEnum contactTypeEnum) {
        AppDepotAddressEntity defaultAddress = getDefaultAddress();
        ExpAddress expAddress = new ExpAddress();
        expAddress.setContact(defaultAddress.getName());
        expAddress.setTel(defaultAddress.getMobile());
        expAddress.setContactType(contactTypeEnum.getCode());
        expAddress.setProvince(defaultAddress.getProvince());
        expAddress.setCity(defaultAddress.getCity());
        expAddress.setAddress(defaultAddress.getAddress());
        return expAddress;
    }
}

