package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.dto.ClassifyDto;
import com.boot.server.entity.AppPetClassifyEntity;
import com.boot.server.mapper.AppPetClassifyMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 宠物分类表(AppPetClassify)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-18 09:50:03
 */
@Service
public class AppPetClassifyRepository extends ServiceImpl<AppPetClassifyMapper, AppPetClassifyEntity> {

    public List<ClassifyDto<Long>> selectClassifyList() {
        List<AppPetClassifyEntity> entityList = this.list(
                Wrappers.<AppPetClassifyEntity>lambdaQuery()
                        .eq(AppPetClassifyEntity::getStatus, 1)
                        .orderByAsc(AppPetClassifyEntity::getSort)
                        .orderByDesc(AppPetClassifyEntity::getId)
        );
        List<ClassifyDto<Long>> classifyDtoList = new ArrayList<>();
        for (AppPetClassifyEntity classify : entityList) {
            ClassifyDto<Long> objectClassifyDto = new ClassifyDto<>();
            objectClassifyDto.setValue(classify.getId());
            objectClassifyDto.setLabel(classify.getTitle());
            classifyDtoList.add(objectClassifyDto);
        }
        return classifyDtoList;
    }
}

