package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.mapper.AppReportResultMappingMapper;
import com.boot.server.entity.AppReportResultMappingEntity;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 报告结果映射表(AppReportResultMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28 12:35:02
 */
@Service
public class AppReportResultMappingRepository extends ServiceImpl<AppReportResultMappingMapper, AppReportResultMappingEntity> {


    /**
     * 根据位点查询映射结果
     *
     * @param positions 位点列表
     * @return 映射结果列表
     */
    public List<AppReportResultMappingEntity> selectByPositions(List<String> positions) {
        if (CollectionUtils.isEmpty(positions)) {
            return Collections.emptyList();
        }
        return this.list(
                Wrappers.<AppReportResultMappingEntity>lambdaQuery()
                        .in(AppReportResultMappingEntity::getPosition, positions)
        );
    }
}

