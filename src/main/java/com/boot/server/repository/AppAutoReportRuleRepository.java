package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.entity.AppAutoReportRuleEntity;
import com.boot.server.mapper.AppAutoReportRuleMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 自动化报告规则(AppAutoReportRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-17 13:54:29
 */
@Service
public class AppAutoReportRuleRepository extends ServiceImpl<AppAutoReportRuleMapper, AppAutoReportRuleEntity> {

    public List<AppAutoReportRuleEntity> selectByGroup(String group) {
        return this.list(
                Wrappers.<AppAutoReportRuleEntity>lambdaQuery()
                        .eq(AppAutoReportRuleEntity::getGroupName, group)
        );
    }
}

