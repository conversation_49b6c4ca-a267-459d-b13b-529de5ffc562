package com.boot.server.repository;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.dto.ActivityParticipantResponse;
import com.boot.server.dto.request.ActivityParticipantRequest;
import com.boot.server.mapper.AppIntegralActivityUserMapper;
import com.boot.server.entity.AppIntegralActivityUserEntity;
import org.springframework.stereotype.Service;

/**
 * 积分活动参加用户(AppIntegralActivityUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-23 13:14:58
 */
@Service
public class AppIntegralActivityUserRepository extends ServiceImpl<AppIntegralActivityUserMapper, AppIntegralActivityUserEntity> {

    /**
     * 检查用户是否已参加过该活动
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @return 是否已参加
     */
    public boolean hasUserJoinedActivity(Long activityId, Long userId) {
        return this.count(Wrappers.<AppIntegralActivityUserEntity>lambdaQuery()
                .eq(AppIntegralActivityUserEntity::getActivityId, activityId)
                .eq(AppIntegralActivityUserEntity::getUserId, userId)) > 0;
    }

    /**
     * 记录用户参加活动
     *
     * @param activityId 活动ID
     * @param userId     用户ID
     * @param integral   获得积分
     * @return 是否成功
     */
    public boolean recordUserJoinActivity(Long activityId, Long userId, Integer integral) {
        AppIntegralActivityUserEntity entity = new AppIntegralActivityUserEntity();
        entity.setActivityId(activityId.intValue());
        entity.setUserId(userId.intValue());
        entity.setIntegral(integral);
        return this.save(entity);
    }

    /**
     * 分页查询活动参加人员列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    public Page<ActivityParticipantResponse> selectActivityParticipants(ActivityParticipantRequest request) {
        // 这里需要使用自定义SQL查询，因为需要关联用户表获取头像和昵称
        // 先返回基础的分页查询，后续在Mapper中实现具体的SQL
        return this.baseMapper.selectActivityParticipants(request.getPage(), request);
    }
}

