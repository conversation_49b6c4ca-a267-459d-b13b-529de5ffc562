package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppGoodsSpecsClassifyEntity;
import com.boot.server.entity.AppGoodsSpecsEntity;
import com.boot.server.mapper.AppGoodsMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品表(AppGoods)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-04 18:40:00
 */
@Service
@RequiredArgsConstructor
public class AppGoodsRepository extends ServiceImpl<AppGoodsMapper, AppGoodsEntity> {
    private final AppGoodsSpecsRepository appGoodsSpecsRepository;
    private final AppGoodsSpecsClassifyRepository appGoodsSpecsClassifyRepository;

    public AppGoodsEntity selectById(Integer id) {
        AppGoodsEntity goods = this.getById(id);
        if (Objects.nonNull(goods)) {
            List<AppGoodsSpecsEntity> specsEntities = appGoodsSpecsRepository.list(
                    Wrappers.<AppGoodsSpecsEntity>lambdaQuery()
                            .eq(AppGoodsSpecsEntity::getGoodsId, goods.getId())
                            .eq(AppGoodsSpecsEntity::getStatus, 1)
                            .orderByAsc(AppGoodsSpecsEntity::getSort)
            );
            List<Long> classifyIds = specsEntities.stream().map(AppGoodsSpecsEntity::getClassifyId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(classifyIds)) {
                List<AppGoodsSpecsClassifyEntity> classifyEntities = appGoodsSpecsClassifyRepository.list(
                        Wrappers.<AppGoodsSpecsClassifyEntity>lambdaQuery()
                                .in(AppGoodsSpecsClassifyEntity::getId, classifyIds)
                                .eq(AppGoodsSpecsClassifyEntity::getStatus, 1)
                                .orderByAsc(AppGoodsSpecsClassifyEntity::getSort)
                );
                Map<Long, List<AppGoodsSpecsEntity>> specsMap = specsEntities.stream().collect(Collectors.groupingBy(AppGoodsSpecsEntity::getClassifyId));
                LinkedHashMap<String, List<AppGoodsSpecsEntity>> specsResultMap = new LinkedHashMap<>();

                // 按照分类的排序顺序来构建结果Map
                for (AppGoodsSpecsClassifyEntity classifyEntity : classifyEntities) {
                    Long classifyId = classifyEntity.getId();
                    String classifyName = classifyEntity.getTitle();
                    List<AppGoodsSpecsEntity> specsEntityList = specsMap.get(classifyId);
                    if (specsEntityList != null) {
                        specsResultMap.put(classifyName, specsEntityList);
                    }
                }
                goods.setSpecs(specsResultMap);
            }
        }
        return goods;
    }

    /**
     * 根据商品名称查询商品
     * @param title 商品名称
     * @return 商品实体
     */
    public AppGoodsEntity getByTitle(String title) {
        return this.getOne(
                Wrappers.<AppGoodsEntity>lambdaQuery()
                        .eq(AppGoodsEntity::getTitle, title)
                        .eq(AppGoodsEntity::getStatus, 1)
        );
    }
}

