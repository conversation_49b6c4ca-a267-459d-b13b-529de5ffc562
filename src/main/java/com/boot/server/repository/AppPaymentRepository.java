package com.boot.server.repository;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.pay.domain.dto.PayDto;
import com.boot.server.common.pay.domain.dto.QueryPayResultRequest;
import com.boot.server.common.pay.domain.vo.WxPushOrderResponse;
import com.boot.server.entity.AppOrdersEntity;
import com.boot.server.entity.AppPaymentEntity;
import com.boot.server.enums.PayStatusEnum;
import com.boot.server.enums.PaymentStatusEnum;
import com.boot.server.mapper.AppOrdersMapper;
import com.boot.server.mapper.AppPaymentMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 订单支付表(AppPayment)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Service
@RequiredArgsConstructor
public class AppPaymentRepository extends ServiceImpl<AppPaymentMapper, AppPaymentEntity> {
    private final AppOrdersMapper appOrdersMapper;

    public void createWxPayment(WxPushOrderResponse wxPushOrderResponse, PayDto payDto) {
        AppPaymentEntity appPaymentEntity = new AppPaymentEntity();
        appPaymentEntity.setAmount(payDto.getPayAmount());
        appPaymentEntity.setStatus(PaymentStatusEnum.TO_PAY.getCode());
        appPaymentEntity.setPrepayId(wxPushOrderResponse.getPrepayId());
        appPaymentEntity.setOrderNo(payDto.getOrderNo());
        baseMapper.insert(appPaymentEntity);
    }


    public void wxPaySuccess(final String orderNo, final String transactionId) {
        AppPaymentEntity appPayment = this.getOne(
                Wrappers.<AppPaymentEntity>lambdaQuery()
                        .eq(AppPaymentEntity::getOrderNo, orderNo)
                        .orderByDesc(AppPaymentEntity::getCreateUser)
                        .last("limit 1")
        );
        if (Objects.equals(appPayment.getStatus(), PaymentStatusEnum.TO_PAY.getCode())) {
            appPayment.setStatus(PaymentStatusEnum.SUCCESS.getCode());
            appPayment.setTransactionId(transactionId);
            baseMapper.updateById(appPayment);
        }
    }

    public Boolean payResult(QueryPayResultRequest request) {
        AppPaymentEntity appPayment = this.getOne(
                Wrappers.<AppPaymentEntity>lambdaQuery()
                        .eq(AppPaymentEntity::getOrderNo, request.getOrderNo())
                        .orderByDesc(AppPaymentEntity::getCreateUser)
                        .last("limit 1")
        );
        if (Objects.isNull(appPayment)) {
            AppOrdersEntity orders = appOrdersMapper.selectOne(
                    Wrappers.<AppOrdersEntity>lambdaQuery()
                            .eq(AppOrdersEntity::getOrderNo, request.getOrderNo())
            );
            return Objects.equals(orders.getPayStatus(), PayStatusEnum.SUCCESS.getCode());
        }
        return Objects.equals(appPayment.getStatus(), PaymentStatusEnum.SUCCESS.getCode());
    }
}

