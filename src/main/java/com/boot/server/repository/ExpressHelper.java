package com.boot.server.repository;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.boot.server.common.express.ExpressOrderService;
import com.boot.server.common.express.dto.CreateOrderRequest;
import com.boot.server.common.express.dto.ExpAddress;
import com.boot.server.common.express.dto.RouterResponse;
import com.boot.server.common.express.dto.RoutesRequest;
import com.boot.server.common.express.enums.ContactTypeEnum;
import com.boot.server.common.express.utils.ExpressUtil;
import com.boot.server.config.ExpressConfig;
import com.boot.server.dto.OrderRouterResponse;
import com.boot.server.entity.AppOrdersEntity;
import com.boot.server.entity.AppPetBindOrderEntity;
import com.boot.server.enums.PetBindOrderStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * ExpressHelper
 *
 * <AUTHOR> 2025/5/13 11:15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExpressHelper {
    private final AppDepotAddressRepository appDepotAddressRepository;
    private final ExpressOrderService expressOrderService;
    private final ExpressConfig expressConfig;
    private final AppPetBindOrderRepository appPetBindOrderRepository;
    private final AppPetRepository appPetRepository;


    /**
     * 购买成功，下单物流信息
     *
     * @param appOrders 商品订单信息
     * @return 物流单号信息
     */
    public String createExpressOrder(AppOrdersEntity appOrders) {
        CreateOrderRequest request = new CreateOrderRequest();
        List<ExpAddress> expAddressList = new ArrayList<>();
        ExpAddress senderAddress = appDepotAddressRepository.getDefaultAddressToExpAddress(ContactTypeEnum.SENDER);
        ExpAddress receiverAddress = new ExpAddress();
        receiverAddress.setContact(appOrders.getName());
        receiverAddress.setTel(appOrders.getMobile());
        receiverAddress.setProvince(appOrders.getProvince());
        receiverAddress.setCity(appOrders.getCity());
        receiverAddress.setAddress(appOrders.getAddress());
        receiverAddress.setContactType(ContactTypeEnum.RECEIVER.getCode());
        request.setOrderId(appOrders.getOrderNo());
        expAddressList.add(senderAddress);
        expAddressList.add(receiverAddress);
        request.setContactInfoList(expAddressList);
        request.setCargoDetails(CreateOrderRequest.getCargoDetails());
        request.setMonthlyCard(expressConfig.getMonthlyCard());
        JSONObject createOrderResponse = expressOrderService.createOrder(request);
        return ExpressUtil.getWaybillNo(createOrderResponse);
    }

    /**
     * 查询订单顺丰单号的路由信息
     *
     * @param logistics 顺丰单号
     * @return 结果
     */
    public OrderRouterResponse queryRoutes(final String logistics) {
        OrderRouterResponse orderRouterResponse = new OrderRouterResponse();
        RoutesRequest routesRequest = new RoutesRequest();
        routesRequest.setTrackingNumber(Collections.singletonList(logistics));
        // 判断是否添加用户手机号后四位
        AppPetBindOrderEntity petBindOrder = appPetBindOrderRepository.getByLogistics(logistics);
        if (Objects.nonNull(petBindOrder)) {
            String senderTel = petBindOrder.getSenderTel();
            if (StrUtil.isNotBlank(senderTel) && senderTel.length() >= 4) {
                // 设置手机号后四位
                routesRequest.setCheckPhoneNo(senderTel.substring(senderTel.length() - 4));
            }
        }
        JSONObject queryRoutes = expressOrderService.queryRoutes(routesRequest);
        List<RouterResponse> waybillRouters = ExpressUtil.getWaybillRouters(queryRoutes);
        orderRouterResponse.setLogistics(logistics);
        orderRouterResponse.setRouters(waybillRouters);
        return orderRouterResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean pickUp(Long binderId) {
        // 绑定记录状态变更为已收件,宠物状态变更为 检测中
        AppPetBindOrderEntity byLogistics = appPetBindOrderRepository.getById(binderId);
        if (Objects.isNull(byLogistics) || Objects.equals(byLogistics.getStatus(), PetBindOrderStatusEnum.DONE.getCode())) {
            log.warn("为找到对应绑定信息或已处理: {} {}", byLogistics.getLogistics(), byLogistics);
            return false;
        }
        // 已取件
        byLogistics.setStatus(PetBindOrderStatusEnum.DONE.getCode());
        appPetBindOrderRepository.updateById(byLogistics);
        // 设置宠物状态
        appPetRepository.updateStatusToProcess(byLogistics.getPetId());
        return true;
    }
}
