package com.boot.server.repository;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.constant.SysConstant;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.util.BeanUtil;
import com.boot.server.common.util.ImgUtil;
import com.boot.server.dto.AutoReportDetailResponse;
import com.boot.server.dto.AutoReportResponse;
import com.boot.server.dto.request.AutoReportRequest;
import com.boot.server.dto.request.ReportDto;
import com.boot.server.entity.AppAutoReportTaskEntity;
import com.boot.server.mapper.AppAutoReportTaskMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自动化报告任务(AppAutoReportTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18 10:59:50
 */
@Service
@RequiredArgsConstructor
public class AppAutoReportTaskRepository extends ServiceImpl<AppAutoReportTaskMapper, AppAutoReportTaskEntity> {
    private final ImgUtil imgUtil;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateReportStatus(final Long taskId, final Integer status) {
        AppAutoReportTaskEntity taskEntity = getById(taskId);
        if (taskEntity == null) {
            throw new BusinessException("任务不存在");
        }
        taskEntity.setStatus(status);
        BusinessException.throwIf(!updateById(taskEntity), "更新任务状态失败");
    }

    public AppAutoReportTaskEntity getByKitsNo(String deviceId) {
        return this.getOne(
                Wrappers.<AppAutoReportTaskEntity>lambdaQuery()
                        .eq(AppAutoReportTaskEntity::getKitsNo, deviceId)
        );
    }

    public Page<AutoReportResponse> selectAdminPage(AutoReportRequest request) {
        Page<AppAutoReportTaskEntity> page = request.getPage();
        Page<AppAutoReportTaskEntity> resultPage = baseMapper.selectPage(
                page,
                Wrappers.<AppAutoReportTaskEntity>lambdaQuery()
                        .like(StrUtil.isNotBlank(request.getKitsNo()), AppAutoReportTaskEntity::getKitsNo, request.getKitsNo())
                        .like(StrUtil.isNotBlank(request.getOrganization()), AppAutoReportTaskEntity::getOrganization, request.getOrganization())
                        .like(StrUtil.isNotBlank(request.getPetNo()), AppAutoReportTaskEntity::getPetNo, request.getPetNo())
                        .eq(request.getStatus() != null, AppAutoReportTaskEntity::getStatus, request.getStatus())
        );
        List<AppAutoReportTaskEntity> records = resultPage.getRecords();
        Page<AutoReportResponse> responsePage = new Page<>(resultPage.getCurrent(), resultPage.getSize(), resultPage.getTotal());
        if (CollectionUtils.isNotEmpty(records)) {
            List<AutoReportResponse> autoReportResponses = BeanUtil.copyToList(records, AutoReportResponse.class);
            responsePage.setRecords(autoReportResponses);
        }
        return responsePage;
    }

    public List<AutoReportDetailResponse> selectDetails(Integer id) {
        AppAutoReportTaskEntity appAutoReportTaskEntity = baseMapper.selectById(id);
        if (appAutoReportTaskEntity == null) {
            throw new BusinessException("自动化证书任务不存在");
        }
        String kitsNo = appAutoReportTaskEntity.getKitsNo();
        String reportJson = appAutoReportTaskEntity.getReportJson();
        ReportDto reportDto = JSON.parseObject(reportJson, ReportDto.class);
        return reportDto.getData().stream()
                .filter(row -> !StrUtil.equalsIgnoreCase(row.getGroup(), SysConstant.POSITION_GROUP))
                .map(ReportDto.Group::getItems)
                .flatMap(List::stream)
                .map(row -> {
                    AutoReportDetailResponse response = BeanUtil.copyToBean(row, AutoReportDetailResponse.class);
                    if (Objects.equals(appAutoReportTaskEntity.getStatus(), SysConstant.AUTO_REPORT_DONE)) {
                        response.setReportUrl(imgUtil.getOssReportImage(kitsNo + "/" + row.getItem()));
                    }
                    return response;
                })
                .collect(Collectors.toList());
    }
}

