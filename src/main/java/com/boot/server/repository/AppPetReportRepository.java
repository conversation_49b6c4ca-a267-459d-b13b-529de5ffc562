package com.boot.server.repository;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.entity.AppPetBindOrderEntity;
import com.boot.server.entity.AppPetReportEntity;
import com.boot.server.enums.PetBindOrderStatusEnum;
import com.boot.server.mapper.AppPetReportMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 试剂盒报告结果(AppPetReport)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14 16:41:58
 */
@Service
@RequiredArgsConstructor
public class AppPetReportRepository extends ServiceImpl<AppPetReportMapper, AppPetReportEntity> {
    private final AppPetBindOrderRepository appPetBindOrderRepository;
    private final AppPetRepository appPetRepository;

    /**
     * 证书生成后调用
     *
     * @param appPetReportEntity 证书记录数据
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateReportStatus(final AppPetReportEntity appPetReportEntity, final List<String> certPaths) {
        this.updateById(appPetReportEntity);
        AppPetBindOrderEntity byKitsNo = appPetBindOrderRepository.getByKitsNo(appPetReportEntity.getKitsNo());
        byKitsNo.setIsReport(1);
        byKitsNo.setIsCertificate(1);
        byKitsNo.setStatus(PetBindOrderStatusEnum.DONE.getCode());
        byKitsNo.setCertImages(String.join(",", certPaths));
        appPetBindOrderRepository.updateById(byKitsNo);
        // 修改宠物状态为已检测
        appPetRepository.updateStatusToDone(byKitsNo.getPetId());
    }

    /**
     * 是否存在报告信息
     *
     * @param id 宠物ID
     * @return 是否存在
     */
    public Boolean hasReportByPetId(Long id) {
        AppPetReportEntity petReport = this.getOne(
                Wrappers.<AppPetReportEntity>lambdaQuery()
                        .eq(AppPetReportEntity::getPetId, id)
                ,
                false
        );
        return Objects.nonNull(petReport);
    }

    public List<AppPetReportEntity> selectListByPetId(final Long petId) {
        return this.list(
                Wrappers.<AppPetReportEntity>lambdaQuery()
                        .eq(AppPetReportEntity::getPetId, petId)
        );
    }
}

