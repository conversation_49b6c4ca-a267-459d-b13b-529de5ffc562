package com.boot.server.repository;

import com.baomidou.dynamic.datasource.annotation.Slave;
import com.boot.server.dto.SampleResponse;
import com.boot.server.mapper.SampleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * SampleRepository
 *
 * <AUTHOR> 2025/7/16 18:22
 */
@Slave
@Component
@RequiredArgsConstructor
public class SampleRepository {
    private final SampleMapper sampleMapper;

    /**
     * 根据设备ID查询样本信息
     *
     * @param deviceId 设备ID
     * @return 样本信息
     */
    @Slave
    public SampleResponse selectSampleByDeviceId(final String deviceId) {
        return sampleMapper.selectSampleByDeviceId(deviceId);
    }

}
