package com.boot.server.repository;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.dto.CartListResponse;
import com.boot.server.entity.AppShoppingCartEntity;
import com.boot.server.mapper.AppShoppingCartMapper;
import org.springframework.stereotype.Service;

/**
 * 购物车(AppShoppingCart)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-05 10:58:30
 */
@Service
public class AppShoppingCartRepository extends ServiceImpl<AppShoppingCartMapper, AppShoppingCartEntity> {

    public Page<CartListResponse> selectPageUserCart(Long currentUserId, Page<CartListResponse> page) {
        return baseMapper.selectPageUserCart(currentUserId, page);
    }

    /**
     * 减购物数量
     *
     * @param id 购物车 ID
     * @return 是否成功
     */
    public Boolean decr(Long id) {
        Long currentUserId = AuthUtil.getCurrentUserId();
        return baseMapper.updateNum(id, -1, currentUserId);
    }

    /**
     * 加购物数量
     *
     * @param id 购物车 ID
     * @return 是否成功
     */
    public Boolean icer(Long id) {
        Long currentUserId = AuthUtil.getCurrentUserId();
        return baseMapper.updateNum(id, 1, currentUserId);
    }
}

