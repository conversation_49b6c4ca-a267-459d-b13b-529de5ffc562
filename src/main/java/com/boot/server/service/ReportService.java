package com.boot.server.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boot.server.common.constant.SysConstant;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.util.ReportUtil;
import com.boot.server.dto.ReportResponse;
import com.boot.server.dto.SampleResponse;
import com.boot.server.dto.TextPosition;
import com.boot.server.dto.UploadReportResponse;
import com.boot.server.dto.request.ReportDto;
import com.boot.server.dto.request.UploadReportRequest;
import com.boot.server.entity.*;
import com.boot.server.repository.*;
import com.boot.server.rule.IRule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * ReportService
 *
 * <AUTHOR> 2025/5/14 16:34
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportService {
    private final AppPetBindOrderRepository appPetBindOrderRepository;
    private final AppPetRepository appPetRepository;
    private final AppOrderDetailsRepository appOrderDetailsRepository;
    private final AppPetReportRepository appPetReportRepository;
    private final AppPetReportDetailRepository appPetReportDetailRepository;
    private final AppReportPositionResultRepository appReportPositionResultRepository;
    private final AppUserRepository appUserRepository;
    private final AppReportMappingRepository appReportMappingRepository;
    private final AppReportCatClassifyMappingRepository appReportCatClassifyMappingRepository;
    private final AppCertCreateRecordRepository appCertCreateRecordRepository;
    private final AppReportResultMappingRepository appReportResultMappingRepository;
    private final AppOfflineOrderRepository appOfflineOrderRepository;
    private final AppAutoReportTaskService appAutoReportTaskService;

    @Autowired
    private List<IRule> rules;

    @Autowired
    private ReportUtil reportUtil;

    @Autowired
    @Lazy
    private ReportService reportService;

    public UploadReportResponse uploadReport(UploadReportRequest request) {
        String path = request.getPath();
        log.info("上传报告开始: {}", path);
        BusinessException.throwIf(!FileUtil.exist(path), "文件目录不存在: " + path);
        // 1. 获取上传的文件
        List<File> files = FileUtil.loopFiles(request.getPath(), file -> {
            String fileName = file.getName();
            // 只处理JSON文件
            return fileName.endsWith(".json");
        });
        BusinessException.throwIf(CollectionUtils.isEmpty(files), "文件目录下没有对应的JSON文件");
        // 总数量
        int total = files.size();
        // 失败数量
        int filed = 0;
        int success = 0;
        // 结果集
        List<UploadReportResponse.FiledResponse> filedList = new ArrayList<>();
        List<UploadReportResponse.WarnResponse> yichang = new ArrayList<>();
        UploadReportResponse response = UploadReportResponse.builder()
                .paramFolder(path)
                .total(total).build();
        for (File jsonFile : files) {
            try {
                String fileName = jsonFile.getName();
                // 1，上传 json文件到服务器
                String reportJsonValue = FileUtil.readUtf8String(jsonFile);
                log.info("报表数据 {}", reportJsonValue);
                // 校验json格式
                List<UploadReportResponse.FiledResponse> filedResponses = new ArrayList<>();
                verifyDataFormat(fileName, reportJsonValue, filedResponses);
                if (CollectionUtils.isEmpty(filedResponses)) {
                    log.info("开始处理JSON进行入库: {}", fileName);
                    List<UploadReportResponse.WarnResponse> warnResponses = reportService.handleReport(reportJsonValue);
                    yichang.addAll(warnResponses);
                    success++;
                } else {
                    log.info("文件{} JSON结构校验失败{}条错误", fileName, filedResponses.size());
                    filedList.addAll(filedResponses);
                    filed++;
                }
            } catch (Exception e) {
                log.error("文件处理失败: {}, 错误信息: {}", jsonFile.getName(), e.getMessage());
                filedList.add(UploadReportResponse.createFiledResponse(jsonFile.getName(), e.getMessage()));
                filed++;
            }
        }
        response.setSuccess(success);
        response.setFailed(filed);
        response.setFiledList(filedList);
        response.setYichang(yichang);
        return response;
    }

    public UploadReportResponse uploadOtherSystemReport(UploadReportRequest request) {
        String path = request.getPath();
        log.info("其他系统上传报告开始: {}", path);
        BusinessException.throwIf(!FileUtil.exist(path), "文件目录不存在: " + path);
        // 1. 获取上传的文件
        List<File> files = FileUtil.loopFiles(request.getPath(), file -> {
            String fileName = file.getName();
            // 只处理JSON文件
            return fileName.endsWith(".json");
        });
        BusinessException.throwIf(CollectionUtils.isEmpty(files), "文件目录下没有对应的JSON文件");
        // 总数量
        int total = files.size();
        // 失败数量
        int filed = 0;
        int success = 0;
        // 结果集
        List<UploadReportResponse.FiledResponse> filedList = new ArrayList<>();
        UploadReportResponse response = UploadReportResponse.builder()
                .paramFolder(path)
                .total(total).build();
        for (File jsonFile : files) {
            try {
                String fileName = jsonFile.getName();
                // 1，上传 json文件到服务器
                String reportJsonValue = FileUtil.readUtf8String(jsonFile);
                // 校验json格式
                List<UploadReportResponse.FiledResponse> filedResponses = new ArrayList<>();
                verifyDataFormat(fileName, reportJsonValue, filedResponses);
                if (CollectionUtils.isEmpty(filedResponses)) {
                    log.info("开始处理JSON进行生成图片: {}", fileName);
                    // 记录报告信息
                    appAutoReportTaskService.saveOrUpdate(reportJsonValue);
                    success++;
                } else {
                    log.info("二方报告文件{} JSON结构校验失败{}条错误", fileName, filedResponses.size());
                    filedList.addAll(filedResponses);
                    filed++;
                }
            } catch (Exception e) {
                log.error("二方报告文件处理失败: {}, 错误信息: {}", jsonFile.getName(), e.getMessage());
                filedList.add(UploadReportResponse.createFiledResponse(jsonFile.getName(), e.getMessage()));
                filed++;
            }
        }
        response.setSuccess(success);
        response.setFailed(filed);
        response.setFiledList(filedList);
        return response;
    }

    /**
     * JSON格式校验
     *
     * @param fileName        文件名
     * @param reportJsonValue json
     * @param filedResponses  结果集
     */
    private void verifyDataFormat(String fileName, final String reportJsonValue, List<UploadReportResponse.FiledResponse> filedResponses) {
        JSONObject reportValue = JSON.parseObject(reportJsonValue);
        if (!reportValue.containsKey("serial_no")) {
            filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON格式不正确,缺少serial_no字段"));

        }
        String serialNo = reportValue.getString("serial_no");
        if (StrUtil.isBlank(serialNo)) {
            filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON中serial_no字段不能为空"));
        }
        if (!reportValue.containsKey("data")) {
            filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON格式不正确,缺少data字段"));
        }
        if (!reportValue.isArray("data")) {
            filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON中的data必须为数组类型"));
        } else {
            if (reportValue.getJSONArray("data").isEmpty()) {
                filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON数据data不能为空"));
            }
        }
        JSONArray reportDataArray = reportValue.getJSONArray("data");
        for (int i = 0; i < reportDataArray.size(); i++) {
            JSONObject reportDataObject = reportDataArray.getJSONObject(i);
            if (!reportDataObject.containsKey("group")) {
                filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON格式不正确,data中缺少group字段,index: " + i));
            }
            if (!reportDataObject.containsKey("items")) {
                filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON格式不正确,data中缺少items字段,index: " + i));
            }
            if (!reportDataObject.isArray("items")) {
                filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON中的items必须为数组类型,index:" + i));
            } else {
                if (reportDataObject.getJSONArray("items").isEmpty()) {
                    filedResponses.add(UploadReportResponse.createFiledResponse(fileName, "JSON中的items不能为空,index:" + i));
                }
            }

        }
    }

    /**
     * JSON 入库
     *
     * @param reportJsonValue json
     */
    @Transactional(rollbackFor = Exception.class)
    public List<UploadReportResponse.WarnResponse> handleReport(String reportJsonValue) {
        ReportDto reportValue = JSON.parseObject(reportJsonValue, ReportDto.class);
        String serialNo = reportValue.getSerialNo();
        AppPetBindOrderEntity petBindOrder = appPetBindOrderRepository.getByKitsNo(serialNo);
        if (Objects.isNull(petBindOrder)) {
            throw new BusinessException("未找到试剂盒绑定宠物信息");
        }
        Long orderDetailId = petBindOrder.getOrderDetailId();
        Long petId = petBindOrder.getPetId();
        AppPetReportEntity petReport = new AppPetReportEntity();
        petReport.setPetId(petId);
        petReport.setOrderDetailId(orderDetailId);
        petReport.setKitsNo(serialNo);
        petReport.setReport(reportJsonValue);
        try {
            appPetReportRepository.save(petReport);
        } catch (DuplicateKeyException e) {
            throw new BusinessException("重复上报：" + reportValue.getSerialNo() + ".json");
        }
        // 本次之前的报告信息
        List<AppPetReportDetailEntity> beforeList = appPetReportDetailRepository.selectListByPetId(petId);
        List<AppReportPositionResultEntity> positionResult = getAppPetReportPositionResultEntities(reportValue, petReport);
        if (CollectionUtils.isNotEmpty(positionResult)) {
            log.info("{} 本次同步的位点检测结果: {}", petReport.getKitsNo(), positionResult.size());
            appReportPositionResultRepository.saveBatch(positionResult, 500);
        }
        // 本次同步的报告信息
        List<AppPetReportDetailEntity> reportDetailEntityList = getAppPetReportDetailEntities(reportValue, petReport);
        appPetReportDetailRepository.saveBatch(reportDetailEntityList, 500);
        // 修改宠物状态为已检测
        appPetReportRepository.updateReportStatus(petReport, Collections.emptyList());
        if (CollectionUtils.isNotEmpty(beforeList)) {
            // 之前的数据
            Map<String, AppPetReportDetailEntity> entityMap = beforeList.stream()
                    .collect(
                            Collectors.toMap(
                                    row -> row.getItem() + row.getPosition(),
                                    Function.identity(),
                                    (v1, v2) -> v2
                            )
                    );

            // 本次的数据
            Map<String, AppPetReportDetailEntity> currentMap = reportDetailEntityList.stream()
                    .collect(
                            Collectors.toMap(
                                    row -> row.getItem() + row.getPosition(),
                                    Function.identity(),
                                    (v1, v2) -> v2
                            )
                    );
            List<UploadReportResponse.WarnResponse> warnResponses = new ArrayList<>();
            for (Map.Entry<String, AppPetReportDetailEntity> entry : currentMap.entrySet()) {
                AppPetReportDetailEntity before = entityMap.get(entry.getKey());
                if (Objects.nonNull(before)) {
                    AppPetReportDetailEntity value = entry.getValue();
                    if (!StrUtil.equalsIgnoreCase(before.getResult(), value.getResult())) {
                        UploadReportResponse.WarnResponse response = new UploadReportResponse.WarnResponse();
                        response.setPosition(before.getPosition());
                        response.setItem(before.getItem());
                        response.setSourceRes(before.getResult());
                        response.setSourceSerialNo(before.getKitsNo());
                        response.setTargetSerialNo(value.getKitsNo());
                        response.setTargetRes(value.getResult());
                        warnResponses.add(response);
                    }
                }
            }
            return warnResponses;

        }
        return Collections.emptyList();
    }

    /**
     * 构建报告明细
     *
     * @param reportValue 报告数据
     * @param petReport   宠物报告实体
     * @return 报告明细实体列表
     */
    @NotNull
    private static List<AppPetReportDetailEntity> getAppPetReportDetailEntities(ReportDto reportValue, AppPetReportEntity petReport) {
        List<ReportDto.Group> data = reportValue.getData();
        // 过滤掉 位点检测结果
        List<ReportDto.Group> groupList = data.stream()
                .filter(row -> !StrUtil.equals(row.getGroup(), SysConstant.POSITION_GROUP))
                .collect(Collectors.toList());
        List<AppPetReportDetailEntity> reportDetailEntityList = new ArrayList<>();
        for (ReportDto.Group group : groupList) {
            for (ReportDto.ReportDetail item : group.getItems()) {
                AppPetReportDetailEntity detail = getAppPetReportDetailEntity(petReport, item, group.getGroup());
                reportDetailEntityList.add(detail);
            }
        }
        return reportDetailEntityList;
    }

    @NotNull
    private static AppPetReportDetailEntity getAppPetReportDetailEntity(AppPetReportEntity petReport, ReportDto.ReportDetail item, String group) {
        AppPetReportDetailEntity detail = new AppPetReportDetailEntity();
        detail.setReportId(petReport.getId());
        detail.setPetId(petReport.getPetId());
        detail.setKitsNo(petReport.getKitsNo());
        detail.setGroup(group);
        detail.setItem(item.getItem());
        detail.setPosition(item.getPosition());
        detail.setResult(item.getResult());
        detail.setSortNo(Objects.isNull(item.getSort()) ? 0 : item.getSort());
        detail.setCoat(item.getCoat());
        detail.setResultInterpretation(item.getResultInterpretation());
        return detail;
    }

    private static List<AppReportPositionResultEntity> getAppPetReportPositionResultEntities(ReportDto reportValue, AppPetReportEntity appPetReport) {
        List<ReportDto.Group> data = reportValue.getData();
        for (ReportDto.Group group : data) {
            if (StrUtil.equals(group.getGroup(), SysConstant.POSITION_GROUP)) {
                // 存入app_report_position_result表
                List<ReportDto.ReportDetail> items = group.getItems();
                return getAppReportPositionResultEntities(appPetReport, items);
            }
        }
        return Collections.emptyList();
    }

    @NotNull
    private static List<AppReportPositionResultEntity> getAppReportPositionResultEntities(AppPetReportEntity appPetReport, List<ReportDto.ReportDetail> items) {
        List<AppReportPositionResultEntity> positionResultEntityList = new ArrayList<>();
        for (ReportDto.ReportDetail item : items) {
            AppReportPositionResultEntity positionResult = new AppReportPositionResultEntity();
            positionResult.setReportId(appPetReport.getId());
            positionResult.setPosition(item.getPosition());
            positionResult.setResult(item.getPResult());
            positionResultEntityList.add(positionResult);
        }
        return positionResultEntityList;
    }

    public ReportResponse getDetailById(Long binderId) {
        ReportResponse response = new ReportResponse();
        AppPetBindOrderEntity bindOrderEntity = appPetBindOrderRepository.getById(binderId);
        BusinessException.throwIf(Objects.isNull(bindOrderEntity), "未知数据");
        Long petId = bindOrderEntity.getPetId();
        AppPetEntity appPet = appPetRepository.selectById(petId);
        BusinessException.throwIf(Objects.isNull(appPet), "未知数据");
        Long orderDetailId = bindOrderEntity.getOrderDetailId();
        AppOrderDetailsEntity orderDetails = appOrderDetailsRepository.getById(orderDetailId);
        BusinessException.throwIf(Objects.isNull(orderDetails), "未知数据");
        Long createUser = appPet.getCreateUser();
        AppUserEntity appUser = appUserRepository.getById(createUser);
        BusinessException.throwIf(Objects.isNull(appUser), "未知繁育机构");
        response.setPetName(appPet.getPetName());
        response.setPetRuName(appPet.getPetRuName());
        response.setBreedName(appPet.getVarieties());
        response.setGenderName(appPet.getGenderName());
        // 繁育机构
        response.setBreederName(appUser.getNickName());
        response.setTestProject(orderDetails.getTitle());
        response.setKitsNo(bindOrderEntity.getKitsNo());
        AppPetReportEntity entity = appPetReportRepository.getOne(
                Wrappers.<AppPetReportEntity>lambdaQuery()
                        .eq(AppPetReportEntity::getPetId, petId)
        );
        if (Objects.equals(entity.getStatus(), 0)) {
            throw new BusinessException("报告未生成请稍后查看");
        }
        List<AppPetReportDetailEntity> reportDetailEntityList = appPetReportDetailRepository.listByReportId(entity.getId());
        List<AppReportMappingEntity> mappings = appReportMappingRepository.findByGoodsId(orderDetails.getGoodsId());
        List<String> mappingKey = mappings.stream().map(AppReportMappingEntity::getReportName).collect(Collectors.toList());
        List<AppPetReportDetailEntity> detailEntities = reportDetailEntityList.stream()
                .filter(row -> mappingKey.contains(row.getItem()))
                .collect(Collectors.toList());
        List<ReportResponse.Item> collect = detailEntities.stream().map(row -> {
            ReportResponse.Item item = new ReportResponse.Item();
            item.setLocusName(row.getPosition());
            item.setProjectName(row.getItem());
            item.setResult(row.getResult());
            return item;
        }).collect(Collectors.toList());
        response.setItems(collect);
        return response;
    }

    /**
     * 根据宠物ID获取宠物检测报告
     *
     * @param petId 宠物ID
     * @return
     */
    public ReportResponse getReportDetailByPetId(Long petId) {
        ReportResponse reportResponse = new ReportResponse();
        AppPetEntity appPet = appPetRepository.selectById(petId);
        BusinessException.throwIf(Objects.isNull(appPet), "未知数据");
        Long createUser = appPet.getCreateUser();
        AppUserEntity appUser = appUserRepository.getById(createUser);
        BusinessException.throwIf(Objects.isNull(appUser), "未知繁育机构");
        reportResponse.setPetName(appPet.getPetName());
        reportResponse.setPetNo(appPet.getPetNo());
        reportResponse.setBreederName(appUser.getNickName());
        reportResponse.setBreedName(appPet.getVarieties());
        reportResponse.setGenderName(appPet.getGenderName());
        // 查询合并的报告详情
        List<AppPetReportEntity> entityList = appPetReportRepository.selectListByPetId(petId);
        if (CollectionUtils.isEmpty(entityList)) {
            return reportResponse;
        }
        List<Long> reportIds = entityList.stream().map(AppPetReportEntity::getId).collect(Collectors.toList());
        log.info("宠物: {},关联的报告: {}", petId, reportIds);
        // 线下获取的订单为空
        Collection<Long> goodsIds = new ArrayList<>();
        List<Long> orderDetailIds = entityList.stream().map(AppPetReportEntity::getOrderDetailId)
                .filter(row -> !Objects.equals(row, 0L))
                .collect(Collectors.toList());
        List<String> offlineKitsNos = entityList.stream()
                .filter(row -> Objects.equals(row.getOrderDetailId(), 0L))
                .map(AppPetReportEntity::getKitsNo)
                .collect(Collectors.toList());
        // 对应订单需要出的检测项
        List<String> needMappingKeys = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderDetailIds)) {
            List<AppOrderDetailsEntity> orderDetailsEntities = appOrderDetailsRepository.listByIds(orderDetailIds);
            Map<Long, Long> detailGoodsMap = orderDetailsEntities.stream().collect(Collectors.toMap(AppOrderDetailsEntity::getId, AppOrderDetailsEntity::getGoodsId));
            log.info("宠物: {},报告关联的商品: {}", petId, detailGoodsMap.values());
            goodsIds.addAll(detailGoodsMap.values());
            List<String> onlineOrderDetailMappingKey = orderDetailsEntities.stream().map(row -> {
                        List<String> keys = new ArrayList<>();
                        for (String specsId : StrUtil.split(row.getGoodsSpecs(), ",")) {
                            keys.add(row.getGoodsId() + ":" + specsId);
                        }
                        return keys;
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            needMappingKeys.addAll(onlineOrderDetailMappingKey);
        }
        // 如果有商品ID为0的，说明是线下渠道获取的订单
        if (CollectionUtils.isNotEmpty(offlineKitsNos)) {
            List<AppOfflineOrderEntity> offlineOrders = appOfflineOrderRepository.listByKitsNo(offlineKitsNos);
            List<String> offlineOrderDetailMappingKey = offlineOrders.stream().map(row -> {
                        List<String> keys = new ArrayList<>();
                        for (String specsId : StrUtil.split(row.getGoodsSpecs(), ",")) {
                            keys.add(row.getGoodsId() + ":" + specsId);
                        }
                        return keys;
                    })
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            needMappingKeys.addAll(offlineOrderDetailMappingKey);
            List<Long> offlineGoodsIds = offlineOrders.stream().map(AppOfflineOrderEntity::getGoodsId).collect(Collectors.toList());
            goodsIds.addAll(offlineGoodsIds);
        }
        List<AppPetReportDetailEntity> detailEntityList = appPetReportDetailRepository.listByReportIds(reportIds);
        List<String> mappings = new ArrayList<>();
        // 查询报告映射关系
        List<AppReportMappingEntity> mappingEntityList = appReportMappingRepository.selectByByIds(goodsIds);
        if (CollectionUtils.isNotEmpty(mappingEntityList)) {
            // 对应商品对应规格的映射关系
            List<String> mappingKeys = mappingEntityList.stream()
                    .filter(row -> needMappingKeys.contains(row.getGoodsId() + ":" + row.getSpecsId()))
                    .map(AppReportMappingEntity::getReportName)
                    .collect(Collectors.toList());
            mappings.addAll(mappingKeys);
        }
        // 是否有品种分类商品(2025-07-24品种套餐下线)
        if (goodsIds.contains(SysConstant.CLASSIFY_GOODS_ID)) {
            log.info("宠物: {}存在品种分类商品", petId);
            // 获取最终品种
            List<AppPetBindOrderEntity> binderList = appPetBindOrderRepository.list(
                    Wrappers.<AppPetBindOrderEntity>lambdaQuery()
                            .eq(AppPetBindOrderEntity::getPetId, petId)
                            .eq(AppPetBindOrderEntity::getIsReport, 1)
            );
            String varietiesName = binderList.stream()
                    .map(AppPetBindOrderEntity::getVarieties)
                    .filter(StrUtil::isNotBlank)
                    .findFirst().orElse(null);
            log.info("宠物: {}的品种分类商品规格名称: {}", petId, varietiesName);
            if (StrUtil.isNotBlank(varietiesName)) {
                List<AppReportCatClassifyMappingEntity> mappingKeys = appReportCatClassifyMappingRepository.getByName(varietiesName);
                log.info("查询到的映射关系: {}", mappingKeys.size());
                if (CollectionUtils.isNotEmpty(mappingKeys)) {
                    List<String> classifyMappingKeys = mappingKeys.stream().map(AppReportCatClassifyMappingEntity::getReportName).collect(Collectors.toList());
                    mappings.addAll(classifyMappingKeys);
                }
            }
        }
        if (CollectionUtils.isEmpty(mappings)) {
            log.info("暂无映射");
            return null;
        }
        Map<String, AppPetReportDetailEntity> entityMap = detailEntityList.stream()
                .filter(row -> mappings.contains(row.getItem()))
                .collect(Collectors.toMap(row -> row.getItem() + ":" + row.getPosition(), Function.identity(), (v1, v2) -> v2));
        List<ReportResponse.Item> values = entityMap.values().stream()
                .map(row -> {
                    ReportResponse.Item item = new ReportResponse.Item();
                    item.setId(row.getId());
                    item.setGroup(row.getGroup());
                    item.setProjectName(row.getItem());
                    item.setLocusName(row.getPosition());
                    item.setResult(row.getResult());
                    item.setSort(row.getSortNo());
                    item.setCoat(row.getCoat());
                    item.setResultInterpretation(row.getResultInterpretation());
                    item.setKitNo(row.getKitsNo());
                    item.setTestDate(row.getCreateTime());
                    return item;
                })
                .collect(Collectors.toList());
        // 结果转换映射 (不需要结果转换了，因为现在同步过来的就是转换后的)
        // toMappingResult(values);
        reportResponse.setItems(values);
        return reportResponse;
    }

    /**
     * 结果转换映射
     *
     * @param values 同步过来的结果
     */
    public void toMappingResult(List<ReportResponse.Item> values) {
        if (CollectionUtils.isNotEmpty(values)) {
            List<String> positions = values.stream().map(ReportResponse.Item::getLocusName).collect(Collectors.toList());
            List<AppReportResultMappingEntity> entityList = appReportResultMappingRepository.selectByPositions(positions);
            if (CollectionUtils.isNotEmpty(entityList)) {
                Map<String, String> resultMap = entityList.stream()
                        .collect(
                                Collectors.toMap(
                                        row -> row.getPosition() + row.getSourceResult(),
                                        AppReportResultMappingEntity::getMappingResult
                                )
                        );
                for (ReportResponse.Item value : values) {
                    String key = value.getLocusName() + value.getResult();
                    String mappingResult = resultMap.get(key);
                    if (StrUtil.isNotBlank(mappingResult)) {
                        value.setResult(mappingResult);
                    }
                }
            }
        }
    }

    /**
     * 创建证书并返回证书
     *
     * @param detailIds 证书详情ID
     * @param petId     宠物ID
     * @return 返回下载路径
     */
    public List<String> createCert(List<String> detailIds, final Long petId) {
        log.info("生成证书: {} 证书明细: {}", petId, detailIds);
        List<AppPetReportDetailEntity> detailEntityList = appPetReportDetailRepository.listByIds(detailIds);
        AppPetEntity appPet = appPetRepository.selectById(petId);
        BusinessException.throwIf(Objects.isNull(appPet), "未知数据");
        Long createUser = appPet.getCreateUser();
        AppUserEntity appUser = appUserRepository.getById(createUser);
        BusinessException.throwIf(Objects.isNull(appUser), "未知繁育机构");
        List<TextPosition> textPositions = ReportUtil.buildTitle(appPet, appUser);
        try {
//            toCreateCertMappingResult(detailEntityList);
            List<String> init = ReportUtil.init(textPositions, detailEntityList);
            AppCertCreateRecordEntity certCreateRecordEntity = new AppCertCreateRecordEntity();
            certCreateRecordEntity.setPetId(petId);
            certCreateRecordEntity.setImages(String.join(",", init));
            certCreateRecordEntity.setCreateUser(createUser);
            certCreateRecordEntity.setUpdateUser(createUser);
            appCertCreateRecordRepository.saveRecord(certCreateRecordEntity, detailEntityList);
            return init;
        } catch (IOException e) {
            log.error("生成证书错误: {}", e.getMessage(), e);
            throw new BusinessException("网络错误请稍候重试！");
        }
    }


    /**
     * 结果转换映射
     *
     * @param values 同步过来的结果
     */
    public void toCreateCertMappingResult(List<AppPetReportDetailEntity> values) {
        if (CollectionUtils.isNotEmpty(values)) {
            List<String> positions = values.stream().map(AppPetReportDetailEntity::getPosition).collect(Collectors.toList());
            List<AppReportResultMappingEntity> entityList = appReportResultMappingRepository.selectByPositions(positions);
            if (CollectionUtils.isNotEmpty(entityList)) {
                Map<String, String> resultMap = entityList.stream()
                        .collect(
                                Collectors.toMap(
                                        row -> row.getPosition() + row.getSourceResult(),
                                        AppReportResultMappingEntity::getMappingResult
                                )
                        );
                for (AppPetReportDetailEntity value : values) {
                    String key = value.getPosition() + value.getResult();
                    String mappingResult = resultMap.get(key);
                    if (StrUtil.isNotBlank(mappingResult)) {
                        value.setResult(mappingResult);
                    }
                }
            }
        }
    }

    public void handleOtherSystemReport(AppAutoReportTaskEntity appAutoReportTask) {
        String reportJsonValue = appAutoReportTask.getReportJson();
        ReportDto reportValue = JSON.parseObject(reportJsonValue, ReportDto.class);
        SampleResponse sampleResponse = SampleResponse.toSampleResponse(appAutoReportTask);
        List<List<List<String>>> resultList = new ArrayList<>();
        List<ReportDto.Group> data = reportValue.getData();
        for (ReportDto.Group datum : data) {
            for (IRule rule : rules) {
                if (rule.isMatch(datum.getGroup())) {
                    resultList.addAll(rule.run(datum));
                }
            }
        }
        // 按照条件生成证书
        for (List<List<String>> list : resultList) {
            List<String> path = reportUtil.autoInit(sampleResponse, list);
            log.info("自动化报告生成证书: {}, 证书路径: {}", appAutoReportTask.getKitsNo(), path);
        }
    }
}
