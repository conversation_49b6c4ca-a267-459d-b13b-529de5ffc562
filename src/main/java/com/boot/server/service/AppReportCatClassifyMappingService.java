package com.boot.server.service;

import com.boot.server.mapper.AppReportCatClassifyMappingMapper;
import com.boot.server.repository.AppReportCatClassifyMappingRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 品种套餐指标映射(AppReportCatClassifyMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-21 00:30:55
 */
@Service
@RequiredArgsConstructor
public class AppReportCatClassifyMappingService {
    private final AppReportCatClassifyMappingRepository repository;
    private final AppReportCatClassifyMappingMapper mapper;
}

