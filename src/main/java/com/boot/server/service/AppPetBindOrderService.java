package com.boot.server.service;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boot.server.common.constant.SysConstant;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.express.ExpressOrderService;
import com.boot.server.common.express.dto.CreateOrderRequest;
import com.boot.server.common.express.dto.ExpAddress;
import com.boot.server.common.express.utils.ExpressUtil;
import com.boot.server.dto.ReceiverAddressResponse;
import com.boot.server.dto.request.BinderPetAndOrderRequest;
import com.boot.server.dto.request.ToExpressRequest;
import com.boot.server.entity.*;
import com.boot.server.enums.GoodsTypeEnum;
import com.boot.server.enums.PetBindOrderStatusEnum;
import com.boot.server.mapper.AppPetBindOrderMapper;
import com.boot.server.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 订单宠物绑定记录(AppPetBindOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-11 22:29:04
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppPetBindOrderService {
    private final AppPetBindOrderRepository repository;
    private final AppPetBindOrderMapper mapper;
    private final AppOrderDetailsRepository orderDetailsRepository;
    private final AppOrdersRepository ordersRepository;
    private final AppPetRepository appPetRepository;
    private final AppGoodsRepository appGoodsRepository;
    private final ExpressOrderService expressOrderService;
    private final AppAddressRepository appAddressRepository;
    private final AppOfflineOrderRepository appOfflineOrderRepository;

    @Transactional(rollbackFor = Exception.class)
    public Long bindPetAndOrderDetail(BinderPetAndOrderRequest request) {
        log.info("绑定: {}", JSONObject.toJSONString(request));
        // 写入绑定记录
        Long orderDetailId = request.getOrderDetailId();
        if (Objects.isNull(orderDetailId)) {
            AppOfflineOrderEntity offlineOrder = appOfflineOrderRepository.getByKitsNo(request.getKitsNo());
            if (Objects.isNull(offlineOrder)) {
                throw new BusinessException("未找到试剂盒订单, 请检查");
            }
            if (Objects.equals(offlineOrder.getStatus(), 1)) {
                throw new BusinessException("试剂盒已被使用，请检查");
            }
        }
        AppPetBindOrderEntity byKitsNo = repository.getByKitsNo(request.getKitsNo());
        if (Objects.nonNull(byKitsNo)) {
            AppPetEntity appPet = appPetRepository.getById(byKitsNo.getPetId());
            throw new BusinessException("此试剂盒已被宠物'" + appPet.getPetName() + "'绑定使用, 请检查");
        }

        if (Objects.isNull(orderDetailId)) {
            log.info("线下绑定试剂盒: {}", request.getKitsNo());
            return binderNoneOrderDetail(request);
        }
        AppOrderDetailsEntity orderDetails = orderDetailsRepository.getById(orderDetailId);
        Long goodsId = orderDetails.getGoodsId();
        AppGoodsEntity appGoods = appGoodsRepository.getById(goodsId);

        AppOrdersEntity appOrders = ordersRepository.getByOrderNo(orderDetails.getOrderNo());
        AppPetBindOrderEntity petBindOrder = new AppPetBindOrderEntity();
        if (Objects.equals(appGoods.getGoodsType(), GoodsTypeEnum.VARIETIES.getCode())) {
            log.info("绑定品种套餐: {}", orderDetails.getGoodsSpecsName());
            petBindOrder.setVarieties(orderDetails.getGoodsSpecsName());
        }
        petBindOrder.setBinderNo(IdWorker.getMillisecond() + appOrders.getId() + orderDetails.getId());
        petBindOrder.setOrderDetailId(orderDetails.getId());
        petBindOrder.setOrderNo(orderDetails.getOrderNo());
        petBindOrder.setOrderId(appOrders.getId());
        petBindOrder.setPetId(request.getPetId());
        petBindOrder.setKitsNo(request.getKitsNo());
        repository.save(petBindOrder);
        // 更新明细库存
        BusinessException.throwIf(!orderDetailsRepository.updateStock(request.getOrderDetailId()), "绑定失败请时候重试");
        // 修改宠物状态
        BusinessException.throwIf(!appPetRepository.updateStatusToBind(request.getPetId()), "绑定失败请时候重试");
        return petBindOrder.getId();
    }

    /**
     * 线下获取到的试剂盒进行绑定回寄
     *
     * @param request 绑定请求参数
     * @return 绑定记录ID
     */
    private Long binderNoneOrderDetail(BinderPetAndOrderRequest request) {
        AppOfflineOrderEntity offlineOrder = appOfflineOrderRepository.getByKitsNo(request.getKitsNo());
        Long goodsId = offlineOrder.getGoodsId();
        AppGoodsEntity appGoods = appGoodsRepository.getById(goodsId);
        AppPetBindOrderEntity petBindOrder = new AppPetBindOrderEntity();
        if (Objects.equals(appGoods.getGoodsType(), GoodsTypeEnum.VARIETIES.getCode())) {
            log.info("绑定品种套餐线下套餐默认为通用套餐");
            petBindOrder.setVarieties(SysConstant.DEFAULT_CLASSIFY);
        }
        petBindOrder.setBinderNo(IdWorker.getMillisecond() + offlineOrder.getId());
        petBindOrder.setPetId(request.getPetId());
        petBindOrder.setKitsNo(request.getKitsNo());
        repository.save(petBindOrder);
        // 修改宠物状态
        BusinessException.throwIf(!appPetRepository.updateStatusToBind(request.getPetId()), "绑定失败请时候重试");
        BusinessException.throwIf(!appOfflineOrderRepository.updateStatus(request.getKitsNo(), 1), "试剂盒状态更新失败，请检查");
        return petBindOrder.getId();
    }

    public AppPetBindOrderEntity getBinderDetail(Long petId) {
        return repository.getOne(
                Wrappers.<AppPetBindOrderEntity>lambdaQuery()
                        .eq(AppPetBindOrderEntity::getPetId, petId)
        );
    }

    /**
     * 回寄
     *
     * @param expressRequest 回寄请求参数
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean toExpress(ToExpressRequest expressRequest) {
        log.info("回寄: {}", JSONObject.toJSONString(expressRequest));
        Long petId = expressRequest.getPetId();
        AppPetBindOrderEntity entity = getPetBinderOrderEntity(petId);
        // 更新宠物状态(待收件)
        appPetRepository.updateStatusToHandle(entity.getPetId());
        // 顺丰下单物流
        String logistics = expressCreateOrder(expressRequest, entity);
        // 修改绑定状态
        entity.setStatus(PetBindOrderStatusEnum.PROCESS.getCode());
        entity.setSenderName(expressRequest.getName());
        entity.setSenderTel(expressRequest.getTel());
        entity.setSenderAddress(expressRequest.getAddress());
        entity.setLogistics(logistics);
        return repository.updateById(entity);
    }

    public AppPetBindOrderEntity getPetBinderOrderEntity(final Long petId) {
        List<AppPetBindOrderEntity> entitys = repository.list(
                Wrappers.<AppPetBindOrderEntity>lambdaQuery()
                        .eq(AppPetBindOrderEntity::getPetId, petId)
                        .eq(AppPetBindOrderEntity::getStatus, PetBindOrderStatusEnum.NONE.getCode())
        );
        BusinessException.throwIf(CollectionUtils.isEmpty(entitys), "未找到回寄套餐");
        BusinessException.throwIf(entitys.size() > 1, "宠物同时绑定了多个套餐");
        return entitys.get(0);
    }

    /**
     * 获取回寄地址展示信息
     *
     * @param petId 宠物地址
     * @return 回寄地址
     */
    public ReceiverAddressResponse getReceiverAddress(Long petId) {
        AppPetBindOrderEntity entity = getPetBinderOrderEntity(petId);
        Long goodsId;
        Long orderDetailId = entity.getOrderDetailId();
        if (Objects.nonNull(orderDetailId)) {
            AppOrderDetailsEntity orderDetails = orderDetailsRepository.getById(orderDetailId);
            goodsId = orderDetails.getGoodsId();
        } else {
            log.info("线下试剂盒绑定: {}", entity.getKitsNo());
            AppOfflineOrderEntity offlineOrderEntity = appOfflineOrderRepository.getByKitsNo(entity.getKitsNo());
            goodsId = offlineOrderEntity.getGoodsId();
        }
        AppGoodsEntity goods = appGoodsRepository.getById(goodsId);
        ReceiverAddressResponse receiverAddressResponse = new ReceiverAddressResponse();
        ReceiverAddressResponse.Address receiverAddress = new ReceiverAddressResponse.Address();
        receiverAddress.setAddress(goods.getAddress());
        receiverAddress.setTel(goods.getTel());
        receiverAddress.setName(goods.getContact());
        receiverAddressResponse.setReceiverAddress(receiverAddress);
        // 寄件人默认地址
        AppAddressEntity aDefault = appAddressRepository.getDefault();
        if (Objects.nonNull(aDefault)) {
            ReceiverAddressResponse.Address senderAddress = new ReceiverAddressResponse.Address();
            senderAddress.setName(aDefault.getName());
            senderAddress.setTel(aDefault.getMobile());
            senderAddress.setAddress(aDefault.getAddressInfo());
            receiverAddressResponse.setSenderAddress(senderAddress);
        }
        return receiverAddressResponse;
    }

    /**
     * 回寄物流下单
     * 不使用月结号
     *
     * @return 物流单号
     */
    private String expressCreateOrder(ToExpressRequest expressRequest, AppPetBindOrderEntity appPetBindOrder) {
        log.info("expressCreateOrder: {} {}", JSONObject.toJSONString(expressRequest), JSONObject.toJSONString(appPetBindOrder));
        CreateOrderRequest createOrderRequest = new CreateOrderRequest();
        // 收件人地址 寄件人地址为expressRequest中的信息
        ReceiverAddressResponse receiverAddress = getReceiverAddress(expressRequest.getPetId());
        createOrderRequest.setCargoDetails(CreateOrderRequest.getCargoDetails());
        createOrderRequest.setOrderId(appPetBindOrder.getBinderNo());
        ArrayList<ExpAddress> expAddresses = new ArrayList<>();
        ExpAddress expAddress = new ExpAddress();
        expAddress.setContactType(1);
        expAddress.setContact(expressRequest.getName());
        expAddress.setTel(expressRequest.getTel());
        expAddress.setAddress(expressRequest.getAddress());
        expAddresses.add(expAddress);
        // 收件人
        ExpAddress expAddress2 = new ExpAddress();
        expAddress2.setContactType(2);
        ReceiverAddressResponse.Address receiverAddressInfo = receiverAddress.getReceiverAddress();
        expAddress2.setContact(receiverAddressInfo.getName());
        expAddress2.setTel(receiverAddressInfo.getTel());
        expAddress2.setAddress(receiverAddressInfo.getAddress());
        expAddresses.add(expAddress2);
        createOrderRequest.setContactInfoList(expAddresses);
        createOrderRequest.setPayMethod(2);
        try {
            JSONObject order = expressOrderService.createOrder(createOrderRequest);
            return ExpressUtil.getWaybillNo(order);
        } catch (Exception e) {
            log.error("{}", e.getMessage(), e);
            if (e instanceof BusinessException) {
                throw (BusinessException) e;
            }
            throw new BusinessException("顺丰下单异常");
        }
    }
}

