package com.boot.server.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.util.BeanUtil;
import com.boot.server.dto.AdminUserResponse;
import com.boot.server.dto.request.AdminUserRequest;
import com.boot.server.entity.AppUserEntity;
import com.boot.server.mapper.AppUserMapper;
import com.boot.server.repository.AppUserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户表(AppUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-02 22:12:30
 */
@Service
@RequiredArgsConstructor
public class AppUserService {
    private final AppUserRepository repository;
    private final AppUserMapper mapper;

    /**
     * 获取用户信息
     *
     * @param adminUserRequest 查询条件
     * @return 分页用户信息
     */
    public Page<AdminUserResponse> selectUserList(AdminUserRequest adminUserRequest) {
        Page<AppUserEntity> page = repository.page(
                adminUserRequest.getPage(),
                Wrappers.<AppUserEntity>lambdaQuery()
                        .like(AppUserEntity::getNickName, adminUserRequest.getNickName())
                        .orderByDesc(AppUserEntity::getId)
        );
        Page<AdminUserResponse> result = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        List<AppUserEntity> records = page.getRecords();
        List<AdminUserResponse> adminUserResponses = BeanUtil.copyToList(records, AdminUserResponse.class);
        result.setRecords(adminUserResponses);
        return result;
    }
}

