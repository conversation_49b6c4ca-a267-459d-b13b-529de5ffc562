package com.boot.server.service;

import cn.hutool.core.util.StrUtil;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.config.FileUploadConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

/**
 * 文件服务类
 */
@Service
public class FileService {

    @Autowired
    private FileUploadConfig fileUploadConfig;

    /**
     * 上传文件
     * 注意：此实现不会实际存储文件，只返回相对路径
     *
     * @param file 文件
     * @return 文件访问路径
     * @throws IOException IO异常
     */
    public String uploadFile(MultipartFile file) throws IOException {
        // 检查文件是否为空
        if (file.isEmpty()) {
            throw new BusinessException("上传的文件不能为空");
        }

        // 检查文件大小
        long fileSize = file.getSize() / 1024 / 1024; // 转换为MB
        if (fileSize > fileUploadConfig.getMaxSize()) {
            throw new BusinessException("文件大小超过限制，最大允许" + fileUploadConfig.getMaxSize() + "MB");
        }

        // 检查文件类型
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        if (!isAllowedFileType(fileExtension)) {
            throw new BusinessException("不支持的文件类型");
        }
        // 生成新的文件名
        String newFileName = UUID.randomUUID().toString().replace("-", "") + "." + fileExtension;

        // 构建存储路径
        String storePath = fileUploadConfig.getBasePath();
        String fullPath = fileUploadConfig.getBasePath() + "/" + newFileName;

        // 创建目录
        File directory = new File(storePath);
        if (!directory.exists()) {
            if (!directory.mkdirs()) {
                throw new BusinessException("创建目录失败");
            }
        }

        // 保存文件
        File destFile = new File(fullPath);
        file.transferTo(destFile);

        // 返回可访问的URL路径
        return fileUploadConfig.getAccessUrlPrefix() + "/" + newFileName;
    }


    /**
     * 获取文件的相对路径
     *
     * @param fileUrl 文件URL路径
     * @return 文件的相对路径
     */
    public String getFilePath(String fileUrl) {
        if (StringUtils.hasText(fileUrl) && fileUrl.startsWith(fileUploadConfig.getAccessUrlPrefix())) {
            // 从 URL 中提取相对路径
            return fileUrl.substring(fileUploadConfig.getAccessUrlPrefix().length());
        }
        throw new BusinessException("无效的文件路径");
    }

    /**
     * 获取文件的完整物理路径
     *
     * @param fileUrl 文件URL路径
     * @return 文件的完整物理路径
     */
    public String getFullFilePath(String fileUrl) {
        // 获取相对路径
        String relativePath = getFilePath(fileUrl);
        // 返回完整的物理路径
        return fileUploadConfig.getBasePath() + relativePath;
    }

    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名（不包含点）
     */
    private String getFileExtension(String filename) {
        if (filename == null) {
            return "";
        }
        int dotIndex = filename.lastIndexOf('.');
        if (dotIndex == -1 || dotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(dotIndex + 1).toLowerCase();
    }

    /**
     * 检查文件类型是否允许上传
     *
     * @param fileExtension 文件扩展名
     * @return 是否允许
     */
    private boolean isAllowedFileType(String fileExtension) {
        if (!StringUtils.hasText(fileExtension)) {
            return false;
        }
        List<String> allowTypes = Arrays.asList(fileUploadConfig.getAllowTypes().split(","));
        return allowTypes.contains(fileExtension.toLowerCase());
    }

    /**
     * 读取文件内容
     *
     * @param fileUrl 文件URL路径
     * @return 文件内容（Base64编码）
     * @throws IOException IO异常
     */
    public String readFileContent(String fileUrl) throws IOException {
        // 获取文件的物理路径
        String filePath = getFilePath(fileUrl);

        // 构建完整的文件路径
        Path path = Paths.get(fileUploadConfig.getBasePath() + filePath);
        File file = path.toFile();

        // 检查文件是否存在
        if (!file.exists() || !file.isFile()) {
            throw new BusinessException("文件不存在");
        }

        // 读取文件内容并转换为Base64编码
        byte[] fileContent = Files.readAllBytes(path);
        return Base64.getEncoder().encodeToString(fileContent);
    }
}
