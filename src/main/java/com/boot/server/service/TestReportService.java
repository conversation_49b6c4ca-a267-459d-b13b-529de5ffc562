package com.boot.server.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.boot.server.common.constant.SysConstant;
import com.boot.server.common.exception.RException;
import com.boot.server.common.util.ImgUtil;
import com.boot.server.dto.QueryReportResponse;
import com.boot.server.dto.request.QueryReportRequest;
import com.boot.server.dto.request.ReportDto;
import com.boot.server.entity.AppAutoReportTaskEntity;
import com.boot.server.repository.AppAutoReportTaskRepository;
import com.boot.server.repository.TestReportRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TestReportService
 *
 * <AUTHOR> 2025/7/18 17:36
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TestReportService {
    private final TestReportRepository testReportRepository;
    private final AppAutoReportTaskRepository appAutoReportTaskRepository;
    private final ImgUtil imgUtil;

    public QueryReportResponse getReport(QueryReportRequest request) {
        QueryReportResponse response = new QueryReportResponse();
        QueryReportResponse.ReportDataResponse dataResponse = testReportRepository.getReport(request.getOrderNo());
        RException.throwIf(StrUtil.isBlank(dataResponse.getDeviceId()), "未获取到报告数据");
        // 获取报告列表
        AppAutoReportTaskEntity reportTask = appAutoReportTaskRepository.getByKitsNo(dataResponse.getDeviceId());
        Integer testStatus = getTestStatus(reportTask);
        dataResponse.setTestState(testStatus);
        response.setData(Collections.singletonList(dataResponse));
        if (Objects.nonNull(reportTask)) {
            List<QueryReportResponse.TestResultList> testResultList = getTestResultList(reportTask);
            dataResponse.setTestResultList(testResultList);
        }
        return response;
    }

    /**
     * 1等待检测，2检测中，3检测完成，4已取消
     *
     * @param reportTask 检测任务实体
     * @return 检测状态
     */
    private Integer getTestStatus(AppAutoReportTaskEntity reportTask) {
        if (reportTask == null) {
            return 1;
        }
        log.info("检测任务 {}", reportTask.getId());
        // 0 未处理 1处理中 2处理完成 3 处理失败
        switch (reportTask.getStatus()) {
            case 0:
            case 3:
                return 1;
            case 1:
                return 2;
            case 2:
                return 3;
            default:
                throw new RException("未知的检测状态");
        }
    }

    private List<QueryReportResponse.TestResultList> getTestResultList(AppAutoReportTaskEntity reportTask) {
        if (Objects.equals(reportTask.getStatus(), SysConstant.AUTO_REPORT_DONE)) {
            String reportJson = reportTask.getReportJson();
            String kitsNo = reportTask.getKitsNo();
            ReportDto reportDto = JSON.parseObject(reportJson, ReportDto.class);
            List<ReportDto.Group> data = reportDto.getData();
            return data.stream()
                    .filter(row -> !Objects.equals(row.getGroup(), SysConstant.POSITION_GROUP))
                    .map(row -> groupItemToTestResultList(row, kitsNo))
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public List<QueryReportResponse.TestResultList> groupItemToTestResultList(ReportDto.Group data, final String kitsNo) {
        List<ReportDto.ReportDetail> items = data.getItems();
        return items.stream().map(item -> {
                    QueryReportResponse.TestResultList testResultList = new QueryReportResponse.TestResultList();
                    testResultList.setProjectAffiliation(groupToProjectAffiliation(data.getGroup()));
                    testResultList.setTestingItem(item.getItem());
                    testResultList.setLocusName(item.getPosition());
                    testResultList.setMutationStatus(item.getResult());
                    testResultList.setModeOfInheritance(item.getModeOfInheritance());
                    testResultList.setReportUrl(imgUtil.getOssReportImage(kitsNo + "/" + item.getItem()));
                    return testResultList;
                })
                .collect(Collectors.toList());
    }

    private String groupToProjectAffiliation(final String group) {
        switch (group) {
            case "疾病":
                return "遗传病";
            case "毛色":
            case "毛型":
            case "毛长":
            case "性状":
                return "毛色毛型基因";
            case "血型":
                return "血型基因";
            // 基因身份证
            // case "基因身份证":
            //    return "基因身份证";
            default:
                return "未知";
        }
    }
}
