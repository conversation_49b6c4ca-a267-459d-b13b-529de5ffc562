package com.boot.server.service;

import com.boot.server.mapper.AppPetReportDetailMapper;
import com.boot.server.repository.AppPetReportDetailRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 试剂盒报告(AppPetReportDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14 16:41:58
 */
@Service
@RequiredArgsConstructor
public class AppPetReportDetailService {
    private final AppPetReportDetailRepository repository;
    private final AppPetReportDetailMapper mapper;


}

