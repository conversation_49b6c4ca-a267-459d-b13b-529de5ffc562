package com.boot.server.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaCodeLineColor;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.config.AppConfig;
import com.boot.server.entity.AppUserEntity;
import com.boot.server.repository.AppUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * WxService
 *
 * <AUTHOR> 2025/7/23 11:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxService {
    private final WxMaService wxMaService;
    private final AppConfig appConfig;
    private final AppUserRepository appUserRepository;


    /**
     * 生成微信小程序二维码
     */
    public String generateQrCode() {
        Long currentUserId = AuthUtil.getCurrentUserId();
        AppUserEntity appUser = appUserRepository.getById(currentUserId);
        String shareCodeFile = appUser.getShareCodeFile();
        // 如果已经存在二维码文件，直接返回
        if (StrUtil.isNotBlank(shareCodeFile)) {
            return shareCodeFile;
        }
        String shareUid = appUser.getShareUid();
        try {
            // 生成文件名
            String shareCodeName = shareUid + ".png";
            String codeFilePath = appConfig.getShareCodeFilePath() + File.separator + shareCodeName;
            // 生成微信小程序二维码
            byte[] wxaCodeUnlimit = wxMaService.getQrcodeService().createWxaCodeUnlimitBytes(
                    shareUid, // 场景值
                    appConfig.getShareAppletPath(), // 小程序页面路径
                    true, // 检查 page 是否存在 为 false会限制只能生成 6000张图
                    appConfig.getAppEnv(), // 环境标识
                    200, // 二维码宽度
                    true, // 自动设置线条颜色
                    new WxMaCodeLineColor(), // 线条颜色
                    true // 是否需要透明背景
            );
            FileUtil.writeBytes(wxaCodeUnlimit, codeFilePath);
            String apiFilePath = appConfig.getShareCodeFileUrl() + File.separator + shareCodeName;
            appUser.setShareCodeFile(appConfig.getShareCodeFileUrl() + File.separator + shareCodeName);
            appUserRepository.updateById(appUser);
            // 返回二维码文件路径
            return apiFilePath;
        } catch (WxErrorException e) {
            log.error("{}", e.getMessage(), e);
            throw new BusinessException("生成邀请码错误");
        }
    }

}
