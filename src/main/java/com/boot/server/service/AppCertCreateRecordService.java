package com.boot.server.service;

import com.boot.server.mapper.AppCertCreateRecordMapper;
import com.boot.server.repository.AppCertCreateRecordRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 证书创建记录表(AppCertCreateRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-23 14:34:53
 */
@Service
@RequiredArgsConstructor
public class AppCertCreateRecordService {
    private final AppCertCreateRecordRepository repository;
    private final AppCertCreateRecordMapper mapper;
}

