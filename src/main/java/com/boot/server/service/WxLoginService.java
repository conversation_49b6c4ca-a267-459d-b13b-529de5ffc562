package com.boot.server.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.config.AppConfig;
import com.boot.server.config.JwtConfig;
import com.boot.server.dto.LoginResultResponse;
import com.boot.server.dto.request.WxLoginRequest;
import com.boot.server.entity.AppUserEntity;
import com.boot.server.repository.AppUserIntegralRecordRepository;
import com.boot.server.repository.AppUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序登录服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WxLoginService {

    private final WxMaService wxMaService;
    private final JwtConfig jwtConfig;
    private final AppUserRepository appUserRepository;
    private final AppConfig appConfig;
    private final AppUserIntegralRecordRepository appUserIntegralRecordRepository;

    /**
     * 微信小程序登录
     *
     * @param wxLoginDTO 微信登录参数
     * @return 登录结果
     */
    @Transactional(rollbackFor = Exception.class)
    public LoginResultResponse wxLogin(WxLoginRequest wxLoginDTO) {
        String shareCode = wxLoginDTO.getShareCode();
        // 1. 获取微信用户openid
        String openid;
        try {
            WxMaJscode2SessionResult sessionResult = wxMaService.getUserService().getSessionInfo(wxLoginDTO.getCode());
            openid = sessionResult.getOpenid();
            // 可选：获取unionid
            String unionid = sessionResult.getUnionid();
            log.info("微信登录成功，openid: {}, unionid: {}", openid, unionid);
        } catch (WxErrorException e) {
            log.error("微信登录失败", e);
            throw new BusinessException("微信登录失败，请稍后再试");
        }

        // 2. 根据openid查询用户
        AppUserEntity user = appUserRepository.getOne(new LambdaQueryWrapper<AppUserEntity>().eq(AppUserEntity::getOpenId, openid));
        boolean isNewUser = false;

        // 3. 如果用户不存在，新建用户
        if (user == null) {
            user = new AppUserEntity();
            user.setOpenId(openid);
            user.setShareUid(IdWorker.get32UUID());
            user.setInviteShareCode(shareCode);
            user.setNickName("微信用户");
            user.setAvatar("/api/file/default.png");
            appUserRepository.save(user);
            // 检查是否存在分享码，如果存在则说明当前用户是被邀请注册的。给邀请用户加积分
            if (StrUtil.isNotBlank(shareCode)) {
                // 邀请人信息
                AppUserEntity inviteAppUser = appUserRepository.getByShareCode(shareCode);
                if (inviteAppUser != null) {
                    // 统计该邀请人的邀请次数
                    long inviteCount = appUserRepository.countByInviteShareCode(inviteAppUser.getShareUid());
                    if (inviteCount <= appConfig.getInviteRewardCount()) {
                        // 邀请人加积分
                        appUserIntegralRecordRepository.addIntegralRecord(
                                inviteAppUser.getId(),
                                appConfig.getInviteIntegral(),
                                "邀请新用户注册奖励积分"
                        );
                    } else {
                        log.info("用户 {} 邀请奖励已达上限10次", inviteAppUser.getId());
                    }
                }
            }
            // 只要是新用户注册均送 1000 积分（有效期一个月）
            appUserIntegralRecordRepository.addExprTimeIntegralRecord(
                    user.getId(),
                    appConfig.getNewUserIntegral(),
                    "新用户注册奖励积分"
            );
            isNewUser = true;
        }

        // 5. 生成JWT令牌
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId());
        claims.put("openid", user.getOpenId());
        String token = jwtConfig.generateToken(claims);

        // 7. 返回登录结果
        return LoginResultResponse.builder()
                .userId(user.getId())
                .nickname(user.getNickName())
                .avatarUrl(user.getAvatar())
                .token(token)
                .isNewUser(isNewUser)
                .build();
    }

}
