package com.boot.server.service;

import com.beust.ah.A;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.dto.OrderDetailResponse;
import com.boot.server.mapper.AppOrderDetailsMapper;
import com.boot.server.repository.AppOrderDetailsRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 用户订单明细(AppOrderDetails)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Service
@RequiredArgsConstructor
public class AppOrderDetailsService {
    private final AppOrderDetailsRepository repository;
    private final AppOrderDetailsMapper mapper;

    public List<OrderDetailResponse> selectListPayedAndNotUsed() {
        Long currentUserId = AuthUtil.getCurrentUserId();
        return mapper.selectListPayedAndNotUsed(currentUserId);
    }
}

