package com.boot.server.service;

import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.mapper.AppGoodsMapper;
import com.boot.server.repository.AppGoodsRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 商品表(AppGoods)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-04 18:40:00
 */
@Service
@RequiredArgsConstructor
public class AppGoodsService {
    private final AppGoodsRepository repository;
    private final AppGoodsMapper mapper;
}

