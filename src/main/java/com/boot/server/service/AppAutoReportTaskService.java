package com.boot.server.service;

import com.alibaba.fastjson2.JSON;
import com.boot.server.common.constant.SysConstant;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.dto.SampleResponse;
import com.boot.server.dto.request.ReportDto;
import com.boot.server.entity.AppAutoReportTaskEntity;
import com.boot.server.mapper.AppAutoReportTaskMapper;
import com.boot.server.repository.AppAutoReportTaskRepository;
import com.boot.server.repository.SampleRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 自动化报告任务(AppAutoReportTask)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18 10:59:50
 */
@Service
@RequiredArgsConstructor
public class AppAutoReportTaskService {
    private final AppAutoReportTaskRepository repository;
    private final AppAutoReportTaskMapper mapper;
    private final SampleRepository sampleRepository;

    public void saveOrUpdate(String reportJsonValue) {
        ReportDto reportDto = JSON.parseObject(reportJsonValue, ReportDto.class);
        String serialNo = reportDto.getSerialNo();
        AppAutoReportTaskEntity taskEntity = getByKitsNo(serialNo);
        if (taskEntity == null) {
            SampleResponse sampleResponse = sampleRepository.selectSampleByDeviceId(serialNo);
            taskEntity = new AppAutoReportTaskEntity();
            taskEntity.setKitsNo(serialNo);
            taskEntity.setOrganization(sampleResponse.getOrganization());
            taskEntity.setPetNo(sampleResponse.getPetNo());
            taskEntity.setPetSex(sampleResponse.getPetSex());
            taskEntity.setPetBreed(sampleResponse.getPetBreed());
            taskEntity.setReportJson(reportJsonValue);
            taskEntity.setStatus(SysConstant.AUTO_REPORT_NONE);
        } else {
            taskEntity.setReportJson(reportJsonValue);
            taskEntity.setStatus(SysConstant.AUTO_REPORT_NONE);
        }
        BusinessException.throwIf(!repository.saveOrUpdate(taskEntity), "报告任务保存失败");
    }

    public AppAutoReportTaskEntity getByKitsNo(String kitsNo) {
        return repository.lambdaQuery().eq(AppAutoReportTaskEntity::getKitsNo, kitsNo).one();
    }
}

