package com.boot.server.service;

import com.boot.server.mapper.AppGoodsSpecsMapper;
import com.boot.server.repository.AppGoodsSpecsRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 商品规格表(AppGoodsSpecs)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-04 20:26:02
 */
@Service
@RequiredArgsConstructor
public class AppGoodsSpecsService {
    private final AppGoodsSpecsRepository repository;
    private final AppGoodsSpecsMapper mapper;
}

