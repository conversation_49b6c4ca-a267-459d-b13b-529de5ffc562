package com.boot.server.service;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.express.ExpressOrderService;
import com.boot.server.common.express.dto.ExpAddress;
import com.boot.server.common.express.dto.PreOrderRequest;
import com.boot.server.common.express.enums.ContactTypeEnum;
import com.boot.server.common.express.utils.ExpressUtil;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.common.util.BeanUtil;
import com.boot.server.common.util.PriceUtil;
import com.boot.server.config.AppConfig;
import com.boot.server.config.ExpressConfig;
import com.boot.server.config.pay.WxPayBean;
import com.boot.server.dto.CalcOrderResponse;
import com.boot.server.dto.GoodsResponse;
import com.boot.server.dto.OrderListResponse;
import com.boot.server.dto.PushOrderResponse;
import com.boot.server.dto.request.CalcOrderRequest;
import com.boot.server.dto.request.OrderListRequest;
import com.boot.server.entity.*;
import com.boot.server.enums.OrderStatusEnum;
import com.boot.server.enums.PayStatusEnum;
import com.boot.server.mapper.AppOrdersMapper;
import com.boot.server.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户订单表(AppOrders)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppOrdersService {
    private final AppOrdersRepository repository;
    private final AppOrdersMapper mapper;
    private final AppOrderDetailsRepository appOrderDetailsRepository;
    private final AppGoodsRepository appGoodsRepository;
    private final AppAddressRepository appAddressRepository;
    private final AppGoodsSpecsRepository appGoodsSpecsRepository;
    private final AppShoppingCartRepository appShoppingCartRepository;
    private final ExpressConfig expressConfig;
    private final ExpressOrderService expressOrderService;
    private final AppDepotAddressRepository appDepotAddressRepository;
    private final WxPayBean wxPayBean;
    private final AppUserRepository appUserRepository;
    private final ExpressHelper expressHelper;
    private final AppUserIntegralRecordRepository appUserIntegralRecordRepository;
    private final AppConfig appConfig;


    /**
     * 购买前计算订单信息
     *
     * @param calcOrderRequest 请求参数
     * @return 订单信息
     */
    public CalcOrderResponse selectCalcOrder(CalcOrderRequest calcOrderRequest) {
        CalcOrderResponse response = new CalcOrderResponse();
        List<CalcOrderRequest.GoodsDto> goods = calcOrderRequest.getGoods();
        List<Long> cartIds = calcOrderRequest.getCartIds();
        if (CollectionUtils.isNotEmpty(cartIds)) {
            List<AppShoppingCartEntity> shoppingCartEntityList = appShoppingCartRepository.listByIds(cartIds);
            goods = shoppingCartEntityList.stream().map(row -> {
                CalcOrderRequest.GoodsDto goodsDto = new CalcOrderRequest.GoodsDto();
                goodsDto.setId(row.getGoodsId());
                goodsDto.setSpecs(row.getSpecs());
                goodsDto.setNumber(row.getNum());
                return goodsDto;
            }).collect(Collectors.toList());
        }
        BusinessException.throwIf(CollectionUtils.isEmpty(goods), "网络错误，请稍候重试");

        List<Long> goodsIds = goods.stream().map(CalcOrderRequest.GoodsDto::getId).collect(Collectors.toList());
        AppAddressEntity addressEntity;
        Long addressId = calcOrderRequest.getAddressId();
        if (addressId != null) {
            addressEntity = appAddressRepository.getById(addressId);
        } else {
            addressEntity = appAddressRepository.getDefault();
        }
        // 设置地址
        response.setEntityAddress(addressEntity);
        // 查询套餐
        List<AppGoodsEntity> goodsEntityList = appGoodsRepository.listByIds(goodsIds);
        calculateOrderAmount(response, goodsEntityList, goods);
        response.setCartIds(calcOrderRequest.getCartIds());
        response.setGoodsIds(goodsIds);
        List<GoodsResponse> goodsResponse = this.getGoodsResponse(goodsEntityList, goods);
        response.setGoodsList(goodsResponse);
        // 设置积分可抵扣值
        AppUserEntity appUser = appUserRepository.getById(AuthUtil.getCurrentUserId());
        // 按照现价计算可用积分
        BigDecimal amount = response.getAmount();
        // 总积分
        Long appUserIntegral = appUser.getIntegral();
        response.setSumIntegral(appUserIntegral);
        // 订单可用积分
        Long integral = PriceUtil.getDeductionIntegral(amount);
        response.setIntegral(integral <= appUser.getIntegral() ? integral : appUserIntegral);
        // 可抵扣金额
        response.setIntegralAmount(PriceUtil.integralToAmount(response.getIntegral()));
        // 是否为第一次下单(第一次下单抵扣积分不能超过金额的 20%)
        response.setHasFirstOrder(repository.hasFirstOrder(AuthUtil.getCurrentUserId()));
        return response;
    }

    /**
     * 计算总价和原价
     */
    public void calculateOrderAmount(CalcOrderResponse response, List<AppGoodsEntity> goodsEntityList, List<CalcOrderRequest.GoodsDto> goodsList) {
        Map<Long, AppGoodsEntity> goodsMap = goodsEntityList.stream().collect(Collectors.toMap(AppGoodsEntity::getId, Function.identity()));
        if (CollectionUtils.isNotEmpty(goodsList)) {
            BigDecimal sourceAmount = goodsList.stream()
                    // 计算原价格
                    .map(row -> {
                        AppGoodsEntity goods = goodsMap.get(row.getId());
                        return calcGoodsAmount(goods.getSourceAmount(), goods.getSourceAmount(), row.getNumber(), goods.getGoodsType(), StrUtil.split(row.getSpecs(), ","));
                    })
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            BigDecimal amount = goodsList.stream()
                    // 计算价格
                    .map(row -> {
                        AppGoodsEntity goods = goodsMap.get(row.getId());
                        return calcGoodsAmount(goods.getAmount(), goods.getIncrPrice(), row.getNumber(), goods.getGoodsType(), StrUtil.split(row.getSpecs(), ","));
                    })
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            response.setSourceAmount(sourceAmount.setScale(2, RoundingMode.HALF_UP));
            response.setAmount(amount.setScale(2, RoundingMode.HALF_UP));
        }
    }

    /**
     * 计算价格每个商品的价格
     */
    private BigDecimal calcGoodsAmount(BigDecimal amount, BigDecimal incrPrice, Integer number, Integer goodsType, List<String> specs) {
        // 随心选
        if (Objects.equals(goodsType, 1)) {
            if (specs.size() > 1) {
                return amount
                        .add(new BigDecimal(specs.size() - 1).multiply(incrPrice))
                        .multiply(new BigDecimal(number))
                        .setScale(2, RoundingMode.HALF_UP);
            }
        }
        return amount.multiply(new BigDecimal(number)).setScale(2, RoundingMode.HALF_UP);
    }

    /**
     * 构建订单商品信息
     *
     * @param goodsEntityList 商品列表
     */
    private List<GoodsResponse> getGoodsResponse(
            List<AppGoodsEntity> goodsEntityList,
            List<CalcOrderRequest.GoodsDto> goods
    ) {
        if (CollectionUtils.isNotEmpty(goodsEntityList)) {
            // 获取选中规格
            List<Long> specsIds = goods.stream()
                    .map(CalcOrderRequest.GoodsDto::getSpecs)
                    .map(row -> StrUtil.split(row, ","))
                    .flatMap(List::stream)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            // 查询规格
            Map<Long, String> mapName = appGoodsSpecsRepository.getMapName(specsIds);
            Map<Long, String> specsClassifyMap = appGoodsSpecsRepository.getSpecsClassifyMap(specsIds);
            Map<Long, AppGoodsEntity> goodsMap = goodsEntityList.stream()
                    .collect(Collectors.toMap(AppGoodsEntity::getId, Function.identity()));
            List<GoodsResponse> goodsResponseList = new ArrayList<>();
            // 构建订单商品明细
            for (CalcOrderRequest.GoodsDto good : goods) {
                Long goodsId = good.getId();
                AppGoodsEntity appGoodsEntity = goodsMap.get(goodsId);
                GoodsResponse response = appGoodsEntity.toResponse(good.getNumber());
                List<Long> goodsSpecsIds = StrUtil.split(good.getSpecs(), ",")
                        .stream().map(Long::parseLong)
                        .collect(Collectors.toList());

                Map<String, List<String>> map = new HashMap<>();
                for (Long goodsSpecsId : goodsSpecsIds) {
                    String specsName = mapName.get(goodsSpecsId);
                    // 分类
                    String classifyName = specsClassifyMap.get(goodsSpecsId);
                    List<String> goodsSpecsMap = map.getOrDefault(classifyName, new ArrayList<>());
                    goodsSpecsMap.add(specsName);
                    map.put(classifyName, goodsSpecsMap);
                }
                List<String> specsNames = map.values().stream().flatMap(List::stream).collect(Collectors.toList());
                response.setSpecsNames(String.join(",", specsNames));
                response.setSpecsNameIds(good.getSpecs());
                response.setSpecsNamesMap(map);
                response.setGoodsType(appGoodsEntity.getGoodsType());
                goodsResponseList.add(response);
            }
            return goodsResponseList;
        }
        throw new BusinessException("网络异常请稍后重试");
    }

    /**
     * 生成订单返回订单号
     *
     * @param calcOrderResponse 订单
     * @return 订单号
     */
    @Transactional(rollbackFor = Exception.class)
    public PushOrderResponse pushOrder(CalcOrderResponse calcOrderResponse) {
        // 设置订单信息
        AppOrdersEntity orders = new AppOrdersEntity();
        orders.setOrderAmount(calcOrderResponse.getAmount());
        orders.setOrderSourceAmount(calcOrderResponse.getSourceAmount());
        orders.setPayAmount(calcOrderResponse.getAmount());
        CalcOrderResponse.AddressResponse address = calcOrderResponse.getAddress();
        orders.setName(address.getName());
        orders.setMobile(address.getMobile());
        orders.setProvince(address.getProvince());
        orders.setCity(address.getCity());
        orders.setDistrict(address.getDistrict());
        orders.setAddress(address.getAddress());
        orders.setOrderNo(getOrderNo());
        orders.setRemark(calcOrderResponse.getRemark());
        if (BooleanUtil.isTrue(calcOrderResponse.getIntegralPay())) {
            log.info("使用积分抵扣订单金额: {} 积分: {}", calcOrderResponse.getIntegralAmount(), calcOrderResponse.getIntegral());
            orders.setIntegral(calcOrderResponse.getIntegral());
            BigDecimal integralAmount = PriceUtil.integralToAmount(calcOrderResponse.getIntegral());
            orders.setIntegralAmount(integralAmount);
            // 首单下单校验积分只允许使用，支付金额不能小于99
            if (repository.hasFirstOrder(AuthUtil.getCurrentUserId())) {
                // 如果订单原价小于99元，不允许使用积分抵扣
                if (calcOrderResponse.getAmount().compareTo(appConfig.getFirstBuyAmount()) < 0) {
                    throw new BusinessException(String.format("首单订单金额小于%s元，不支持积分抵扣", appConfig.getFirstBuyAmount().toString()));
                }
                BigDecimal payAmount = calcOrderResponse.getAmount().subtract(integralAmount);
                if (payAmount.compareTo(appConfig.getFirstBuyAmount()) < 0) {
                    throw new BusinessException(String.format("首单支付金额不能小于%s元", appConfig.getFirstBuyAmount().toString()));
                }
            }
            orders.setPayAmount(calcOrderResponse.getAmount().subtract(integralAmount));
            log.info("订单号: {} 使用积分抵扣后支付金额: {}", orders.getOrderNo(), orders.getPayAmount());
            // 直接扣除积分
            appUserIntegralRecordRepository.addIntegralRecord(
                    AuthUtil.getCurrentUserId(),
                    -orders.getIntegral(),
                    "支付订单号: " + orders.getOrderNo() + " 抵扣积分"
            );
        }
        // 预下单接口调用检测地址是否可用
        preOrderLogistics(orders);
        repository.save(orders);
        // 设置订单明细
        List<GoodsResponse> goodsList = calcOrderResponse.getGoodsList();
        this.pushOrderDetail(orders, goodsList);
        // 删除购物车数据
        if (CollectionUtils.isNotEmpty(calcOrderResponse.getCartIds())) {
            appShoppingCartRepository.removeBatchByIds(calcOrderResponse.getCartIds());
        }

        // 支付金额为 0
        if (orders.getPayAmount().compareTo(BigDecimal.ZERO) == 0) {
            // 如果订单金额为0 则直接设置为已支付状态
            orders.setPayStatus(PayStatusEnum.SUCCESS.getCode());
            orders.setOrderStatus(OrderStatusEnum.SUCCESS.getCode());
            orders.setPayTime(LocalDateTime.now());
            log.info("订单号: {} 金额为0, 直接设置为已支付状态", orders.getOrderNo());
            // 物流下单
            String expressOrderNo = expressHelper.createExpressOrder(orders);
            // 更新物流单号
            orders.setLogistics(expressOrderNo);
            repository.updateById(orders);
            if (orders.getIntegral() > 0) {
                appUserIntegralRecordRepository.addIntegralRecord(
                        orders.getCreateUser(),
                        -orders.getIntegral(),
                        "支付订单号: " + orders.getOrderNo() + " 抵扣积分"
                );
            }
            return PushOrderResponse.nonePay(orders.getOrderNo());
        }
        return PushOrderResponse.toPay(orders.getOrderNo());
    }

    /**
     * 商品下单物流预下单
     *
     * @param appOrders 订单信息
     */
    private void preOrderLogistics(AppOrdersEntity appOrders) {
        PreOrderRequest request = new PreOrderRequest();
        List<ExpAddress> expAddressList = new ArrayList<>();
        // 寄件人为商家地址
        ExpAddress senderAddress = appDepotAddressRepository.getDefaultAddressToExpAddress(ContactTypeEnum.SENDER);
        expAddressList.add(senderAddress);
        // 收件人为客户地址
        ExpAddress receiverAddress = new ExpAddress();
        receiverAddress.setContact(appOrders.getName());
        receiverAddress.setTel(appOrders.getMobile());
        receiverAddress.setProvince(appOrders.getProvince());
        receiverAddress.setCity(appOrders.getCity());
        receiverAddress.setAddress(appOrders.getAddress());
        receiverAddress.setContactType(ContactTypeEnum.RECEIVER.getCode());
        expAddressList.add(receiverAddress);
        request.setOrderId(appOrders.getOrderNo());
        request.setContactInfoList(expAddressList);
        request.setMonthlyCard(expressConfig.getMonthlyCard());
        JSONObject jsonObject = expressOrderService.preOrder(request);
        // 校验是否请求成功
        ExpressUtil.getApiResultData(jsonObject);
    }

    private String getOrderNo() {
        String orderNoPrefix = wxPayBean.getOrderNoPrefix();
        if (StrUtil.isNotBlank(orderNoPrefix)) {
            return orderNoPrefix + IdWorker.getMillisecond() + String.format("%03d", AuthUtil.getCurrentUserId());
        }
        return IdWorker.getMillisecond() + String.format("%03d", AuthUtil.getCurrentUserId());
    }

    /**
     * 保存订单详情
     *
     * @param orders    订单
     * @param goodsList 商品列表
     */
    private void pushOrderDetail(AppOrdersEntity orders, List<GoodsResponse> goodsList) {
        String orderNo = orders.getOrderNo();
        List<AppOrderDetailsEntity> orderDetailsEntityList = new ArrayList<>();
        // 获取选中规格
        List<Long> specsIds = goodsList.stream()
                .map(GoodsResponse::getSpecsNameIds)
                .map(row -> StrUtil.split(row, ","))
                .flatMap(List::stream)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        Map<Long, String> mapName = appGoodsSpecsRepository.getMapName(specsIds);
        Map<Long, String> specsClassifyMap = appGoodsSpecsRepository.getSpecsClassifyMap(specsIds);
        for (GoodsResponse goodsResponse : goodsList) {
            List<Long> goodsSpecsIds = StrUtil.split(goodsResponse.getSpecsNameIds(), ",")
                    .stream().map(Long::parseLong)
                    .collect(Collectors.toList());

            Map<String, List<String>> map = new HashMap<>();
            for (Long goodsSpecsId : goodsSpecsIds) {
                String specsName = mapName.get(goodsSpecsId);
                // 分类
                String classifyName = specsClassifyMap.get(goodsSpecsId);
                List<String> goodsSpecsMap = map.getOrDefault(classifyName, new ArrayList<>());
                goodsSpecsMap.add(specsName);
                map.put(classifyName, goodsSpecsMap);
            }
            AppOrderDetailsEntity details = new AppOrderDetailsEntity();
            details.setAmount(goodsResponse.getAmount());
            details.setSourceAmount(goodsResponse.getSourceAmount());
            details.setTitle(goodsResponse.getTitle());
            details.setGoodsSpecsClassifyMap(JSON.toJSONString(map));
            details.setDescription(goodsResponse.getDescription());
            details.setImage(goodsResponse.getImage());
            details.setOrderNo(orderNo);
            details.setGoodsId(goodsResponse.getId());
            details.setGoodsSpecs(goodsResponse.getSpecsNameIds());
            details.setGoodsSpecsName(goodsResponse.getSpecsNames());
            details.setNumber(goodsResponse.getNumber());
            details.setStock(goodsResponse.getNumber());
            orderDetailsEntityList.add(details);
        }
        appOrderDetailsRepository.saveBatch(orderDetailsEntityList);
    }


    /**
     * 订单编号查询订单信息
     *
     * @param orderNo 订单编号
     * @return 订单信息
     */
    public CalcOrderResponse selectOrderByNo(String orderNo) {
        AppOrdersEntity appOrders = repository.getByOrderNo(orderNo);
        BusinessException.throwIf(Objects.isNull(appOrders), "订单不存在");
        CalcOrderResponse response = new CalcOrderResponse();
        response.setId(appOrders.getId());
        response.setPayStatus(appOrders.getPayStatus());
        response.setOrderStatus(appOrders.getOrderStatus());
        response.setCreateTime(appOrders.getCreateTime());
        response.setPayTime(appOrders.getPayTime());
        response.setOrderNo(appOrders.getOrderNo());
        response.setAmount(appOrders.getOrderAmount());
        response.setPayAmount(appOrders.getPayAmount());
        response.setRemark(appOrders.getRemark());
        response.setSourceAmount(appOrders.getOrderSourceAmount());
        response.setLogistics(appOrders.getLogistics());
        response.setIntegral(appOrders.getIntegral());
        response.setIntegralAmount(appOrders.getIntegralAmount());
        CalcOrderResponse.AddressResponse addressResponse = new CalcOrderResponse.AddressResponse();
        addressResponse.setName(appOrders.getName());
        addressResponse.setMobile(appOrders.getMobile());
        addressResponse.setProvince(appOrders.getProvince());
        addressResponse.setCity(appOrders.getCity());
        addressResponse.setDistrict(appOrders.getDistrict());
        addressResponse.setAddress(appOrders.getAddress());
        response.setAddress(addressResponse);
        List<GoodsResponse> orderDetailToGoodsResponse = getOrderDetailToGoodsResponse(orderNo);
        response.setGoodsList(orderDetailToGoodsResponse);
        return response;
    }

    private List<GoodsResponse> getOrderDetailToGoodsResponse(final String orderNo) {
        List<AppOrderDetailsEntity> detailsEntityList = appOrderDetailsRepository.listByOrderNo(orderNo);
        List<GoodsResponse> goodsResponseList = new ArrayList<>();
        for (AppOrderDetailsEntity detailsEntity : detailsEntityList) {
            GoodsResponse goodsResponse = new GoodsResponse();
            goodsResponse.setId(detailsEntity.getGoodsId());
            goodsResponse.setOrderDetailId(detailsEntity.getId());
            goodsResponse.setAmount(detailsEntity.getAmount());
            goodsResponse.setSourceAmount(detailsEntity.getSourceAmount());
            goodsResponse.setSpecsNames(detailsEntity.getGoodsSpecsName());
            goodsResponse.setSpecsNameIds(detailsEntity.getGoodsSpecs());
            goodsResponse.setImage(detailsEntity.getImage());
            goodsResponse.setTitle(detailsEntity.getTitle());
            goodsResponse.setDescription(detailsEntity.getDescription());
            goodsResponse.setNumber(detailsEntity.getNumber());
            Map<String, List<String>> specsNamesMap = JSON.parseObject(detailsEntity.getGoodsSpecsClassifyMap(), new TypeReference<Map<String, List<String>>>() {
            });
            goodsResponse.setSpecsNamesMap(specsNamesMap);
            goodsResponseList.add(goodsResponse);
        }
        return goodsResponseList;
    }

    // 分页我的订单列表
    public Page<OrderListResponse> pageOrders(OrderListRequest orderListRequest) {
        Page<AppOrdersEntity> page = orderListRequest.getPage();
        Page<AppOrdersEntity> ordersPageResult = repository.page(
                page,
                Wrappers.<AppOrdersEntity>lambdaQuery()
                        .eq(Objects.nonNull(orderListRequest.getOrderStatus()), AppOrdersEntity::getOrderStatus, orderListRequest.getOrderStatus())
                        .eq(Objects.nonNull(orderListRequest.getPayStatus()), AppOrdersEntity::getPayStatus, orderListRequest.getPayStatus())
                        .eq(AppOrdersEntity::getCreateUser, AuthUtil.getCurrentUserId())
                        .orderByDesc(AppOrdersEntity::getCreateTime)
        );
        Page<OrderListResponse> response = new Page<>();
        response.setTotal(ordersPageResult.getTotal());
        response.setSize(ordersPageResult.getSize());
        response.setCurrent(ordersPageResult.getCurrent());
        List<AppOrdersEntity> records = ordersPageResult.getRecords();
        List<OrderListResponse> orderListResponses = BeanUtil.copyToList(records, OrderListResponse.class);
        response.setRecords(orderListResponses);
        if (CollectionUtils.isNotEmpty(orderListResponses)) {
            List<String> orderNos = orderListResponses.stream().map(OrderListResponse::getOrderNo).collect(Collectors.toList());
            Map<String, List<GoodsResponse>> orderDetailToGoodsResponse = getOrderDetailToGoodsResponse(orderNos);
            for (OrderListResponse orderListResponse : orderListResponses) {
                orderListResponse.setGoodsList(orderDetailToGoodsResponse.get(orderListResponse.getOrderNo()));
            }
        }
        return response;
    }


    private Map<String, List<GoodsResponse>> getOrderDetailToGoodsResponse(final List<String> orderNos) {
        Map<String, List<AppOrderDetailsEntity>> stringListMap = appOrderDetailsRepository.listByOrderNos(orderNos);
        Map<String, List<GoodsResponse>> response = new HashMap<>(stringListMap.size());
        for (Map.Entry<String, List<AppOrderDetailsEntity>> entry : stringListMap.entrySet()) {
            List<GoodsResponse> goodsResponseList = new ArrayList<>();
            for (AppOrderDetailsEntity detailsEntity : entry.getValue()) {
                GoodsResponse goodsResponse = new GoodsResponse();
                goodsResponse.setId(detailsEntity.getGoodsId());
                goodsResponse.setOrderDetailId(detailsEntity.getId());
                goodsResponse.setAmount(detailsEntity.getAmount());
                goodsResponse.setSourceAmount(detailsEntity.getSourceAmount());
                goodsResponse.setSpecsNames(detailsEntity.getGoodsSpecsName());
                goodsResponse.setSpecsNameIds(detailsEntity.getGoodsSpecs());
                goodsResponse.setImage(detailsEntity.getImage());
                goodsResponse.setTitle(detailsEntity.getTitle());
                goodsResponse.setDescription(detailsEntity.getDescription());
                goodsResponse.setNumber(detailsEntity.getNumber());
                goodsResponseList.add(goodsResponse);
            }
            response.put(entry.getKey(), goodsResponseList);
        }

        return response;
    }

    // 取消订单
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(String orderNo, boolean system) {
        AppOrdersEntity appOrders = repository.getByOrderNo(orderNo);
        if (Objects.equals(appOrders.getPayStatus(), PayStatusEnum.TO_PAY.getCode())) {
            appOrders.setPayStatus(PayStatusEnum.CANCEL.getCode());
            appOrders.setCancelType(system ? 2 : 1);
            appOrders.setCancelTime(LocalDateTime.now());
            // 积分返还
            appUserIntegralRecordRepository.addIntegralRecord(
                    appOrders.getCreateUser(),
                    appOrders.getIntegral(),
                    "订单取消-支付订单号: " + appOrders.getOrderNo() + "，积分返还"
            );
            return repository.updateById(appOrders);
        }
        log.error("订单号: {} 取消失败, 订单状态: {}", orderNo, appOrders.getPayStatus());
        return false;
    }
}

