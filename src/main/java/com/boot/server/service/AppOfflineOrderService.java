package com.boot.server.service;

import com.boot.server.mapper.AppOfflineOrderMapper;
import com.boot.server.repository.AppOfflineOrderRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 线下渠道订单表(AppOfflineOrder)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-11 11:10:23
 */
@Service
@RequiredArgsConstructor
public class AppOfflineOrderService {
    private final AppOfflineOrderRepository repository;
    private final AppOfflineOrderMapper mapper;
}

