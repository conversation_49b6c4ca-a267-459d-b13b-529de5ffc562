package com.boot.server.service;

import com.boot.server.mapper.AppPaymentMapper;
import com.boot.server.repository.AppPaymentRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 订单支付表(AppPayment)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 19:01:17
 */
@Service
@RequiredArgsConstructor
public class AppPaymentService {
    private final AppPaymentRepository repository;
    private final AppPaymentMapper mapper;
}

