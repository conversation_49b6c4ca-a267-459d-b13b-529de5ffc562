package com.boot.server.service;

import com.boot.server.mapper.AppDepotAddressMapper;
import com.boot.server.repository.AppDepotAddressRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 发货仓地址表(AppDepotAddress)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-13 11:07:57
 */
@Service
@RequiredArgsConstructor
public class AppDepotAddressService {
    private final AppDepotAddressRepository repository;
    private final AppDepotAddressMapper mapper;
}

