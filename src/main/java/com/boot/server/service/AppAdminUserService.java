package com.boot.server.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.common.util.ServletUtils;
import com.boot.server.config.JwtConfig;
import com.boot.server.dto.request.AdminLoginRequest;
import com.boot.server.entity.AppAdminUserEntity;
import com.boot.server.mapper.AppAdminUserMapper;
import com.boot.server.repository.AppAdminUserRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理用户表(AppAdminUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-17 11:24:33
 */
@Service
@RequiredArgsConstructor
public class AppAdminUserService {
    private final AppAdminUserRepository repository;
    private final AppAdminUserMapper mapper;
    private final JwtConfig jwtConfig;


    /**
     * 管理用户登录
     *
     * @param adminLoginRequest 登录请求
     * @return 用户信息
     */
    public String adminLogin(AdminLoginRequest adminLoginRequest) {
        AppAdminUserEntity adminUser = repository.getOne(
                Wrappers.<AppAdminUserEntity>lambdaQuery()
                        .eq(AppAdminUserEntity::getUsername, adminLoginRequest.getUsername())
        );
        if (adminUser == null) {
            throw new BusinessException("用户不存在");
        }
        if (!StrUtil.equals(adminLoginRequest.getPassword(), adminUser.getPassword())) {
            throw new BusinessException("密码错误");
        }
        Map<String, Object> map = new HashMap<>();
        map.put("userId", adminUser.getId());
        String token = jwtConfig.generateToken(map);
        HttpServletResponse response = ServletUtils.getResponse();
        response.setHeader("Authorization", "Bearer " + token);
        return token;
    }


    /**
     * 获取当前登录用户信息
     *
     * @return 登录用户信息
     */
    public AppAdminUserEntity getCurrentUser() {
        Long currentUserId = AuthUtil.getCurrentUserId();
        return mapper.selectById(currentUserId);
    }
}

