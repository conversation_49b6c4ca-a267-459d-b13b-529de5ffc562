package com.boot.server.service;

import com.boot.server.mapper.AppAutoReportDetailMapper;
import com.boot.server.repository.AppAutoReportDetailRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 自动化报告明细(AppAutoReportDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-18 10:59:51
 */
@Service
@RequiredArgsConstructor
public class AppAutoReportDetailService {
    private final AppAutoReportDetailRepository repository;
    private final AppAutoReportDetailMapper mapper;
}

