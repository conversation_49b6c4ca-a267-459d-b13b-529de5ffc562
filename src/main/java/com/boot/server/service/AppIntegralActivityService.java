package com.boot.server.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.common.util.BeanUtil;
import com.boot.server.dto.ActivityParticipantResponse;
import com.boot.server.dto.AdminIntegralActivityResponse;
import com.boot.server.dto.JoinIntegralActivityResponse;
import com.boot.server.dto.request.ActivityParticipantRequest;
import com.boot.server.dto.request.AdminIntegralActivityRequest;
import com.boot.server.dto.request.CreateIntegralActivityRequest;
import com.boot.server.dto.request.JoinIntegralActivityRequest;
import com.boot.server.dto.request.UpdateIntegralActivityRequest;
import com.boot.server.entity.AppIntegralActivityEntity;
import com.boot.server.mapper.AppIntegralActivityMapper;
import com.boot.server.repository.AppIntegralActivityRepository;
import com.boot.server.repository.AppIntegralActivityUserRepository;
import com.boot.server.repository.AppUserIntegralRecordRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 积分活动(AppIntegralActivity)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-23 13:14:58
 */
@Service
@RequiredArgsConstructor
public class AppIntegralActivityService {
    private final AppIntegralActivityRepository repository;
    private final AppIntegralActivityMapper mapper;
    private final AppIntegralActivityUserRepository appIntegralActivityUserRepository;
    private final AppUserIntegralRecordRepository appUserIntegralRecordRepository;

    /**
     * 分页查询积分活动列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    public Page<AdminIntegralActivityResponse> selectAdminPage(AdminIntegralActivityRequest request) {
        return repository.selectAdminPage(request);
    }

    /**
     * 根据ID查询积分活动详情
     *
     * @param id 积分活动ID
     * @return 积分活动详情
     */
    public AdminIntegralActivityResponse getById(Long id) {
        AppIntegralActivityEntity entity = repository.getById(id);
        BusinessException.throwIfNull(entity, "积分活动不存在");
        return BeanUtil.copyToBean(entity, AdminIntegralActivityResponse.class);
    }

    /**
     * 创建积分活动
     *
     * @param request 创建请求
     * @return 是否成功
     */
    public Boolean create(CreateIntegralActivityRequest request) {
        // 验证兑换码唯一性
        if (repository.existsByActivityNo(request.getActivityNo())) {
            throw new BusinessException("兑换码已存在，请使用其他口令");
        }

        // 验证过期时间不能是过去时间
        if (request.getExprTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("过期时间不能早于当前时间");
        }
        AppIntegralActivityEntity entity = BeanUtil.copyToBean(request, AppIntegralActivityEntity.class);
        entity.setUsedCount(0); // 初始化已使用次数为0
        return repository.save(entity);
    }

    /**
     * 更新积分活动
     *
     * @param request 更新请求
     * @return 是否成功
     */
    public Boolean update(UpdateIntegralActivityRequest request) {
        // 验证积分活动是否存在
        AppIntegralActivityEntity existEntity = repository.getById(request.getId());
        BusinessException.throwIfNull(existEntity, "积分活动不存在");

        // 验证兑换码唯一性（排除当前记录）
        if (repository.existsByActivityNoAndIdNot(request.getActivityNo(), request.getId())) {
            throw new BusinessException("兑换码已存在，请使用其他口令");
        }

        // 验证过期时间不能是过去时间
        if (request.getExprTime().isBefore(LocalDateTime.now())) {
            throw new BusinessException("过期时间不能早于当前时间");
        }

        AppIntegralActivityEntity entity = BeanUtil.copyToBean(request, AppIntegralActivityEntity.class);
        entity.setUsedCount(existEntity.getUsedCount()); // 保持原有的已使用次数

        return repository.updateById(entity);
    }

    /**
     * 删除积分活动（逻辑删除）
     *
     * @param id 积分活动ID
     * @return 是否成功
     */
    public Boolean delete(Long id) {
        // 验证积分活动是否存在
        AppIntegralActivityEntity entity = repository.getById(id);
        BusinessException.throwIfNull(entity, "积分活动不存在");

        return repository.removeById(id);
    }

    /**
     * 用户参加积分活动
     *
     * @param request 参加活动请求
     * @return 参加结果
     */
    @Transactional(rollbackFor = Exception.class)
    public JoinIntegralActivityResponse joinActivity(JoinIntegralActivityRequest request) {
        Long currentUserId = AuthUtil.getCurrentUserId();
        // 1. 根据兑换码查询活动
        AppIntegralActivityEntity activity = repository.getByActivityNo(request.getActivityNo());
        BusinessException.throwIfNull(activity, "兑换码不存在或已失效");
        // 2. 检查活动是否过期
        BusinessException.throwIf(activity.getExprTime().isBefore(LocalDateTime.now()),"兑换码已过期");

        // 3. 检查用户是否已参加过该活动
        BusinessException.throwIf(appIntegralActivityUserRepository.hasUserJoinedActivity(activity.getId(), currentUserId), "您已兑换过积分啦！");

        // 4. 检查活动人数限制
        if (activity.getLimitNum() > 0 && activity.getUsedCount() >= activity.getLimitNum()) {
            throw new BusinessException("活动参与人数已满");
        }

        try {
            // 5. 记录用户参加活动
            boolean recordSuccess = appIntegralActivityUserRepository.recordUserJoinActivity(
                    activity.getId(),
                    currentUserId,
                    activity.getIntegral()
            );
            if (!recordSuccess) {
                throw new BusinessException("活动太火爆了，请稍后重试");
            }
            // 6. 增加用户积分(有效期一个月)
            appUserIntegralRecordRepository.addExprTimeIntegralRecord(
                    currentUserId,
                    activity.getIntegral().longValue(),
                    LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "积分活动"
            );
            // 7. 更新活动已使用次数
            repository.incrementUsedCount(activity.getId());
            return JoinIntegralActivityResponse.success(activity.getIntegral(), activity.getRemark());
        } catch (Exception e) {
            throw new BusinessException("参加活动失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询活动参加人员列表
     *
     * @param request 查询请求
     * @return 分页结果
     */
    public Page<ActivityParticipantResponse> selectActivityParticipants(ActivityParticipantRequest request) {
        // 验证活动是否存在
        AppIntegralActivityEntity activity = repository.getById(request.getActivityId());
        BusinessException.throwIfNull(activity, "积分活动不存在");

        return appIntegralActivityUserRepository.selectActivityParticipants(request);
    }
}

