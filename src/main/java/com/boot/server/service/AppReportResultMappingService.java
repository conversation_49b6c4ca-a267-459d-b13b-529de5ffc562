package com.boot.server.service;

import com.boot.server.mapper.AppReportResultMappingMapper;
import com.boot.server.repository.AppReportResultMappingRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 报告结果映射表(AppReportResultMapping)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-28 12:35:02
 */
@Service
@RequiredArgsConstructor
public class AppReportResultMappingService {
    private final AppReportResultMappingRepository repository;
    private final AppReportResultMappingMapper mapper;
}

