package com.boot.server.service;

import com.boot.server.mapper.AppGoodsSpecsClassifyMapper;
import com.boot.server.repository.AppGoodsSpecsClassifyRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 商品规格分类表(AppGoodsSpecsClassify)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-18 09:50:02
 */
@Service
@RequiredArgsConstructor
public class AppGoodsSpecsClassifyService {
    private final AppGoodsSpecsClassifyRepository repository;
    private final AppGoodsSpecsClassifyMapper mapper;
}

