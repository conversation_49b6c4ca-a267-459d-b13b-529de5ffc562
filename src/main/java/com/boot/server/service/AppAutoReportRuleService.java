package com.boot.server.service;

import com.boot.server.mapper.AppAutoReportRuleMapper;
import com.boot.server.repository.AppAutoReportRuleRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 自动化报告规则(AppAutoReportRule)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-17 13:54:29
 */
@Service
@RequiredArgsConstructor
public class AppAutoReportRuleService {
    private final AppAutoReportRuleRepository repository;
    private final AppAutoReportRuleMapper mapper;
}

