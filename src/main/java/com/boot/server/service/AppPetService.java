package com.boot.server.service;

import com.boot.server.mapper.AppPetMapper;
import com.boot.server.repository.AppPetRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 爱宠车(AppPet)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 13:53:35
 */
@Service
@RequiredArgsConstructor
public class AppPetService {
    private final AppPetRepository repository;
    private final AppPetMapper mapper;
}

