package com.boot.server.service;

import com.boot.server.mapper.AppPetClassifyMapper;
import com.boot.server.repository.AppPetClassifyRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 宠物分类表(AppPetClassify)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-18 09:50:03
 */
@Service
@RequiredArgsConstructor
public class AppPetClassifyService {
    private final AppPetClassifyRepository repository;
    private final AppPetClassifyMapper mapper;
}

