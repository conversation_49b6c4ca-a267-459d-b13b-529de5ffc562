package com.boot.server.service;

import com.boot.server.mapper.AppIntegralActivityUserMapper;
import com.boot.server.repository.AppIntegralActivityUserRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 积分活动参加用户(AppIntegralActivityUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-23 13:14:58
 */
@Service
@RequiredArgsConstructor
public class AppIntegralActivityUserService {
    private final AppIntegralActivityUserRepository repository;
    private final AppIntegralActivityUserMapper mapper;
}

