package com.boot.server.service;

import com.boot.server.mapper.AppAddressMapper;
import com.boot.server.repository.AppAddressRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 地址表(AppAddress)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-09 16:26:12
 */
@Service
@RequiredArgsConstructor
public class AppAddressService {
    private final AppAddressRepository repository;
    private final AppAddressMapper mapper;
}

