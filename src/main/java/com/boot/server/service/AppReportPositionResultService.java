package com.boot.server.service;

import com.boot.server.mapper.AppReportPositionResultMapper;
import com.boot.server.repository.AppReportPositionResultRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 位点检测结果(AppReportPositionResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-22 10:37:11
 */
@Service
@RequiredArgsConstructor
public class AppReportPositionResultService {
    private final AppReportPositionResultRepository repository;
    private final AppReportPositionResultMapper mapper;
}

