package com.boot.server.service;

import com.boot.server.mapper.AppKnowledgesMapper;
import com.boot.server.repository.AppKnowledgesRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 知识列表(AppKnowledges)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-04 17:24:52
 */
@Service
@RequiredArgsConstructor
public class AppKnowledgesService {
    private final AppKnowledgesRepository repository;
    private final AppKnowledgesMapper mapper;
}

