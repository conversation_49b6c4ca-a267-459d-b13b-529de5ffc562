package com.boot.server.service;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.TimedCache;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boot.server.common.util.AppCacheUtil;
import com.boot.server.dto.GoodsSpecsDetailResponse;
import com.boot.server.entity.AppGoodsSpecsDetailEntity;
import com.boot.server.mapper.AppGoodsSpecsDetailMapper;
import com.boot.server.repository.AppGoodsSpecsDetailRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 规格详情表(AppGoodsSpecsDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-22 23:38:23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppGoodsSpecsDetailService {
    private final AppGoodsSpecsDetailRepository repository;
    private final AppGoodsSpecsDetailMapper mapper;
    public final static TimedCache<Integer, List<GoodsSpecsDetailResponse>> CACHE = CacheUtil.newTimedCache(0);

    public List<GoodsSpecsDetailResponse> getSpecsDetailList(Integer specsId) {
        List<GoodsSpecsDetailResponse> goodsSpecsDetailResponses = CACHE.get(specsId);
        if (CollectionUtils.isNotEmpty(goodsSpecsDetailResponses)) {
            log.info("从缓存中获取规格详情数据，specsId: {}", specsId);
            return goodsSpecsDetailResponses;
        }
        // 通过规格ID查询规格详情
        List<AppGoodsSpecsDetailEntity> entityList = repository.list(
                Wrappers.<AppGoodsSpecsDetailEntity>lambdaQuery()
                        .eq(AppGoodsSpecsDetailEntity::getSpecsId, specsId)
        );
        if (CollectionUtils.isNotEmpty(entityList)) {
            Map<String, List<AppGoodsSpecsDetailEntity>> classifyNameGroupMap = entityList.stream().collect(Collectors.groupingBy(AppGoodsSpecsDetailEntity::getClassifyName));
            List<GoodsSpecsDetailResponse> result = new ArrayList<>();
            for (Map.Entry<String, List<AppGoodsSpecsDetailEntity>> entry : classifyNameGroupMap.entrySet()) {
                GoodsSpecsDetailResponse goodsSpecsDetailResponse = new GoodsSpecsDetailResponse();
                goodsSpecsDetailResponse.setClassifyName(entry.getKey());
                List<String> items = entry.getValue().stream().map(AppGoodsSpecsDetailEntity::getItem).collect(Collectors.toList());
                goodsSpecsDetailResponse.setItems(items);
                result.add(goodsSpecsDetailResponse);
            }
            // 将结果存入缓存
            CACHE.put(specsId, result);
            return result;
        }
        return Collections.emptyList();
    }

    public void clearCache() {
        CACHE.clear();
    }
}

