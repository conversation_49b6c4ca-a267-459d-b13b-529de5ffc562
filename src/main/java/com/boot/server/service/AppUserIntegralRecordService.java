package com.boot.server.service;

import com.boot.server.mapper.AppUserIntegralRecordMapper;
import com.boot.server.repository.AppUserIntegralRecordRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 积分明细记录(AppUserIntegralRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-05 18:14:01
 */
@Service
@RequiredArgsConstructor
public class AppUserIntegralRecordService {
    private final AppUserIntegralRecordRepository repository;
    private final AppUserIntegralRecordMapper mapper;
}

