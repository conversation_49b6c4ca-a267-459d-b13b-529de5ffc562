package com.boot.server.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.boot.server.common.dto.PageRequest;
import com.boot.server.common.exception.BusinessException;
import com.boot.server.common.util.AuthUtil;
import com.boot.server.dto.CartListResponse;
import com.boot.server.dto.request.AddCardRequest;
import com.boot.server.entity.AppGoodsEntity;
import com.boot.server.entity.AppGoodsSpecsEntity;
import com.boot.server.entity.AppShoppingCartEntity;
import com.boot.server.mapper.AppShoppingCartMapper;
import com.boot.server.repository.AppGoodsRepository;
import com.boot.server.repository.AppGoodsSpecsRepository;
import com.boot.server.repository.AppShoppingCartRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 购物车(AppShoppingCart)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-05 10:58:29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppShoppingCartService {
    private final AppShoppingCartRepository repository;
    private final AppGoodsSpecsRepository appGoodsSpecsRepository;
    private final AppShoppingCartMapper mapper;
    private final AppGoodsRepository appGoodsRepository;

    @Transactional(rollbackFor = Exception.class)
    public Boolean add(AddCardRequest addCardRequest) {
        AppShoppingCartEntity cart = new AppShoppingCartEntity();
        cart.setGoodsId(addCardRequest.getGoodsId());
        cart.setSpecs(addCardRequest.getSpec());
        cart.setNum(addCardRequest.getQuantity());
        AppGoodsEntity goods = appGoodsRepository.getById(addCardRequest.getGoodsId());
        if (goods == null) {
            throw new BusinessException("商品不存在");
        }
        cart.setGoodsType(goods.getGoodsType());
        return repository.save(cart);
    }

    public Long userCardCount() {
        Long currentUserId = AuthUtil.getCurrentUserId();
        return repository.count(
                Wrappers.<AppShoppingCartEntity>lambdaQuery()
                        .eq(AppShoppingCartEntity::getCreateUser, currentUserId)
        );
    }

    public Page<CartListResponse> page(PageRequest pageRequest) {
        Page<CartListResponse> page = pageRequest.getPage();
        Page<CartListResponse> cartListResponsePage = repository.selectPageUserCart(AuthUtil.getCurrentUserId(), page);
        List<CartListResponse> records = cartListResponsePage.getRecords();
        if (CollectionUtils.isNotEmpty(records)) {
            List<Long> collect = records.stream()
                    .map(row -> StrUtil.split(row.getSpecs(), ","))
                    .flatMap(List::stream)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            Map<Long, String> mapName = appGoodsSpecsRepository.getMapName(collect);
            for (CartListResponse response : records) {
                String specs = response.getSpecs();
                List<Long> specsIds = StrUtil.split(specs, ",").stream().map(Long::valueOf).collect(Collectors.toList());
                List<String> names = new ArrayList<>();
                for (Long specsId : specsIds) {
                    names.add(mapName.get(specsId));
                }
                response.setSpecsName(String.join(",", names));
                // 随心选计算价格
                if (Objects.equals(response.getGoodsType(), 1)) {
                    int size = specsIds.size();
                    // 大于 1 每多一项加response.incrPrice元
                    if (size > 1) {
                        BigDecimal amount = new BigDecimal(size - 1).multiply(response.getIncrPrice())
                                .add(response.getAmount())
                                .setScale(2, RoundingMode.HALF_UP);
                        response.setAmount(amount);
                    }
                }
            }
        }
        return cartListResponsePage;
    }

    /**
     * 减购物车数量
     *
     * @param id 购物车 ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean decr(Long id) {
        Boolean decr = repository.decr(id);
        AppShoppingCartEntity cart = repository.getById(id);
        if (Objects.equals(cart.getNum(), 0)) {
            log.info("删除购物车: {}", id);
            return repository.removeById(id);
        }
        return decr;
    }

    /**
     * 加购物车数量
     *
     * @param id 购物车 ID
     * @return 是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean icer(Long id) {
        return repository.icer(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(List<Long> ids) {
        return repository.remove(
                Wrappers.lambdaQuery(AppShoppingCartEntity.class)
                        .in(AppShoppingCartEntity::getId, ids)
                        .eq(AppShoppingCartEntity::getCreateUser, AuthUtil.getCurrentUserId())
        );
    }

    public Boolean updateCartQuantity(Long id, Long number) {
        int cartQuantity = mapper.updateCartQuantity(id, number, AuthUtil.getCurrentUserId());
        return SqlHelper.retBool(cartQuantity);
    }
}

