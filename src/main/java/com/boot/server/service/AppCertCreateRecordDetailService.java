package com.boot.server.service;

import com.boot.server.mapper.AppCertCreateRecordDetailMapper;
import com.boot.server.repository.AppCertCreateRecordDetailRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 证书创建记录明细表(AppCertCreateRecordDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-23 14:34:54
 */
@Service
@RequiredArgsConstructor
public class AppCertCreateRecordDetailService {
    private final AppCertCreateRecordDetailRepository repository;
    private final AppCertCreateRecordDetailMapper mapper;
}

