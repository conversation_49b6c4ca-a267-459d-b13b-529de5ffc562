package com.boot.server.service;

import com.boot.server.mapper.AppPetReportMapper;
import com.boot.server.repository.AppPetReportRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

/**
 * 试剂盒报告结果(AppPetReport)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-05-14 16:41:58
 */
@Service
@RequiredArgsConstructor
public class AppPetReportService {
    private final AppPetReportRepository repository;
    private final AppPetReportMapper mapper;
}

