<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.server.mapper.SampleMapper">

    <select id="selectSampleByDeviceId" resultType="com.boot.server.dto.SampleResponse">
        SELECT '小猫来了'                                                         as organization,
               s.pet_name                                                         as petNo,
               CASE s.pet_sex WHEN 1 THEN '雄性' when 2 THEN '雌性' ELSE '无' END as petSex,
               sn.device_id                                                       as deviceId,
               s.pet_breed                                                        as petBreed
        FROM `sample` s
                 left join series_number sn on s.series_id = sn.id
        where device_id = #{deviceId}
    </select>
</mapper>

