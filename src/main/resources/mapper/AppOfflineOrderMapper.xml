<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.server.mapper.AppOfflineOrderMapper">
    <select id="selectOfflineOrderPage" resultType="com.boot.server.entity.AppOfflineOrderEntity">
        select *, t2.title as goodsName
        from app_offline_order as t1
        inner join app_goods as t2 on t1.goods_id = t2.id
        <where>
            <if test="request.kitsNo != null and request.kitsNo != ''">
                and t1.kits_no = #{request.kitsNo}
            </if>
            <if test="request.goodsId != null">
                and t1.goods_id = #{request.goodsId}
            </if>
            <if test="request.status != null">
                and t1.status = #{request.status}
            </if>
            and t1.deleted = 0 and t2.deleted = 0
        </where>
    </select>
</mapper>

