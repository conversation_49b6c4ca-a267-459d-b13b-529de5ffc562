<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.server.mapper.AppShoppingCartMapper">

    <select id="selectPageUserCart" resultType="com.boot.server.dto.CartListResponse">
        select t1.id            as id,
               t2.title         as title,
               t2.image         as image,
               t2.description   as description,
               t2.amount        as amount,
               t2.source_amount as source_amount,
               t2.status        as status,
               t1.num           as number,
               t1.specs         as specs,
               t1.create_time   as createTime,
               t2.goods_type    as goodsType,
               t2.incr_price    as incrPrice
        from app_shopping_cart as t1
                 inner join app_goods as t2 on t1.goods_id = t2.id
        where t1.deleted = 0
          and t2.deleted = 0
          and t1.create_user = #{userId}
        order by t1.create_time desc
    </select>

    <delete id="updateNum">
        update app_shopping_cart set num = num + #{value} where id = #{id} and num > 0 and create_user = #{userId}
    </delete>

    <update id="updateCartQuantity">
        update app_shopping_cart set num = #{number} where id = #{id} and num > 0 and create_user = #{userId}
    </update>
</mapper>

