<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.server.mapper.AppOrderDetailsMapper">
    <select id="selectListPayedAndNotUsed" resultType="com.boot.server.dto.OrderDetailResponse">
        select t1.id as id,
               t1.title as title,
               t1.goods_id as goodsId,
               t1.title as title,
               t1.image as image,
               t1.amount as amount,
               t1.stock as stock,
               t1.order_no as orderNo,
               t1.create_time as createTime
        from app_order_details as t1
                 inner join app_orders as t2 on t1.order_no = t2.order_no
        where used = 0
          and t2.pay_status = 1
          and stock >= 1
          and t1.create_user = #{currentUserId} and t2.deleted = 0
    </select>

    <select id="selectOrderBinderList" resultType="com.boot.server.dto.AdminBinderListResponse">
        select t1.id                             as id,
               t2.pet_name                       as petName,
               t2.pet_ru_name                    as petRuName,
               t2.detect_status                  as detectStatus,
               t2.id                             as petId,
               if(t2.gender = 1, '雌性', '雄性') as genderName,
               t1.kits_no                        as kitsNo,
               t2.avatar                         as avatar,
               t3.title                          as varietiesName,
               t1.logistics                      as logistics,
               t1.is_certificate                 as isCertificate,
               t1.is_report                      as isReport,
               t1.cert_images                    as certImages,
               t1.varieties                      as orderVarietiesName
        from app_pet_bind_order as t1
                 inner join app_pet as t2 on t1.pet_id = t2.id
                 left join app_pet_classify as t3 on t2.varieties = t3.id
        where t1.order_detail_id = #{request.orderDetailId}
          and t2.deleted = 0
    </select>

    <select id="selectListByOrderNo" resultType="com.boot.server.entity.AppOrderDetailsEntity">
        select t1.*, t2.goods_type as goodsType
        from app_order_details as t1
                 inner join app_goods as t2 on t1.goods_id = t2.id
        where t1.order_no = #{orderNo}
          and t1.deleted = 0
    </select>
</mapper>

