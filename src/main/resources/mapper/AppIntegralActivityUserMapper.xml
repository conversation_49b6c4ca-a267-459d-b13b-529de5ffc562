<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.server.mapper.AppIntegralActivityUserMapper">

    <!-- 分页查询活动参加人员列表 -->
    <select id="selectActivityParticipants" resultType="com.boot.server.dto.ActivityParticipantResponse">
        SELECT
            au.user_id as userId,
            u.avatar,
            u.nick_name as nickName,
            au.integral,
            au.create_time as joinTime
        FROM app_integral_activity_user au
        LEFT JOIN app_user u ON au.user_id = u.id
        WHERE au.deleted = 0
        AND au.activity_id = #{request.activityId}
        <if test="request.nickName != null and request.nickName != ''">
            AND u.nick_name LIKE CONCAT('%', #{request.nickName}, '%')
        </if>
        ORDER BY au.create_time DESC
    </select>

</mapper>

