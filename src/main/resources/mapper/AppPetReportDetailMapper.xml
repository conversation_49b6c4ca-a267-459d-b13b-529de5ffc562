<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.server.mapper.AppPetReportDetailMapper">

    <select id="selectListByPetId" resultType="com.boot.server.entity.AppPetReportDetailEntity">
        select t2.*
        from app_pet_report as t1
                 inner join app_pet_report_detail as t2 on t1.id = t2.report_id
        where t1.pet_id = #{petId}
    </select>
</mapper>

