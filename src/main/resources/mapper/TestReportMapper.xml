<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boot.server.mapper.TestReportMapper">
    <!--testState: 1等待检测，2检测中，3检测完成，4已取消-->
    <select id="selectByOrderId" resultType="com.boot.server.dto.QueryReportResponse$ReportDataResponse">
        SELECT ro.id                          as orderId,
               ro.id                          as orderNo,
               s.id                           as petNumber,
               s.pet_name                     as petName,
               ''                             as orderProductId,
               ''                             as productId,
               ''                             as productName,
               ''                             as testState,
               ''                             as orderProductId,
               s.id                           as reportNumber,
               s.id                           as sampleNumber,
               sn.device_id                   as deviceId,
               1                              as sampleType,
               date_format(now(), '%Y-%m-%d') as testEndTime,
               0                              as isPackage
        FROM `report_order` ro
                 left join sample s on ro.sample_id = s.id
                 left join series_number sn on s.series_id = sn.id
        WHERE ro.id = #{orderNo}
    </select>
</mapper>

