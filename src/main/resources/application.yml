server:
  port: 8080
spring:
  servlet:
    multipart:
      enabled: true # 是否启用HTTP上传处理
      max-file-size: 10MB # 设置单个文件最大长度
      max-request-size: 10MB # 最大请求文件的大小
      file-size-threshold: 10MB # 当文件达到多少时进行磁盘写入
  profiles:
    active: dev
  jackson:
    time-zone: Asia/Shanghai
# MyBatis-Plus 配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.boot.server.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: AUTO
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
