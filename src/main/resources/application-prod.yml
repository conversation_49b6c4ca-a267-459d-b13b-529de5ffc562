spring:
  datasource:
    dynamic:
      primary: master # 主数据源名称
      strict: false # 是否严格模式，严格模式下如果主库不可用则不使用从库
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***********************************************************************************************************************************************************************************
          username: pet_user
          password: mLt1FAcL
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *********************************************************************************************************************************************************************************
          username: biom_user
          password: 7VYMC74XnV
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 15MB

# 微信小程序配置
wx:
  miniapp:
    appid: wxb2eaa6134dca7907
    secret: 793721b744a73430600ecf91b9958f48

# JWT配置
jwt:
  # JWT密钥
  secret: cuAihCz53DZRjZwbsGcZJ2Ai6At+T142uphtJMsk7iQ=
  # 令牌有效期，单位秒，默认7天
  expiration: 604800

# 文件上传配置
file:
  upload:
    base-path: /home/<USER>/mnt/uploads
    allow-types: jpg,jpeg,png,gif,mp4,pdf,doc,docx,xls,xlsx
    max-size: 10
    access-url-prefix: /api/file
    report-path: report
    # 证书模板
    template: /home/<USER>/mnt/report/
    oss-get-path: https://api.yiangai.cn/open/download?path=
    env: prod/

express:
  base: https://bspgw.sf-express.com
  # 顾客编码
  appId: YH5DM0iX
  appSecret: LCDL6bNsMi6hQTCshoNUP9FDRbNmEmgl
  monthlyCard: 7550205341

wxpay:
  app-id: wxb2eaa6134dca7907
  mch-id: 1488370842
  partner-key: wyssz5nqhl5wah44xnmy2boqmcgcd1oi
  pay-notify: https://api.yiangai.cn/open/app/notify/payNotify


app:
  env: prod
  app-env: release
  buy-integral-rate: 1
  consumption-integral-rate: 10
  first-buy-amount: 99 # 首单金额限制
  thirty-days-share-integral: 300 # 30天分享奖励积分
  invite-integral: 200 # 邀请奖励积分
  invite-reward-count: 10 # 邀请奖励次数
  sign-key: a3e4ab91ca39-1e57-ff07-890f-0d5b7617
  share-code-file-path: ${file.upload.base-path}/share-code
  share-code-file-url: ${file.upload.access-url-prefix}/share-code
  new-user-integral: 1000 # 新用户注册奖励积分


dromara:
  x-file-storage: #文件存储配置
    default-platform: aliyun #默认使用的存储平台
    aliyun-oss:
      - platform: aliyun # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: LTAI5tPAAfBrZsx5iYwPKMKV
        secret-key: ******************************
        end-point: http://oss-cn-qingdao.aliyuncs.com
        bucket-name: pet-report-oss
        base-path: prod/ # 基础路径