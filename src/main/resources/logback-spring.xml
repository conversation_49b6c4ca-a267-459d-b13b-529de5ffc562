<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义日志文件的存储地址 -->
    <property name="LOG_HOME" value="./logs" />
    <!-- 定义日志文件名称 -->
    <property name="APP_NAME" value="pet-server" />
    <!-- 定义日志文件大小 -->
    <property name="MAX_FILE_SIZE" value="100MB" />

    <!-- 控制台日志格式 -->
    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n" />

    <!-- 文件日志格式 -->
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} [%X{requestId}] - %msg%n" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 异步日志配置 -->
    <appender name="ASYNC_CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="CONSOLE" />
    </appender>

    <!-- 所有日志输出 -->
    <appender name="FILE_ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}-all.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-all.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxHistory>15</maxHistory>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 错误日志输出 -->
    <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxHistory>15</maxHistory>
            <maxFileSize>${MAX_FILE_SIZE}</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 异步文件日志配置 -->
    <appender name="ASYNC_FILE_ALL" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE_ALL" />
    </appender>

    <appender name="ASYNC_FILE_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>512</queueSize>
        <appender-ref ref="FILE_ERROR" />
    </appender>

    <!-- 开发环境 -->
    <springProfile name="dev">
        <logger name="com.boot.server" level="DEBUG" additivity="false">
            <appender-ref ref="ASYNC_CONSOLE" />
            <appender-ref ref="ASYNC_FILE_ALL" />
            <appender-ref ref="ASYNC_FILE_ERROR" />
        </logger>
        <root level="INFO">
            <appender-ref ref="ASYNC_CONSOLE" />
            <appender-ref ref="ASYNC_FILE_ALL" />
            <appender-ref ref="ASYNC_FILE_ERROR" />
        </root>
    </springProfile>

    <!-- 开发环境 -->
    <springProfile name="win">
        <logger name="com.boot.server" level="DEBUG" additivity="false">
            <appender-ref ref="ASYNC_CONSOLE" />
            <appender-ref ref="ASYNC_FILE_ALL" />
            <appender-ref ref="ASYNC_FILE_ERROR" />
        </logger>
        <root level="INFO">
            <appender-ref ref="ASYNC_CONSOLE" />
            <appender-ref ref="ASYNC_FILE_ALL" />
            <appender-ref ref="ASYNC_FILE_ERROR" />
        </root>
    </springProfile>

    <!-- 测试环境 -->
    <springProfile name="uat">
        <logger name="com.boot.server" level="INFO" additivity="false">
            <appender-ref ref="ASYNC_CONSOLE" />
            <appender-ref ref="ASYNC_FILE_ALL" />
            <appender-ref ref="ASYNC_FILE_ERROR" />
        </logger>
        <root level="INFO">
            <appender-ref ref="ASYNC_CONSOLE" />
            <appender-ref ref="ASYNC_FILE_ALL" />
            <appender-ref ref="ASYNC_FILE_ERROR" />
        </root>
    </springProfile>

    <!-- 生产环境 -->
    <springProfile name="prod">
        <logger name="com.boot.server" level="INFO" additivity="false">
            <appender-ref ref="ASYNC_CONSOLE" />
            <appender-ref ref="ASYNC_FILE_ALL" />
            <appender-ref ref="ASYNC_FILE_ERROR" />
        </logger>
        <root level="INFO">
            <appender-ref ref="ASYNC_CONSOLE" />
            <appender-ref ref="ASYNC_FILE_ALL" />
            <appender-ref ref="ASYNC_FILE_ERROR" />
        </root>
    </springProfile>
</configuration>
