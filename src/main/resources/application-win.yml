spring:
  datasource:
    dynamic:
      primary: master # 主数据源名称
      strict: false # 是否严格模式，严格模式下如果主库不可用则不使用从库
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: *************************************************************************************************************************************************
          username: root
          password: root
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ****************************************************************************************************************************************************
          username: biom_user
          password: 7VYMC74XnV
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 15MB

# 微信小程序配置
wx:
  miniapp:
    appid: wxb2eaa6134dca7907
    secret: 793721b744a73430600ecf91b9958f48

# JWT配置
jwt:
  # JWT密钥
  secret: vOqA5onXqIBxDSyFzuJNwy7KhALTTBZC
  # 令牌有效期，单位秒，默认7天
  expiration: 604800

# 文件上传配置
file:
  upload:
    base-path: D:\IdeaProject\pet-server\uploads
    allow-types: jpg,jpeg,png,gif,mp4,pdf,doc,docx,xls,xlsx
    max-size: 10
    access-url-prefix: /api/file
    report-path: report
    # 证书模板
    template: D:\IdeaProject\pet-server\report\
    oss-get-path: http://localhost:8080/open/download?path=
    env: dev/


express:
  base: https://sfapi-sbox.sf-express.com
  # 顾客编码
  appId: YYRCQTNP
  appSecret: ********************************
  monthlyCard: 7551234567

wxpay:
  app-id: wxb2eaa6134dca7907
  mch-id: 1488370842
  partner-key: ********************************
  pay-notify: http://q4fb968e.natappfree.cc/open/app/notify/payNotify
  order-no-prefix: D

app:
  buy-integral-rate: 1
  consumption-integral-rate: 10
  first-buy-amount: 99 # 首单金额限制
  thirty-days-share-integral: 300 # 30天分享奖励积分
  invite-integral: 200 # 邀请奖励积分
  invite-reward-count: 10 # 邀请奖励次数
  sign-key: a3e4ab91ca39-1e57-ff07-890f-0d5b7617
  share-code-file-path: ${file.upload.base-path}/share-code
  share-code-file-url: ${file.upload.access-url-prefix}/share-code
  new-user-integral: 1000 # 新用户注册奖励积分

dromara:
  x-file-storage: #文件存储配置
    default-platform: aliyun #默认使用的存储平台
    aliyun-oss:
      - platform: aliyun # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: LTAI5tPAAfBrZsx5iYwPKMKV
        secret-key: ******************************
        end-point: http://oss-cn-qingdao.aliyuncs.com
        bucket-name: pet-report-oss
        base-path: dev/ # 基础路径
