DROP TABLE IF EXISTS app_report_cat_classify_mapping;
CREATE TABLE app_report_cat_classify_mapping
(
    id            int          NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    classify_id   INT          NULL                     DEFAULT NULL COMMENT '宠物分类ID',
    classify_name VA<PERSON>HAR(255) NULL                     DEFAULT NULL COMMENT '宠物分类中文名',
    report_name   VARCHAR(255) NULL                     DEFAULT NULL COMMENT '指标名称',
    deleted       tinyint(1)                            DEFAULT '0' COMMENT '是否删除',
    create_user   bigint                                DEFAULT NULL COMMENT '创建者',
    update_user   bigint                                DEFAULT NULL COMMENT '更新者',
    create_time   datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time   datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark        varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 26
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='品种套餐指标映射';
