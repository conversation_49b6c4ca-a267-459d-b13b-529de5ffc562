alter table app_pet
    add pet_no varchar(100) null comment '宠物编号全局唯一' after id;

DROP TABLE IF EXISTS `app_offline_order`;
CREATE TABLE `app_offline_order`
(
    `id`          int          NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `kits_no`     varchar(255) NOT NULL COMMENT '试剂盒编号',
    `goods_id`    int          NOT NULL COMMENT '对应商品ID',
    `status`      tinyint(1)   NOT NULL                 DEFAULT 0 COMMENT '是否使用',
    `deleted`     tinyint(1)                            DEFAULT '0' COMMENT '是否删除',
    `create_user` bigint                                DEFAULT NULL COMMENT '创建者',
    `update_user` bigint                                DEFAULT NULL COMMENT '更新者',
    `create_time` datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark`      varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='线下渠道订单表';


alter table app_pet_report
    modify order_detail_id int null default 0 comment '订单详情ID';


alter table app_offline_order
    add `goods_specs` varchar(255) NOT NULL COMMENT '规格ID多个使用英文逗号隔开' after status;


alter table app_offline_order
    add `goods_specs_name` varchar(1000) NOT NULL COMMENT '规格中文名多个使用英文逗号隔开' after goods_specs;


update app_pet
set pet_no = CONCAT('SZ', LPAD(id, 5, '0'))