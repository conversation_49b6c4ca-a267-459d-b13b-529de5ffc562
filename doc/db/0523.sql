DROP TABLE IF EXISTS `app_cert_create_record`;
CREATE TABLE `app_cert_create_record`
(
    `id`          int          NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `images`      VARCHAR(500) NOT NULL COMMENT '证书存放路径',
    `pet_id`      INT          NOT NULL COMMENT '宠物ID',
    `deleted`     tinyint(1)                            DEFAULT 0 COMMENT '是否删除',
    `create_user` bigint                                DEFAULT NULL COMMENT '创建者',
    `update_user` bigint                                DEFAULT NULL COMMENT '更新者',
    `create_time` datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark`      varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='证书创建记录表';


DROP TABLE IF EXISTS `app_cert_create_record_detail`;
CREATE TABLE `app_cert_create_record_detail`
(
    `id`          int          NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `record_id`   INT          NOT NULL COMMENT '记录ID',
    `item`        VARCHAR(255) NOT NULL COMMENT '检测项目',
    `position`    VARCHAR(255) NOT NULL COMMENT '位点名称',
    `result`      VARCHAR(255) NOT NULL COMMENT '突变情况',
    `deleted`     tinyint(1)                            DEFAULT 0 COMMENT '是否删除',
    `create_user` bigint                                DEFAULT NULL COMMENT '创建者',
    `update_user` bigint                                DEFAULT NULL COMMENT '更新者',
    `create_time` datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark`      varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='证书创建记录明细表';
