DROP TABLE IF EXISTS `app_goods_specs_detail`;
CREATE TABLE `app_goods_specs_detail`
(
    `id`            int          NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `specs_id`      int          NULL                     DEFAULT 0 COMMENT '规格ID',
    `classify_name` VA<PERSON>HA<PERSON>(255) NULL COMMENT '分类',
    `item`          VARCHAR(255) NULL COMMENT '项目',
    `deleted`       tinyint(1)                            DEFAULT '0' COMMENT '是否删除',
    `create_user`   bigint                                DEFAULT NULL COMMENT '创建者',
    `update_user`   bigint                                DEFAULT NULL COMMENT '更新者',
    `create_time`   datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark`        varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 3
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='规格详情表';
