DROP TABLE IF EXISTS app_auto_report_rule;
CREATE TABLE app_auto_report_rule
(
    id             int          NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    group_name     varchar(255) NOT NULL COMMENT '分组',
    item           varchar(255) NOT NULL COMMENT '项目',
    result         varchar(255) NOT NULL COMMENT '基因型',
    interpretation text         NOT NULL COMMENT '解读',
    deleted        tinyint(1)                            DEFAULT '0' COMMENT '是否删除',
    create_user    bigint                                DEFAULT NULL COMMENT '创建者',
    update_user    bigint                                DEFAULT NULL COMMENT '更新者',
    create_time    datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time    datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark         varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 26
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='自动化报告规则';


DROP TABLE IF EXISTS app_auto_report_task;
CREATE TABLE app_auto_report_task
(
    id           int          NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    kits_no      varchar(255) NOT NULL COMMENT '试剂盒编号/检测编号',
    organization varchar(255) NULL COMMENT '机构名称',
    pet_no       varchar(255) NULL COMMENT '宠物编号',
    pet_sex      varchar(255) NULL COMMENT '宠物性别',
    pet_breed    varchar(255) NULL COMMENT '宠物品种',
    report_json  json         NOT NULL COMMENT '报告JSON',
    status       tinyint(1)                            DEFAULT '0' COMMENT '任务状态 0 未处理 1处理中 2处理完成 3处理失败',
    deleted      tinyint(1)                            DEFAULT '0' COMMENT '是否删除',
    create_user  bigint                                DEFAULT NULL COMMENT '创建者',
    update_user  bigint                                DEFAULT NULL COMMENT '更新者',
    create_time  datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time  datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark       varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 26
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='自动化报告任务';
