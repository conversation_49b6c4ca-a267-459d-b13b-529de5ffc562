alter table app_user
    add share_uid varchar(100) null comment '分享码' after id;

alter table app_user
    add share_code_file varchar(255) null comment '分享码路径' after share_uid;

alter table app_user
    add invite_share_code varchar(255) null comment '邀请人分享码' after share_uid;

update app_user
set share_uid = replace(uuid(), '-', '');



DROP TABLE IF EXISTS app_integral_activity;
CREATE TABLE app_integral_activity
(
    id          int          NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    activity_no varchar(255) NOT NULL COMMENT '活动编号',
    integral    int          NOT NULL COMMENT '奖励积分',
    expr_time   datetime     NULL COMMENT '过期时间',
    limit_num   int          NOT NULL                 DEFAULT 0 COMMENT '限制人数0不限制',
    used_count  int          NOT NULL                 DEFAULT 0 COMMENT '已使用次数',
    deleted     tinyint(1)                            DEFAULT '0' COMMENT '是否删除',
    create_user bigint                                DEFAULT NULL COMMENT '创建者',
    update_user bigint                                DEFAULT NULL COMMENT '更新者',
    create_time datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time datetime     NOT NULL                 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark      varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='积分活动';


DROP TABLE IF EXISTS app_integral_activity_user;
CREATE TABLE app_integral_activity_user
(
    id          int      NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    activity_id int      NOT NULL COMMENT '活动ID',
    user_id     int      NOT NULL COMMENT '用户ID',
    integral    int      NOT NULL COMMENT '奖励积分',
    deleted     tinyint(1)                            DEFAULT '0' COMMENT '是否删除',
    create_user bigint                                DEFAULT NULL COMMENT '创建者',
    update_user bigint                                DEFAULT NULL COMMENT '更新者',
    create_time datetime NOT NULL                     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time datetime NOT NULL                     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark      varchar(500) COLLATE utf8mb4_0900_bin DEFAULT '' COMMENT '备注信息',
    PRIMARY KEY (id) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_0900_bin
  ROW_FORMAT = DYNAMIC COMMENT ='积分活动参加用户';



alter table app_report_mapping
    add specs_id int null comment '规格ID' after goods_id;

alter table app_user_integral_record
    add expr_time datetime null comment '过期时间' after op_type;

alter table app_banners
    modify link_type tinyint default 0 null comment '0 公众号 1 page页 2tab页';