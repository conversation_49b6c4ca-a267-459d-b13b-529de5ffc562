#!/bin/bash

# 应用名称
APP_NAME="pet-server"
# JAR包名称
JAR_NAME="pet-server.jar"
# 日志文件
LOG_FILE="./logs/${APP_NAME}.log"

# 检查进程状态
status() {
    # 使用精确的jar包名称匹配
    pid=$(ps -ef | grep "${JAR_NAME}" | grep -v grep | awk '{print $2}')
    if [ -z "$pid" ]; then
        echo "${APP_NAME} is not running"
    else
        echo "${APP_NAME} is running, PID=${pid}"
    fi
}

# 停止应用
stop() {
    pid=$(ps -ef | grep "${JAR_NAME}" | grep -v grep | awk '{print $2}')
    if [ -z "$pid" ]; then
        echo "${APP_NAME} is not running"
    else
        echo "Stopping ${APP_NAME}..."
        kill $pid
        sleep 2
        # 检查是否还在运行
        if ps -p $pid > /dev/null; then
            echo "Force stopping ${APP_NAME}..."
            kill -9 $pid
        fi
        echo "${APP_NAME} stopped"
    fi
}

# 启动应用
start() {
    pid=$(ps -ef | grep "${JAR_NAME}" | grep -v grep | awk '{print $2}')
    if [ -n "$pid" ]; then
        echo "${APP_NAME} is already running, PID=${pid}"
        exit 1
    fi
    
    echo "Starting ${APP_NAME}..."
    nohup java -jar ${JAR_NAME} > ${LOG_FILE} 2>&1 &
    
    # 等待进程启动
    sleep 2
    pid=$(ps -ef | grep "${JAR_NAME}" | grep -v grep | awk '{print $2}')
    if [ -n "$pid" ]; then
        echo "${APP_NAME} started, PID=${pid}"
    else
        echo "${APP_NAME} failed to start"
        exit 1
    fi
}

# 重启应用
restart() {
    stop
    sleep 2
    start
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    status)
        status
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac

exit 0 